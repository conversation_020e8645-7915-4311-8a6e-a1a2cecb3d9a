import { BatchStatus } from '@/utils/types'
import { AlertTriangle, CheckCircle, Clock, DollarSign, Sprout, Warehouse, Wheat } from 'lucide-react'

export const getStatusConfig = (status: BatchStatus) => {
  const configs = {
    growing: {
      label: 'Growing',
      icon: Sprout,
      color: '#4CAF50',
      bgColor: 'rgba(76, 175, 80, 0.1)',
    },
    harvested: {
      label: 'Harvested',
      icon: Wheat,
      color: '#FFC107',
      bgColor: 'rgba(255, 193, 7, 0.1)',
    },
    storage: {
      label: 'In Storage',
      icon: Warehouse,
      color: '#2196F3',
      bgColor: 'rgba(33, 150, 243, 0.1)',
    },
    sold: {
      label: 'Sold',
      icon: DollarSign,
      color: '#FF6B00',
      bgColor: 'rgba(255, 107, 0, 0.1)',
    },
    ready: {
      label: 'Ready',
      icon: CheckCircle,
      color: '#4CAF50',
      bgColor: 'rgba(76, 175, 80, 0.1)',
    },
    in_progress: {
      label: 'In Progress',
      icon: Clock,
      color: '#9C27B0',
      bgColor: 'rgba(156, 39, 176, 0.1)',
    },
    damaged: {
      label: 'Damaged',
      icon: AlertTriangle,
      color: '#F44336',
      bgColor: 'rgba(244, 67, 54, 0.1)',
    },
  }

  return configs[status]
}

export const getStatusOptions = () => {
  return [
    { value: 'storage', label: 'In Storage' },
    { value: 'ready', label: 'Ready' },
  ]
}
