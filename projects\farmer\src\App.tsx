import { Toaster as Sonner } from '@/components/ui/sonner'
import { TooltipProvider } from '@/components/ui/tooltip'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { SupportedWallet, WalletId, WalletManager, WalletProvider } from '@txnlab/use-wallet-react'
import { useEffect } from 'react'
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom'
import ProtectedRoute from './components/auth/ProtectedRoute'
import Toaster from './components/ui/toaster'
import ActivateAccount from './pages/ActivateAccount'
import CryptoTransfer from './pages/CryptoTransfer'
import DashboardLayout from './pages/Dashboard'
import ForgotPassword from './pages/ForgotPassword'
import { KCTToKTTConversion } from './pages/KCTToKTTConversion'
import { KTTToKCTConversion } from './pages/KTTToKCTConversion'
import Login from './pages/Login'
import NotFound from './pages/NotFound'
import { OnboardingFlow } from './pages/OnboardingFlow'
import PaymentInterface from './pages/PaymentInterface'
import Register from './pages/Register'
import ResetPassword from './pages/ResetPassword'
import { TokenBurn } from './pages/TokenBurn'
import TransactionHistory from './pages/TransactionHistory'
import { useAuthStore } from './stores/authStore'
import { getAlgodConfigFromViteEnvironment, getKmdConfigFromViteEnvironment } from './utils/network/getAlgoClientConfigs'
import { VITE_ALGOD_NETWORK } from './utils/variable'
// import AssetOptIn from './pages/test'
const queryClient = new QueryClient()

let supportedWallets: SupportedWallet[]
if (VITE_ALGOD_NETWORK === '') {
  const kmdConfig = getKmdConfigFromViteEnvironment()
  supportedWallets = [
    {
      id: WalletId.KMD,
      options: {
        baseServer: kmdConfig.server,
        token: String(kmdConfig.token),
        port: String(kmdConfig.port),
      },
    },
  ]
} else {
  supportedWallets = [
    { id: WalletId.DEFLY },
    { id: WalletId.PERA },
    { id: WalletId.EXODUS },
    // If you are interested in WalletConnect v2 provider
    // refer to https://github.com/TxnLab/use-wallet for detailed integration instructions
  ]
}

// AppWrapper component to handle auth initialization
const AppWrapper = () => {
  const { checkAuth } = useAuthStore()

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  return <AppRoutes />
}

// Routes component
const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Navigate to="/login" replace />} />

      {/* Public routes */}
      <Route
        path="/login"
        element={
          <ProtectedRoute requireAuth={false}>
            <Login />
          </ProtectedRoute>
        }
      />
      <Route
        path="/register"
        element={
          <ProtectedRoute requireAuth={false}>
            <Register />
          </ProtectedRoute>
        }
      />
      <Route
        path="/forgot-password"
        element={
          <ProtectedRoute requireAuth={false}>
            <ForgotPassword />
          </ProtectedRoute>
        }
      />
      <Route
        path="/activate-account/:uid/:token"
        element={
          <ProtectedRoute requireAuth={false}>
            <ActivateAccount />
          </ProtectedRoute>
        }
      />
      <Route
        path="/reset-password/:uid/:token"
        element={
          <ProtectedRoute requireAuth={false}>
            <ResetPassword />
          </ProtectedRoute>
        }
      />

      {/* Protected routes */}

      {/* Onboarding route - requires auth but not account_address */}
      <Route
        path="/onboarding"
        element={
          <ProtectedRoute requireOnboarding={false}>
            <OnboardingFlow />
          </ProtectedRoute>
        }
      />

      {/* Fully protected routes - require auth AND account_address */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute requireOnboarding={true}>
            <DashboardLayout />
          </ProtectedRoute>
        }
      />
      <Route
        path="/buy"
        element={
          <ProtectedRoute requireOnboarding={true}>
            <PaymentInterface />
          </ProtectedRoute>
        }
      />
      <Route
        path="/burn"
        element={
          <ProtectedRoute requireOnboarding={true}>
            <TokenBurn />
          </ProtectedRoute>
        }
      />
      <Route
        path="/convert"
        element={
          <ProtectedRoute requireOnboarding={true}>
            <KTTToKCTConversion />
          </ProtectedRoute>
        }
      />
      <Route
        path="/sell"
        element={
          <ProtectedRoute requireOnboarding={true}>
            <KCTToKTTConversion />
          </ProtectedRoute>
        }
      />
      <Route
        path="/transactions"
        element={
          <ProtectedRoute requireOnboarding={true}>
            <TransactionHistory />
          </ProtectedRoute>
        }
      />
      <Route
        path="/transfer"
        element={
          <ProtectedRoute requireOnboarding={true}>
            <CryptoTransfer />
          </ProtectedRoute>
        }
      />

      <Route path="*" element={<NotFound />} />
    </Routes>
  )
}

/**
 * Sets up the main application component with routing, wallet management, and UI providers.
 *
 * Wraps the app with providers for React Query, tooltips, notifications, wallet context, and routing, and defines all application routes.
 */
export default function App() {
  const algodConfig = getAlgodConfigFromViteEnvironment()

  const walletManager = new WalletManager({
    wallets: supportedWallets,
    defaultNetwork: algodConfig.network,
    networks: {
      [algodConfig.network]: {
        algod: {
          baseServer: algodConfig.server,
          port: algodConfig.port,
          token: String(algodConfig.token),
        },
      },
    },
    options: {
      resetNetwork: true,
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <WalletProvider manager={walletManager}>
            <AppWrapper />
          </WalletProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  )
}
