from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Coordinates,
    Location,
    GrowthPeriod,
    Fertilizer,
    Crops,
    CropTransfer,
)
from django.db.models import Manager, QuerySet
from user.serializers import UserSerializer

User = get_user_model()


# ------------------------
# Simple/Nested Serializers
# ------------------------


class CoordinatesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Coordinates
        fields = ["lat", "long"]


class LocationSerializer(serializers.ModelSerializer):
    coordinates = CoordinatesSerializer()

    class Meta:
        model = Location
        fields = ["address", "coordinates"]


class GrowthPeriodSerializer(serializers.ModelSerializer):
    class Meta:
        model = GrowthPeriod
        fields = ["start_date", "harvest_date"]


class FertilizerNameListField(serializers.Field):
    """
    • input : list of strings  →  validated list of strings
    • output: list of strings  (manager → .values_list)
    """

    def to_internal_value(self, data):
        """
        Validates and converts input data to a list of fertilizer names.

        Raises a ValidationError if the input is not a list of strings.
        """
        if not isinstance(data, list):
            raise serializers.ValidationError("Expected a list of fertilizer names.")
        if not all(isinstance(item, str) for item in data):
            raise serializers.ValidationError("Each fertilizer must be a string.")
        return data  # we keep plain list of names

    def to_representation(self, value):
        # value is ManyRelatedManager or QuerySet (thanks to prefetch_related)
        """
        Converts a related manager, queryset, or iterable of fertilizers to a list of fertilizer names.

        Args:
            value: A ManyRelatedManager, QuerySet, or iterable representing fertilizers.

        Returns:
            A list of fertilizer names as strings.
        """
        if isinstance(value, (Manager, QuerySet)):
            return list(value.values_list("name", flat=True))
        # already a list / iterable → just return it
        return list(value)


# ------------------------
# Writable Nested Crops Serializer
# ------------------------


class CropsSerializer(serializers.ModelSerializer):
    location = LocationSerializer()
    growth_period = GrowthPeriodSerializer()
    # ← change here: simple list of strings, not SlugRelatedField
    fertilizers_used = FertilizerNameListField()
    crop_id = serializers.IntegerField()

    class Meta:
        model = Crops
        fields = [
            "crop_id",
            "crop_grade",
            "quantity",
            "location",
            "growth_period",
            "soil_type",
            "irrigation_type",
            "fertilizers_used",
            "certification",
            "unit",
            "created_at",
        ]

    def create(self, validated_data):
        # 1. extract nested structures
        """
        Creates a Crops instance along with its nested related objects.

        Extracts and creates related Coordinates, Location, and GrowthPeriod objects from the validated data, associates fertilizers by name (creating them if necessary), and returns the newly created Crops instance with all relations established.
        """
        loc_data = validated_data.pop("location")
        coords_data = loc_data.pop("coordinates")
        gp_data = validated_data.pop("growth_period")
        fert_names = validated_data.pop("fertilizers_used", [])

        # 2. build Coordinates → Location
        coords = Coordinates.objects.create(**coords_data)
        location = Location.objects.create(coordinates=coords, **loc_data)

        # 3. build GrowthPeriod
        growth_period = GrowthPeriod.objects.create(**gp_data)

        # 4. create the Crop
        crop = Crops.objects.create(
            location=location, growth_period=growth_period, **validated_data
        )

        # 5. get_or_create fertilizers and assign
        fert_objs = []
        for name in fert_names:
            obj, _ = Fertilizer.objects.get_or_create(name=name)
            fert_objs.append(obj)
        crop.fertilizers_used.set(fert_objs)

        return crop


# ------------------------
# Main CropTransfer Serializer
# ------------------------


class CropTransferSerializer(serializers.ModelSerializer):
    transaction_id = serializers.CharField()
    crop = CropsSerializer()

    from_user = serializers.SlugRelatedField(
        slug_field="account_address", queryset=User.objects.all()
    )
    to_user = serializers.SlugRelatedField(
        slug_field="account_address", queryset=User.objects.all()
    )

    class Meta:
        model = CropTransfer
        fields = [
            "transaction_id",
            "cost",
            "crop",
            "from_user",
            "to_user",
            "timestamp",
            "status",
        ]

    def create(self, validated_data):
        """
        Creates a CropTransfer instance, reusing an existing crop by crop_id if available or creating a new crop if not.

        If a crop with the specified crop_id exists, it is linked to the transfer; otherwise, a new crop is created using the nested serializer before creating the transfer.
        """
        crop_data = validated_data.pop("crop")
        crop_id = crop_data.get("crop_id")

        # Try to fetch an existing crop first
        crop = None
        if crop_id is not None:
            crop = Crops.objects.filter(crop_id=crop_id).first()

        # Not found? → create it with the nested serializer
        if crop is None:
            crop = CropsSerializer().create(crop_data)

        # Now create the transfer that points to the resolved crop
        transfer = CropTransfer.objects.create(crop=crop, **validated_data)
        return transfer


class CropTransferHistorySerializer(serializers.ModelSerializer):
    transaction_id = serializers.CharField()
    crop = serializers.IntegerField(source="crop.crop_id")
    from_user = UserSerializer(read_only=True)
    to_user = UserSerializer(read_only=True)

    class Meta:
        model = CropTransfer
        fields = [
            "transaction_id",
            "cost",
            "crop",
            "from_user",
            "to_user",
            "timestamp",
            "status",
        ]
