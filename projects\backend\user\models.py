from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from .managers import UserManager

class RoleChoices(models.TextChoices):
    FARMER = 'farmer', 'Farmer'
    TRADER = 'trader', 'Trader'
    MANUFACTURER = 'manufacturer', 'Manufacturer'
    BANK = 'bank', 'Bank'
    ADMIN = 'admin', 'Admin'

class User(AbstractUser):
    username = None
    first_name = None
    last_name = None

    name = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    is_active = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now, db_column="account_creation_date")
    last_login = models.DateTimeField(null=True, blank=True)
    password = models.CharField(max_length=255)
    role = models.CharField(
        max_length=50,
        default=RoleChoices.FARMER,
        choices=RoleChoices.choices,
    )
    account_address = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    opt_in = models.BooleanField(default=False)
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    objects = UserManager()

    class Meta:
        verbose_name = "User"
        verbose_name_plural = "Users"
        indexes = [models.Index(fields=['email']),]

    def __str__(self):
        return self.email
