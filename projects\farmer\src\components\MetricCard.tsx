import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface MetricCardProps {
  title: string
  value: string
  change?: string
  isPositive?: boolean
  className?: string
}

export const MetricCard = ({ title, value, change, isPositive, className }: MetricCardProps) => {
  return (
    <Card className={cn('p-6 bg-secondary-bg border-none transition-transform duration-300 hover:scale-105', className)}>
      <h3 className="text-secondary-text text-sm font-medium mb-2">{title}</h3>
      <p className="text-primary-text text-2xl font-bold mb-2">{value}</p>
      {change && (
        <p className={cn('text-sm font-medium', isPositive ? 'text-primary-accent' : 'text-red-500')}>
          {isPositive ? '+' : '-'}
          {change}
        </p>
      )}
    </Card>
  )
}
