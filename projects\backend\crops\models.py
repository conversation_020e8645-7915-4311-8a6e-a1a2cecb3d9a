from django.db import models
from django.core.validators import MinV<PERSON>ueValidator
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

User = get_user_model()


# -------------------------------
# Coordinates & Location
# -------------------------------
class Coordinates(models.Model):
    lat = models.FloatField()
    long = models.FloatField()

    def __str__(self) -> str:
        """
        Returns the coordinates as a string in the format "(latitude, longitude)".
        """
        return f"({self.lat}, {self.long})"

    class Meta:
        verbose_name = "Coordinate"
        verbose_name_plural = "Coordinates"


class Location(models.Model):
    address = models.CharField(max_length=255)
    coordinates = models.OneToOneField(
        Coordinates, on_delete=models.CASCADE, related_name="location"
    )

    def __str__(self) -> str:
        """
        Returns the address as the string representation of the location.
        """
        return self.address


# -------------------------------
# Growth Period & Fertilizer
# -------------------------------
class GrowthPeriod(models.Model):
    start_date = models.DateField()
    harvest_date = models.DateField()

    def __str__(self) -> str:
        """
        Returns a string representation of the growth period as a date range.
        """
        return f"{self.start_date} → {self.harvest_date}"

    def clean(self) -> None:
        """
        Validates that the harvest date is after the start date.

        Raises:
            ValidationError: If the harvest date is not later than the start date.
        """
        if self.harvest_date <= self.start_date:
            raise ValidationError("Harvest date must be after start date")

    @property
    def duration(self) -> int:
        """
        Returns the number of days between the start and harvest dates of the growth period.
        """
        return (self.harvest_date - self.start_date).days


class Fertilizer(models.Model):
    name = models.CharField(max_length=100, unique=True)

    def __str__(self) -> str:
        """
        Returns the name of the fertilizer as its string representation.
        """
        return self.name


# -------------------------------
# Crop (registered by Farmer)
# -------------------------------
class Crops(models.Model):
    GRADE_CHOICES = [
        ("A", "Grade A"),
        ("B", "Grade B"),
        ("C", "Grade C"),
    ]

    UNITS_CHOICES = [
        ("kg", "kg"),
        ("tons", "tons"),
    ]

    crop_id = models.BigAutoField(primary_key=True)  # auto-increment
    crop_grade = models.CharField(max_length=1, choices=GRADE_CHOICES)
    quantity = models.DecimalField(
        max_digits=16, decimal_places=2, validators=[MinValueValidator(0)], default=0
    )
    unit = models.CharField(max_length=4, default="tons", choices=UNITS_CHOICES)
    location = models.OneToOneField(
        Location, on_delete=models.CASCADE, related_name="crop"
    )
    growth_period = models.OneToOneField(
        GrowthPeriod, on_delete=models.CASCADE, related_name="crop"
    )

    soil_type = models.CharField(max_length=100)
    irrigation_type = models.CharField(max_length=100)

    fertilizers_used = models.ManyToManyField(
        Fertilizer, related_name="crops", blank=True
    )

    certification = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # ---------------------------
    # Convenience helpers
    # ---------------------------
    def __str__(self) -> str:
        """
        Returns a string representation of the crop using its primary key.
        """
        return f"Crop #{self.pk}"

    class Meta:
        verbose_name_plural = "Crops"
        ordering = ["-created_at"]


# -------------------------------
# Crop Transfer  (many-to-one : Crops → CropTransfer)
# -------------------------------
class CropTransfer(models.Model):
    transaction_id = models.CharField(primary_key=True, editable=False, blank=False)

    crop = models.ForeignKey(Crops, on_delete=models.CASCADE, related_name="transfers")

    from_user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="sent_transfers"
    )
    to_user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="received_transfers"
    )

    cost = models.DecimalField(
        max_digits=16, decimal_places=2, validators=[MinValueValidator(0)], default=0
    )
    timestamp = models.DateTimeField(auto_now_add=True)

    STATUS_CHOICES = [
        ("PENDING", "Pending"),
        ("COMPLETED", "Completed"),
        ("CANCELLED", "Cancelled"),
    ]
    status = models.CharField(
        max_length=10, choices=STATUS_CHOICES, default="COMPLETED"
    )

    def __str__(self) -> str:
        """
        Returns a string summarizing the crop transfer, including transaction ID and usernames of sender and receiver.
        """
        return (
            f"Transfer {self.transaction_id} — "
            f"{self.from_user.username} ➜ {self.to_user.username}"
        )

    class Meta:
        ordering = ["-timestamp"]

