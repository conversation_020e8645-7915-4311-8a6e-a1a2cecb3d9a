import apiClient from '@/services/apiClient'
import { useAuthStore } from '@/stores/authStore'
import { useWallet } from '@txnlab/use-wallet-react'
import { AlertCircle, CheckCircle2, Copy, ExternalLink, User } from 'lucide-react'
import { useState } from 'react'
import { ellipseAddress } from '../../utils/ellipseAddress'

interface SelectAccountScreenProps {
  handleNext: () => void
  handleBack: () => void
  selectedAccount: string
  setSelectedAccount: (address: string) => void
}

export default function SelectAccountScreen({ handleNext, handleBack, selectedAccount, setSelectedAccount }: SelectAccountScreenProps) {
  const [copySuccess, setCopySuccess] = useState<string | null>(null)
  const { wallets } = useWallet()

  const handleAccountSelect = (address: string) => {
    setSelectedAccount(address)
  }

  const handleCopyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address)
      setCopySuccess(address)
      setTimeout(() => setCopySuccess(null), 2000)
    } catch (err) {
      console.error('Failed to copy address:', err)
    }
  }

  const handleAccountConfirm = async () => {
    try {
      if (selectedAccount) {
        console.log('Selected account:', selectedAccount)
        const response = await apiClient.post('/user/connect-account/', { account_address: selectedAccount })
        const activeWallet = wallets[0]
        if (activeWallet) {
          await activeWallet.connect()
          activeWallet.setActiveAccount(selectedAccount)
        }
        if (response.status !== 200) {
          throw new Error('Wallet connection failed')
        }
        useAuthStore.getState().updateUser({ account_address: selectedAccount })
        handleNext()
      }
    } catch (error) {
      console.error('Wallet connection failed:', error)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-primary-text mb-4">Select Account</h2>
        <p className="text-primary-text opacity-70 mb-2">
          Choose which account you'd like to use with Agritram. This account will be used for all transactions and asset management.
        </p>
      </div>

      <div className="bg-alt-bg shadow-md p-6 rounded-lg space-y-6">
        <div className="space-y-4">
          {wallets[0]?.accounts?.map((account) => (
            <label
              key={account.address}
              className={`flex items-center gap-4 p-4 rounded-lg cursor-pointer transition-colors duration-200 ${
                selectedAccount === account.address
                  ? 'bg-button-bg bg-opacity-20 border border-border-primary'
                  : 'bg-alt-bg border border-transparent hover:border-border-primary'
              }`}
            >
              <input
                type="radio"
                name="account"
                className="h-4 w-4 text-border-primary bg-alt-bg border-border-primary focus:ring-border-primary accent-border-primary"
                checked={selectedAccount === account.address}
                onChange={() => handleAccountSelect(account.address)}
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm text-primary-text font-mono">{ellipseAddress(account.address)}</span>
                  <button
                    onClick={() => handleCopyAddress(account.address)}
                    className="text-primary-text hover:text-primary-text/80 transition-colors p-1 rounded-md hover:bg-alt-bg"
                    title="Copy address"
                  >
                    {copySuccess === account.address ? (
                      <CheckCircle2 className="h-4 w-4 text-border-primary" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </button>
                  <a
                    href={`https://lora.algokit.io/localnet/account/${account.address}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-text hover:text-primary-text/80 transition-colors p-1 rounded-md hover:bg-alt-bg"
                    title="View on explorer"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </div>
              </div>
            </label>
          ))}
        </div>

        {!selectedAccount && (
          <div className="flex items-start gap-3 p-4 bg-alt-bg rounded-lg text-primary-text">
            <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-primary-text">
              Please select an account to continue. Make sure the selected account has sufficient ALGO balance for transactions and asset
              opt-ins.
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-primary-text/10 text-primary-text opacity-60 hover:text-primary-text hover:border-primary-text  duration-300"
        >
          Back
        </button>
        <button
          onClick={handleAccountConfirm}
          disabled={!selectedAccount}
          className="bg-button-bg text-button-text px-6 py-2 rounded-lg hover:bg-button-bg-hover duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <User className="h-5 w-5 mr-2" />
          Continue with Selected Account
        </button>
      </div>
    </div>
  )
}
