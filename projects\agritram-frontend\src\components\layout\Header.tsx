import { motion } from 'framer-motion';
import { Bell, Search, User } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';

interface HeaderProps {
  title: string;
}

const Header = ({ title }: HeaderProps) => {
  const { userName, walletAddress } = useAuthStore();
  
  const truncateAddress = (address: string | null) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };
  
  return (
    <header className="flex items-center justify-between p-4 border-b border-neutral-200 bg-white">
      <h1 className="text-2xl font-semibold text-neutral-900">{title}</h1>
      
      <div className="flex items-center gap-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search..."
            className="pl-10 pr-4 py-2 rounded-lg border border-neutral-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="relative p-2 rounded-full text-neutral-600 hover:bg-neutral-100"
        >
          <Bell className="w-5 h-5" />
          <span className="absolute top-0 right-0 w-2 h-2 bg-primary-500 rounded-full"></span>
        </motion.button>
        
        <div className="flex items-center gap-3">
          <div className="flex flex-col items-end">
            <p className="text-sm font-medium text-neutral-900">{userName}</p>
            <p className="text-xs text-neutral-500">{truncateAddress(walletAddress)}</p>
          </div>
          <div className="flex items-center justify-center w-9 h-9 rounded-full bg-primary-100 text-primary-700">
            <User className="w-5 h-5" />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;