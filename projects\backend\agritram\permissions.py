from rest_framework.permissions import BasePermission
from rest_framework.request import Request
from rest_framework.views import APIView


class CustomPermission(BasePermission):
    def has_permission(self, request: Request, view: APIView) -> bool:
        # Super Admin: All permissions
        if request.user.is_superuser:
            return True

        # Admin: All permissions
        if request.user.groups.filter(name="Admin").exists():
            return request.method in ["GET", "POST", "PUT"]

        # AI: GET and POST permissions
        if request.user.groups.filter(name="AI").exists():
            return request.method in ["GET", "POST"]

        # Staff: Only GET permission
        if request.user.groups.filter(name="Staff").exists() and request.user.is_staff:
            return request.method == "GET"

        # Read-Only: GET permission for others
        return (
            (request.method == "GET")
            or (
                request.method == "POST"
                and (
                    view.__class__.__name__ == "user_note_post_list"
                    or view.__class__.__name__ == "user_saved_posts_handler"
                )
            )
            or (
                request.method == "PUT"
                and (
                    view.__class__.__name__ == "update_user"
                    or view.__class__.__name__ == "reset_password"
                )
            )
        )
