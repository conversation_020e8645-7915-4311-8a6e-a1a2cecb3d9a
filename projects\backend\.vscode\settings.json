{
  // General - see also /.editorconfig
  "editor.formatOnSave": true,
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    ".mypy_cache": true,
    ".pytest_cache": true,
    ".ruff_cache": true,
    "**/__pycache__": true,
    ".idea": true
  },

  // Python
  "python.analysis.autoImportCompletions": true,
  "python.analysis.extraPaths": ["${workspaceFolder}/smart_contracts"],
  "python.analysis.diagnosticSeverityOverrides": {
    "reportMissingModuleSource": "none"
  },
  "python.defaultInterpreterPath": "${workspaceFolder}/.venv",
  "[python]": {
    "editor.codeActionsOnSave": {
      "source.fixAll": "explicit",
      // Prevent default import sorting from running; Ruff will sort imports for us anyway
      "source.organizeImports": "never"
    },
    "editor.defaultFormatter": "ms-python.black-formatter"
  },
  "black-formatter.args": ["--config=pyproject.toml"],
  "python.testing.pytestEnabled": true,

  // Ruff
  "ruff.enable": true,
  "ruff.importStrategy": "fromEnvironment",
  "ruff.fixAll": true,
  "ruff.organizeImports": true,
  "ruff.codeAction.disableRuleComment": { "enable": true },
  "ruff.codeAction.fixViolation": { "enable": true },

  // Pylance type checking (already off)
  "python.analysis.typeCheckingMode": "off",

  // Windows terminal policy helper
  "terminal.integrated.shellArgs.windows": ["-ExecutionPolicy", "RemoteSigned"]
}
