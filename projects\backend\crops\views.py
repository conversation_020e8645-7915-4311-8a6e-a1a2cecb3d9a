from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from .models import CropTransfer, Crops
from .serializers import (
    CropTransferSerializer,
    CropsSerializer,
    CropTransferHistorySerializer,
)
from django.db import models


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def create_crop_transfer(request):
    """
    Handles POST requests to create a new crop transfer record.

    Validates incoming crop transfer data using the CropTransferSerializer. On success,
    returns the created crop transfer data with HTTP 201 status; on validation failure,
    returns serializer errors with HTTP 400 status. Any exceptions during processing are
    caught and returned as an error message with HTTP 400 status.
    """
    print(request.data)
    try:
        serializer = CropTransferSerializer(data=request.data)
        if serializer.is_valid():
            transfer = serializer.save()
            return Response(
                CropTransferSerializer(transfer).data, status=status.HTTP_201_CREATED
            )
        else:
            # Add this else block to handle invalid serializer data
            print(serializer.errors)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        print(e)
        # Return a more informative error response
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_crop(request, crop_id):
    """
    Handles GET requests to retrieve a crop by its crop_id.
    Returns the crop data if found, otherwise returns 404.
    """
    crop = get_object_or_404(Crops, crop_id=crop_id)
    serializer = CropsSerializer(crop)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_all_crops(request):
    """
    Handles GET requests to retrieve all crops.
    Returns a list of all crop data.
    """
    crops = Crops.objects.all()
    serializer = CropsSerializer(crops, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_crop_transaction_history(request, crop_id):
    """
    Handles GET requests to retrieve the transaction history for a specific crop for the authenticated user.
    Returns a list of CropTransfer records where the user is either the sender or receiver for the given crop.
    """
    transfers = (
        CropTransfer.objects.filter(crop__crop_id=crop_id)
        .order_by("timestamp")
    )
    serializer = CropTransferHistorySerializer(transfers, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)
