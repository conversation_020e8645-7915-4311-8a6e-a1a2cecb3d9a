import { AlgorandClient } from '@algorandfoundation/algokit-utils'
import { useWallet } from '@txnlab/use-wallet-react'
import { SupplyChainClient } from '../contracts/SupplyChain'
import { getAlgodConfigFromViteEnvironment } from './network/getAlgoClientConfigs'
import { VITE_SUPPLY_CHAIN_SM_ID } from './variable'

export const algodConfig = getAlgodConfigFromViteEnvironment()
export const algorand = AlgorandClient.fromConfig({ algodConfig })

/**
 * Returns a `SupplyChainClient` instance configured with the connected wallet's address and signer.
 *
 * The client is set up to interact with the supply chain smart contract using the current wallet context and the contract application ID from environment variables.
 *
 * @returns A `SupplyChainClient` instance ready for contract interactions with the active wallet.
 */
export function useSupplyChainClient() {
  const { activeAddress, transactionSigner: signer } = useWallet()

  return new SupplyChainClient({
    appId: BigInt(VITE_SUPPLY_CHAIN_SM_ID),
    algorand: algorand,
    defaultSender: activeAddress || undefined,
    defaultSigner: signer || undefined,
  })
}
