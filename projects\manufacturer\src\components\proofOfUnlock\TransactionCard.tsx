import { ChevronRight, Wheat } from 'lucide-react'
import { Transaction } from '../../stores/transactionStore'
import MilestoneProgress from './MilestoneProgress'

interface TransactionCardProps {
  transaction: Transaction
  onClick: () => void
}

const TransactionCard = ({ transaction, onClick }: TransactionCardProps) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-status-active-bg text-status-active-text">Active</span>
      case 'completed':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-status-completed-bg text-status-completed-text">Completed</span>
        )
      case 'pending':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-status-pending-bg text-status-pending-text">Pending</span>
      default:
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-accent-light/20 text-accent-dark">{status}</span>
    }
  }

  // Calculate progress percentage
  const completedMilestones = transaction.milestones.filter((m) => ['completed', 'verified', 'released'].includes(m.status)).length
  const progressPercentage = Math.round((completedMilestones / transaction.milestones.length) * 100)

  return (
    <div
      className="bg-card-bg rounded-xl shadow-md hover:shadow-lg border border-card-border overflow-hidden cursor-pointer hover:translate-y-[-4px] transition-all duration-300 hover:bg-accent-light/5"
      onClick={onClick}
    >
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-lg bg-accent-light/10 text-accent">
              <Wheat className="w-5 h-5" />
            </div>
            <div className="w-full">
              {/* Product Name Header */}
              <h3 className="text-lg font-semibold text-primary-text mb-1">
                {/* Group crops by product name */}
                {Object.values(
                  transaction.crops.reduce((acc, crop) => {
                    if (!acc[crop.productName]) {
                      acc[crop.productName] = true
                    }
                    return acc
                  }, {} as Record<string, boolean>),
                ).length > 1 ? (
                  // Multiple product types
                  <span>Multiple Products</span>
                ) : (
                  // Single product type
                  <span>{transaction.crops[0].productName}</span>
                )}
              </h3>

              {/* Crop Details - replaced with summary */}
              <div className="flex flex-row gap-2 mt-1 mb-2 items-center">
                <div className="inline-flex items-center bg-accent-light/10 rounded-lg px-2 py-1 w-fit">
                  <span className="text-xs font-medium text-primary-text">Total Batches:</span>
                  <span className="ml-1.5 text-xs text-accent border-l border-card-border pl-1.5">
                    {new Set(transaction.crops.map((crop) => crop.crop_id)).size}
                  </span>
                </div>
                <div className="inline-flex items-center bg-accent-light/10 rounded-lg px-2 py-1 w-fit">
                  <span className="text-xs font-medium text-primary-text">Total Quantity:</span>
                  <span className="ml-1.5 text-xs text-accent border-l border-card-border pl-1.5">
                    {Number(
                      transaction.crops.reduce((total, crop) => {
                        if (crop.unit === 'tonnes' || crop.unit === 'tons') {
                          return total + (crop.quantity || 0)
                        } else if (crop.unit === 'kg' || crop.unit === 'kilograms') {
                          return total + (crop.quantity || 0) / 1000
                        } else {
                          return total
                        }
                      }, 0) || 0,
                    ).toFixed(3)}{' '}
                    tonnes
                  </span>
                </div>
              </div>
            </div>
          </div>
          {getStatusBadge(transaction.status)}
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm font-medium text-secondary-text">Total Amount</p>
            <p className="text-lg font-semibold text-accent">KTT {transaction.total_amount.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-secondary-text">Released</p>
            <p className="text-lg font-semibold text-accent">KTT {transaction.release_amount.toLocaleString()}</p>
          </div>
        </div>

        <MilestoneProgress milestones={transaction.milestones} />

        <div className="flex justify-between items-center mt-4 pt-4 border-t border-card-border">
          <div>
            <p className="text-sm font-medium text-primary-text">Progress</p>
            <p className="text-xs text-secondary-text">
              {completedMilestones} of {transaction.milestones.length} milestones
            </p>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-accent">{progressPercentage}%</span>
            <ChevronRight className="w-5 h-5 text-secondary-text" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default TransactionCard
