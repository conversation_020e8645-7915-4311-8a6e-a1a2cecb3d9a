/* eslint-disable */
/**
 * This file was automatically generated by @algorandfoundation/algokit-client-generator.
 * DO NOT MODIFY IT BY HAND.
 * requires: @algorandfoundation/algokit-utils: ^7
 */
import { type AlgorandClient } from '@algorandfoundation/algokit-utils/types/algorand-client'
import { ABIReturn, AppReturn, SendAppTransactionResult } from '@algorandfoundation/algokit-utils/types/app'
import { Arc56Contract, getArc56ReturnValue, getABIStructFromABITuple } from '@algorandfoundation/algokit-utils/types/app-arc56'
import {
  AppClient as _AppClient,
  AppClientMethodCallParams,
  AppClientParams,
  AppClientBareCallParams,
  CallOnComplete,
  AppClientCompilationParams,
  ResolveAppClientByCreatorAndName,
  ResolveAppClientByNetwork,
  CloneAppClientParams,
} from '@algorandfoundation/algokit-utils/types/app-client'
import { AppFactory as _AppFactory, AppFactoryAppClientParams, AppFactoryResolveAppClientByCreatorAndNameParams, AppFactoryDeployParams, AppFactoryParams, CreateSchema } from '@algorandfoundation/algokit-utils/types/app-factory'
import { TransactionComposer, AppCallMethodCall, AppMethodCallTransactionArgument, SimulateOptions, RawSimulateOptions, SkipSignaturesSimulateOptions } from '@algorandfoundation/algokit-utils/types/composer'
import { SendParams, SendSingleTransactionResult, SendAtomicTransactionComposerResults } from '@algorandfoundation/algokit-utils/types/transaction'
import { Address, encodeAddress, modelsv2, OnApplicationComplete, Transaction, TransactionSigner } from 'algosdk'
import SimulateResponse = modelsv2.SimulateResponse

export const APP_SPEC: Arc56Contract = {"name":"ProofOfUnlock","structs":{},"methods":[{"name":"create_contract","args":[{"type":"address","name":"buyer"},{"type":"address","name":"seller"},{"type":"uint64","name":"total_amount"},{"type":"asset","name":"asset"},{"type":"uint64[]","name":"milestone_percentages"}],"returns":{"type":"void"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"fund_escrow","args":[{"type":"axfer","name":"payment"}],"returns":{"type":"void"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"approve_milestone","args":[{"type":"uint64","name":"milestone_index"}],"returns":{"type":"void"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"release_funds","args":[{"type":"asset","name":"asset"}],"returns":{"type":"void"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"get_balance","args":[{"type":"asset","name":"asset"}],"returns":{"type":"uint64"},"actions":{"create":[],"call":["NoOp"]},"readonly":true,"events":[],"recommendations":{}}],"arcs":[22,28],"networks":{},"state":{"schema":{"global":{"ints":0,"bytes":6},"local":{"ints":0,"bytes":0}},"keys":{"global":{"buyer":{"keyType":"AVMString","valueType":"address","key":"YnV5ZXI="},"seller":{"keyType":"AVMString","valueType":"address","key":"c2VsbGVy"},"total_amount":{"keyType":"AVMString","valueType":"uint64","key":"dG90YWxfYW1vdW50"},"milestones":{"keyType":"AVMString","valueType":"(uint64,bool,bool)[]","key":"bWlsZXN0b25lcw=="},"released":{"keyType":"AVMString","valueType":"uint64","key":"cmVsZWFzZWQ="},"withdrawn":{"keyType":"AVMString","valueType":"uint64","key":"d2l0aGRyYXdu"}},"local":{},"box":{}},"maps":{"global":{},"local":{},"box":{}}},"bareActions":{"create":["NoOp"],"call":[]},"sourceInfo":{"approval":{"sourceInfo":[{"pc":[754],"errorMessage":"Already approved"},{"pc":[630],"errorMessage":"Incorrect amount"},{"pc":[479,723],"errorMessage":"Index access is out of bounds"},{"pc":[614],"errorMessage":"Invalid buyer"},{"pc":[710],"errorMessage":"Invalid milestone index"},{"pc":[620],"errorMessage":"Invalid receiver"},{"pc":[606],"errorMessage":"Must be second transaction in group"},{"pc":[1062],"errorMessage":"No funds available"},{"pc":[128,155,173,188,210],"errorMessage":"OnCompletion is not NoOp"},{"pc":[1003],"errorMessage":"Only buyer can release"},{"pc":[340],"errorMessage":"Total percentages must equal 100%"},{"pc":[762],"errorMessage":"Unauthorized"},{"pc":[245],"errorMessage":"can only call when creating"},{"pc":[131,158,176,191,213],"errorMessage":"can only call when not creating"},{"pc":[366,610,760,953],"errorMessage":"check self.buyer exists"},{"pc":[521,703,828],"errorMessage":"check self.milestones exists"},{"pc":[419,865,887,1007],"errorMessage":"check self.released exists"},{"pc":[408,1105,1138],"errorMessage":"check self.seller exists"},{"pc":[414,625,871],"errorMessage":"check self.total_amount exists"},{"pc":[424,1012,1066,1078],"errorMessage":"check self.withdrawn exists"},{"pc":[201],"errorMessage":"transaction type is axfer"}],"pcOffsetMethod":"none"},"clear":{"sourceInfo":[],"pcOffsetMethod":"none"}},"source":{"approval":"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","clear":"I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"},"byteCode":{"approval":"CiAEAAFBBCYIAQAIcmVsZWFzZWQJd2l0aGRyYXduBWJ1eWVyCm1pbGVzdG9uZXMGc2VsbGVyDHRvdGFsX2Ftb3VudAgAAAAAAAAAADEbQQCcggUEhmqiWwSERt4UBCAwplYEDsOWegTGda4LNhoAjgUAVAA+AC8AHQACIkMxGRREMRhENhoBF8AwiAP3gAQVH3x1TFCwI0MxGRREMRhENhoBF8AwiALqI0MxGRREMRhENhoBiAHWI0MxGRREMRhEMRYjCUk4ECUSRIgBWyNDMRkURDEYRDYaATYaAjYaAzYaBBfAMDYaBYgADSNDMRlA/4kxGBREI0OKBQAiggIAFmNyZWF0ZV9jb250cmFjdCBjYWxsZWSL+1CL/FCL/VCL/hZQi/9QsCKL/yJZIosEiwMMQQAai/9XAgCLBElOAoEIC1uLAgiMAiMIjARC/96LAoFkEkQri/tnJwWL/GcnBov9ZyknB2cqJwdnIitlRIAhU3RhdGUgYWZ0ZXIgc3RvcmluZyBjb250cmFjdCBkYXRhTFAiJwVlRFAiJwZlRFAiKWVEUCIqZURQsLEyADIKIrISi/6yEbIUJbIQsgGzgAIAAIwAIowBiwGLAwxBADCL/1cCAIsBSU4CgQgLgQhYiwBXAgBMKFAkIlRQSRWBCQoWVwYCTFCMACMIjAFC/8gnBIsAZyInBGVEgBhNaWxlc3RvbmVzIGFmdGVyIHN0b3JpbmdMULCJigEAi/84E4ASZnVuZF9lc2Nyb3cgY2FsbGVkSwFQi/84FExLAVCL/zgSSRZPAksBULAxFiMSRCIrZURLBBJEMgpPAxJEIicGZUQXTwISRIANRXNjcm93IGZ1bmRlZE8CUExQsImKAQCAGGFwcHJvdmVfbWlsZXN0b25lIGNhbGxlZIv/UDEAULCL/xdJIicEZUxJTgJOA0RJIllLAg1EVwIATIEJC0lOAoEJWEmBQFMoIk8CVCgTQQCsiwMkUygiTwJUKBNBAJ4jFEQxACIrZUQSRIsDgUAjVCQjVEmMA4sCgQIIiwFMSwJdJwRMZ4sAFoAYTWlsZXN0b25lIGFmdGVyIGFwcHJvdmFsTFBLAVAiJwRlRFCwgUBTKCJPAlQoE0EAQYsDJFMoIk8CVCgTQQAziwMiWyIpZUQXIicGZUQXTwILgWQKCBYpTGciKWVEgBBSZWxlYXNlZCB1cGRhdGVkTFCwiSJC/1+KAQCL/xaAFHJlbGVhc2VfZnVuZHMgY2FsbGVkTFAxAFCwMQAiK2VEE0EALoAnV2l0aGRyYXcgZmFpbGVkOiBPbmx5IGJ1eWVyIGNhbiByZWxlYXNlMQBQsAAiKWVEFyIqZUQXCUkiDkEAKhaAI1dpdGhkcmF3IGZhaWxlZDogTm8gZnVuZHMgYXZhaWxhYmxlTFCwACIqZUQXSwEIFipMZyIqZUSAEVdpdGhkcmF3biB1cGRhdGVkTFCwIicFZUSAEFdpdGhkcmF3IHN1Y2Nlc3NMUEsBFlCwsTIAIicFZUSL/7IRTwKyErIUJbIQsgGziYoBAYv/FoASZ2V0X2JhbGFuY2UgY2FsbGVkTFCwMgqL/3AAQQAFiwAWTIknB0L/+Q==","clear":"CoEBQw=="},"compilerInfo":{"compiler":"puya","compilerVersion":{"major":4,"minor":7,"patch":0}},"events":[],"templateVariables":{}} as unknown as Arc56Contract

/**
 * A state record containing binary data
 */
export interface BinaryState {
  /**
   * Gets the state value as a Uint8Array
   */
  asByteArray(): Uint8Array | undefined
  /**
   * Gets the state value as a string
   */
  asString(): string | undefined
}

class BinaryStateValue implements BinaryState {
  constructor(private value: Uint8Array | undefined) {}

  asByteArray(): Uint8Array | undefined {
    return this.value
  }

  asString(): string | undefined {
    return this.value !== undefined ? Buffer.from(this.value).toString('utf-8') : undefined
  }
}

/**
 * Expands types for IntelliSense so they are more human readable
 * See https://stackoverflow.com/a/69288824
 */
export type Expand<T> = T extends (...args: infer A) => infer R
  ? (...args: Expand<A>) => Expand<R>
  : T extends infer O
    ? { [K in keyof O]: O[K] }
    : never


/**
 * The argument types for the ProofOfUnlock contract
 */
export type ProofOfUnlockArgs = {
  /**
   * The object representation of the arguments for each method
   */
  obj: {
    'create_contract(address,address,uint64,asset,uint64[])void': {
      buyer: string
      seller: string
      totalAmount: bigint | number
      asset: bigint
      milestonePercentages: bigint[] | number[]
    }
    'fund_escrow(axfer)void': {
      payment: AppMethodCallTransactionArgument
    }
    'approve_milestone(uint64)void': {
      milestoneIndex: bigint | number
    }
    'release_funds(asset)void': {
      asset: bigint
    }
    'get_balance(asset)uint64': {
      asset: bigint
    }
  }
  /**
   * The tuple representation of the arguments for each method
   */
  tuple: {
    'create_contract(address,address,uint64,asset,uint64[])void': [buyer: string, seller: string, totalAmount: bigint | number, asset: bigint, milestonePercentages: bigint[] | number[]]
    'fund_escrow(axfer)void': [payment: AppMethodCallTransactionArgument]
    'approve_milestone(uint64)void': [milestoneIndex: bigint | number]
    'release_funds(asset)void': [asset: bigint]
    'get_balance(asset)uint64': [asset: bigint]
  }
}

/**
 * The return type for each method
 */
export type ProofOfUnlockReturns = {
  'create_contract(address,address,uint64,asset,uint64[])void': void
  'fund_escrow(axfer)void': void
  'approve_milestone(uint64)void': void
  'release_funds(asset)void': void
  'get_balance(asset)uint64': bigint
}

/**
 * Defines the types of available calls and state of the ProofOfUnlock smart contract.
 */
export type ProofOfUnlockTypes = {
  /**
   * Maps method signatures / names to their argument and return types.
   */
  methods:
    & Record<'create_contract(address,address,uint64,asset,uint64[])void' | 'create_contract', {
      argsObj: ProofOfUnlockArgs['obj']['create_contract(address,address,uint64,asset,uint64[])void']
      argsTuple: ProofOfUnlockArgs['tuple']['create_contract(address,address,uint64,asset,uint64[])void']
      returns: ProofOfUnlockReturns['create_contract(address,address,uint64,asset,uint64[])void']
    }>
    & Record<'fund_escrow(axfer)void' | 'fund_escrow', {
      argsObj: ProofOfUnlockArgs['obj']['fund_escrow(axfer)void']
      argsTuple: ProofOfUnlockArgs['tuple']['fund_escrow(axfer)void']
      returns: ProofOfUnlockReturns['fund_escrow(axfer)void']
    }>
    & Record<'approve_milestone(uint64)void' | 'approve_milestone', {
      argsObj: ProofOfUnlockArgs['obj']['approve_milestone(uint64)void']
      argsTuple: ProofOfUnlockArgs['tuple']['approve_milestone(uint64)void']
      returns: ProofOfUnlockReturns['approve_milestone(uint64)void']
    }>
    & Record<'release_funds(asset)void' | 'release_funds', {
      argsObj: ProofOfUnlockArgs['obj']['release_funds(asset)void']
      argsTuple: ProofOfUnlockArgs['tuple']['release_funds(asset)void']
      returns: ProofOfUnlockReturns['release_funds(asset)void']
    }>
    & Record<'get_balance(asset)uint64' | 'get_balance', {
      argsObj: ProofOfUnlockArgs['obj']['get_balance(asset)uint64']
      argsTuple: ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']
      returns: ProofOfUnlockReturns['get_balance(asset)uint64']
    }>
  /**
   * Defines the shape of the state of the application.
   */
  state: {
    global: {
      keys: {
        buyer: string
        seller: string
        totalAmount: bigint
        milestones: [bigint, boolean, boolean][]
        released: bigint
        withdrawn: bigint
      }
      maps: {}
    }
  }
}

/**
 * Defines the possible abi call signatures.
 */
export type ProofOfUnlockSignatures = keyof ProofOfUnlockTypes['methods']
/**
 * Defines the possible abi call signatures for methods that return a non-void value.
 */
export type ProofOfUnlockNonVoidMethodSignatures = keyof ProofOfUnlockTypes['methods'] extends infer T ? T extends keyof ProofOfUnlockTypes['methods'] ? MethodReturn<T> extends void ? never : T  : never : never
/**
 * Defines an object containing all relevant parameters for a single call to the contract.
 */
export type CallParams<TArgs> = Expand<
  Omit<AppClientMethodCallParams, 'method' | 'args' | 'onComplete'> &
    {
      /** The args for the ABI method call, either as an ordered array or an object */
      args: Expand<TArgs>
    }
>
/**
 * Maps a method signature from the ProofOfUnlock smart contract to the method's arguments in either tuple or struct form
 */
export type MethodArgs<TSignature extends ProofOfUnlockSignatures> = ProofOfUnlockTypes['methods'][TSignature]['argsObj' | 'argsTuple']
/**
 * Maps a method signature from the ProofOfUnlock smart contract to the method's return type
 */
export type MethodReturn<TSignature extends ProofOfUnlockSignatures> = ProofOfUnlockTypes['methods'][TSignature]['returns']

/**
 * Defines the shape of the keyed global state of the application.
 */
export type GlobalKeysState = ProofOfUnlockTypes['state']['global']['keys']


/**
 * Defines supported create method params for this smart contract
 */
export type ProofOfUnlockCreateCallParams =
  | Expand<AppClientBareCallParams & {method?: never} & {onComplete?: OnApplicationComplete.NoOpOC} & CreateSchema>
/**
 * Defines arguments required for the deploy method.
 */
export type ProofOfUnlockDeployParams = Expand<Omit<AppFactoryDeployParams, 'createParams' | 'updateParams' | 'deleteParams'> & {
  /**
   * Create transaction parameters to use if a create needs to be issued as part of deployment; use `method` to define ABI call (if available) or leave out for a bare call (if available)
   */
  createParams?: ProofOfUnlockCreateCallParams
}>


/**
 * Exposes methods for constructing `AppClient` params objects for ABI calls to the ProofOfUnlock smart contract
 */
export abstract class ProofOfUnlockParamsFactory {
  /**
   * Constructs a no op call for the create_contract(address,address,uint64,asset,uint64[])void ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static createContract(params: CallParams<ProofOfUnlockArgs['obj']['create_contract(address,address,uint64,asset,uint64[])void'] | ProofOfUnlockArgs['tuple']['create_contract(address,address,uint64,asset,uint64[])void']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'create_contract(address,address,uint64,asset,uint64[])void' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.buyer, params.args.seller, params.args.totalAmount, params.args.asset, params.args.milestonePercentages],
    }
  }
  /**
   * Constructs a no op call for the fund_escrow(axfer)void ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static fundEscrow(params: CallParams<ProofOfUnlockArgs['obj']['fund_escrow(axfer)void'] | ProofOfUnlockArgs['tuple']['fund_escrow(axfer)void']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'fund_escrow(axfer)void' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.payment],
    }
  }
  /**
   * Constructs a no op call for the approve_milestone(uint64)void ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static approveMilestone(params: CallParams<ProofOfUnlockArgs['obj']['approve_milestone(uint64)void'] | ProofOfUnlockArgs['tuple']['approve_milestone(uint64)void']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'approve_milestone(uint64)void' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.milestoneIndex],
    }
  }
  /**
   * Constructs a no op call for the release_funds(asset)void ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static releaseFunds(params: CallParams<ProofOfUnlockArgs['obj']['release_funds(asset)void'] | ProofOfUnlockArgs['tuple']['release_funds(asset)void']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'release_funds(asset)void' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.asset],
    }
  }
  /**
   * Constructs a no op call for the get_balance(asset)uint64 ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static getBalance(params: CallParams<ProofOfUnlockArgs['obj']['get_balance(asset)uint64'] | ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'get_balance(asset)uint64' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.asset],
    }
  }
}

/**
 * A factory to create and deploy one or more instance of the ProofOfUnlock smart contract and to create one or more app clients to interact with those (or other) app instances
 */
export class ProofOfUnlockFactory {
  /**
   * The underlying `AppFactory` for when you want to have more flexibility
   */
  public readonly appFactory: _AppFactory

  /**
   * Creates a new instance of `ProofOfUnlockFactory`
   *
   * @param params The parameters to initialise the app factory with
   */
  constructor(params: Omit<AppFactoryParams, 'appSpec'>) {
    this.appFactory = new _AppFactory({
      ...params,
      appSpec: APP_SPEC,
    })
  }
  
  /** The name of the app (from the ARC-32 / ARC-56 app spec or override). */
  public get appName() {
    return this.appFactory.appName
  }
  
  /** The ARC-56 app spec being used */
  get appSpec() {
    return APP_SPEC
  }
  
  /** A reference to the underlying `AlgorandClient` this app factory is using. */
  public get algorand(): AlgorandClient {
    return this.appFactory.algorand
  }
  
  /**
   * Returns a new `AppClient` client for an app instance of the given ID.
   *
   * Automatically populates appName, defaultSender and source maps from the factory
   * if not specified in the params.
   * @param params The parameters to create the app client
   * @returns The `AppClient`
   */
  public getAppClientById(params: AppFactoryAppClientParams) {
    return new ProofOfUnlockClient(this.appFactory.getAppClientById(params))
  }
  
  /**
   * Returns a new `AppClient` client, resolving the app by creator address and name
   * using AlgoKit app deployment semantics (i.e. looking for the app creation transaction note).
   *
   * Automatically populates appName, defaultSender and source maps from the factory
   * if not specified in the params.
   * @param params The parameters to create the app client
   * @returns The `AppClient`
   */
  public async getAppClientByCreatorAndName(
    params: AppFactoryResolveAppClientByCreatorAndNameParams,
  ) {
    return new ProofOfUnlockClient(await this.appFactory.getAppClientByCreatorAndName(params))
  }

  /**
   * Idempotently deploys the ProofOfUnlock smart contract.
   *
   * @param params The arguments for the contract calls and any additional parameters for the call
   * @returns The deployment result
   */
  public async deploy(params: ProofOfUnlockDeployParams = {}) {
    const result = await this.appFactory.deploy({
      ...params,
    })
    return { result: result.result, appClient: new ProofOfUnlockClient(result.appClient) }
  }

  /**
   * Get parameters to create transactions (create and deploy related calls) for the current app. A good mental model for this is that these parameters represent a deferred transaction creation.
   */
  readonly params = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the ProofOfUnlock smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The params for a create call
       */
      bare: (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        return this.appFactory.params.bare.create(params)
      },
    },

  }

  /**
   * Create transactions for the current app
   */
  readonly createTransaction = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the ProofOfUnlock smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The transaction for a create call
       */
      bare: (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        return this.appFactory.createTransaction.bare.create(params)
      },
    },

  }

  /**
   * Send calls to the current app
   */
  readonly send = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the ProofOfUnlock smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The create result
       */
      bare: async (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        const result = await this.appFactory.send.bare.create(params)
        return { result: result.result, appClient: new ProofOfUnlockClient(result.appClient) }
      },
    },

  }

}
/**
 * A client to make calls to the ProofOfUnlock smart contract
 */
export class ProofOfUnlockClient {
  /**
   * The underlying `AppClient` for when you want to have more flexibility
   */
  public readonly appClient: _AppClient

  /**
   * Creates a new instance of `ProofOfUnlockClient`
   *
   * @param appClient An `AppClient` instance which has been created with the ProofOfUnlock app spec
   */
  constructor(appClient: _AppClient)
  /**
   * Creates a new instance of `ProofOfUnlockClient`
   *
   * @param params The parameters to initialise the app client with
   */
  constructor(params: Omit<AppClientParams, 'appSpec'>)
  constructor(appClientOrParams: _AppClient | Omit<AppClientParams, 'appSpec'>) {
    this.appClient = appClientOrParams instanceof _AppClient ? appClientOrParams : new _AppClient({
      ...appClientOrParams,
      appSpec: APP_SPEC,
    })
  }
  
  /**
   * Checks for decode errors on the given return value and maps the return value to the return type for the given method
   * @returns The typed return value or undefined if there was no value
   */
  decodeReturnValue<TSignature extends ProofOfUnlockNonVoidMethodSignatures>(method: TSignature, returnValue: ABIReturn | undefined) {
    return returnValue !== undefined ? getArc56ReturnValue<MethodReturn<TSignature>>(returnValue, this.appClient.getABIMethod(method), APP_SPEC.structs) : undefined
  }
  
  /**
   * Returns a new `ProofOfUnlockClient` client, resolving the app by creator address and name
   * using AlgoKit app deployment semantics (i.e. looking for the app creation transaction note).
   * @param params The parameters to create the app client
   */
  public static async fromCreatorAndName(params: Omit<ResolveAppClientByCreatorAndName, 'appSpec'>): Promise<ProofOfUnlockClient> {
    return new ProofOfUnlockClient(await _AppClient.fromCreatorAndName({...params, appSpec: APP_SPEC}))
  }
  
  /**
   * Returns an `ProofOfUnlockClient` instance for the current network based on
   * pre-determined network-specific app IDs specified in the ARC-56 app spec.
   *
   * If no IDs are in the app spec or the network isn't recognised, an error is thrown.
   * @param params The parameters to create the app client
   */
  static async fromNetwork(
    params: Omit<ResolveAppClientByNetwork, 'appSpec'>
  ): Promise<ProofOfUnlockClient> {
    return new ProofOfUnlockClient(await _AppClient.fromNetwork({...params, appSpec: APP_SPEC}))
  }
  
  /** The ID of the app instance this client is linked to. */
  public get appId() {
    return this.appClient.appId
  }
  
  /** The app address of the app instance this client is linked to. */
  public get appAddress() {
    return this.appClient.appAddress
  }
  
  /** The name of the app. */
  public get appName() {
    return this.appClient.appName
  }
  
  /** The ARC-56 app spec being used */
  public get appSpec() {
    return this.appClient.appSpec
  }
  
  /** A reference to the underlying `AlgorandClient` this app client is using. */
  public get algorand(): AlgorandClient {
    return this.appClient.algorand
  }

  /**
   * Get parameters to create transactions for the current app. A good mental model for this is that these parameters represent a deferred transaction creation.
   */
  readonly params = {
    /**
     * Makes a clear_state call to an existing instance of the ProofOfUnlock smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams>) => {
      return this.appClient.params.bare.clearState(params)
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `create_contract(address,address,uint64,asset,uint64[])void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    createContract: (params: CallParams<ProofOfUnlockArgs['obj']['create_contract(address,address,uint64,asset,uint64[])void'] | ProofOfUnlockArgs['tuple']['create_contract(address,address,uint64,asset,uint64[])void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(ProofOfUnlockParamsFactory.createContract(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `fund_escrow(axfer)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    fundEscrow: (params: CallParams<ProofOfUnlockArgs['obj']['fund_escrow(axfer)void'] | ProofOfUnlockArgs['tuple']['fund_escrow(axfer)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(ProofOfUnlockParamsFactory.fundEscrow(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `approve_milestone(uint64)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    approveMilestone: (params: CallParams<ProofOfUnlockArgs['obj']['approve_milestone(uint64)void'] | ProofOfUnlockArgs['tuple']['approve_milestone(uint64)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(ProofOfUnlockParamsFactory.approveMilestone(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `release_funds(asset)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    releaseFunds: (params: CallParams<ProofOfUnlockArgs['obj']['release_funds(asset)void'] | ProofOfUnlockArgs['tuple']['release_funds(asset)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(ProofOfUnlockParamsFactory.releaseFunds(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `get_balance(asset)uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    getBalance: (params: CallParams<ProofOfUnlockArgs['obj']['get_balance(asset)uint64'] | ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(ProofOfUnlockParamsFactory.getBalance(params))
    },

  }

  /**
   * Create transactions for the current app
   */
  readonly createTransaction = {
    /**
     * Makes a clear_state call to an existing instance of the ProofOfUnlock smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams>) => {
      return this.appClient.createTransaction.bare.clearState(params)
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `create_contract(address,address,uint64,asset,uint64[])void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    createContract: (params: CallParams<ProofOfUnlockArgs['obj']['create_contract(address,address,uint64,asset,uint64[])void'] | ProofOfUnlockArgs['tuple']['create_contract(address,address,uint64,asset,uint64[])void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(ProofOfUnlockParamsFactory.createContract(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `fund_escrow(axfer)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    fundEscrow: (params: CallParams<ProofOfUnlockArgs['obj']['fund_escrow(axfer)void'] | ProofOfUnlockArgs['tuple']['fund_escrow(axfer)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(ProofOfUnlockParamsFactory.fundEscrow(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `approve_milestone(uint64)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    approveMilestone: (params: CallParams<ProofOfUnlockArgs['obj']['approve_milestone(uint64)void'] | ProofOfUnlockArgs['tuple']['approve_milestone(uint64)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(ProofOfUnlockParamsFactory.approveMilestone(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `release_funds(asset)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    releaseFunds: (params: CallParams<ProofOfUnlockArgs['obj']['release_funds(asset)void'] | ProofOfUnlockArgs['tuple']['release_funds(asset)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(ProofOfUnlockParamsFactory.releaseFunds(params))
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `get_balance(asset)uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    getBalance: (params: CallParams<ProofOfUnlockArgs['obj']['get_balance(asset)uint64'] | ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(ProofOfUnlockParamsFactory.getBalance(params))
    },

  }

  /**
   * Send calls to the current app
   */
  readonly send = {
    /**
     * Makes a clear_state call to an existing instance of the ProofOfUnlock smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams & SendParams>) => {
      return this.appClient.send.bare.clearState(params)
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `create_contract(address,address,uint64,asset,uint64[])void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    createContract: async (params: CallParams<ProofOfUnlockArgs['obj']['create_contract(address,address,uint64,asset,uint64[])void'] | ProofOfUnlockArgs['tuple']['create_contract(address,address,uint64,asset,uint64[])void']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(ProofOfUnlockParamsFactory.createContract(params))
      return {...result, return: result.return as unknown as (undefined | ProofOfUnlockReturns['create_contract(address,address,uint64,asset,uint64[])void'])}
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `fund_escrow(axfer)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    fundEscrow: async (params: CallParams<ProofOfUnlockArgs['obj']['fund_escrow(axfer)void'] | ProofOfUnlockArgs['tuple']['fund_escrow(axfer)void']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(ProofOfUnlockParamsFactory.fundEscrow(params))
      return {...result, return: result.return as unknown as (undefined | ProofOfUnlockReturns['fund_escrow(axfer)void'])}
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `approve_milestone(uint64)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    approveMilestone: async (params: CallParams<ProofOfUnlockArgs['obj']['approve_milestone(uint64)void'] | ProofOfUnlockArgs['tuple']['approve_milestone(uint64)void']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(ProofOfUnlockParamsFactory.approveMilestone(params))
      return {...result, return: result.return as unknown as (undefined | ProofOfUnlockReturns['approve_milestone(uint64)void'])}
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `release_funds(asset)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    releaseFunds: async (params: CallParams<ProofOfUnlockArgs['obj']['release_funds(asset)void'] | ProofOfUnlockArgs['tuple']['release_funds(asset)void']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(ProofOfUnlockParamsFactory.releaseFunds(params))
      return {...result, return: result.return as unknown as (undefined | ProofOfUnlockReturns['release_funds(asset)void'])}
    },

    /**
     * Makes a call to the ProofOfUnlock smart contract using the `get_balance(asset)uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    getBalance: async (params: CallParams<ProofOfUnlockArgs['obj']['get_balance(asset)uint64'] | ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(ProofOfUnlockParamsFactory.getBalance(params))
      return {...result, return: result.return as unknown as (undefined | ProofOfUnlockReturns['get_balance(asset)uint64'])}
    },

  }

  /**
   * Clone this app client with different params
   *
   * @param params The params to use for the the cloned app client. Omit a param to keep the original value. Set a param to override the original value. Setting to undefined will clear the original value.
   * @returns A new app client with the altered params
   */
  public clone(params: CloneAppClientParams) {
    return new ProofOfUnlockClient(this.appClient.clone(params))
  }

  /**
   * Makes a readonly (simulated) call to the ProofOfUnlock smart contract using the `get_balance(asset)uint64` ABI method.
   * 
   * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
   *
   * @param params The params for the smart contract call
   * @returns The call result
   */
  async getBalance(params: CallParams<ProofOfUnlockArgs['obj']['get_balance(asset)uint64'] | ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']>) {
    const result = await this.appClient.send.call(ProofOfUnlockParamsFactory.getBalance(params))
    return result.return as unknown as ProofOfUnlockReturns['get_balance(asset)uint64']
  }

  /**
   * Methods to access state for the current ProofOfUnlock app
   */
  state = {
    /**
     * Methods to access global state for the current ProofOfUnlock app
     */
    global: {
      /**
       * Get all current keyed values from global state
       */
      getAll: async (): Promise<Partial<Expand<GlobalKeysState>>> => {
        const result = await this.appClient.state.global.getAll()
        return {
          buyer: result.buyer,
          seller: result.seller,
          totalAmount: result.total_amount,
          milestones: result.milestones,
          released: result.released,
          withdrawn: result.withdrawn,
        }
      },
      /**
       * Get the current value of the buyer key in global state
       */
      buyer: async (): Promise<string | undefined> => { return (await this.appClient.state.global.getValue("buyer")) as string | undefined },
      /**
       * Get the current value of the seller key in global state
       */
      seller: async (): Promise<string | undefined> => { return (await this.appClient.state.global.getValue("seller")) as string | undefined },
      /**
       * Get the current value of the total_amount key in global state
       */
      totalAmount: async (): Promise<bigint | undefined> => { return (await this.appClient.state.global.getValue("total_amount")) as bigint | undefined },
      /**
       * Get the current value of the milestones key in global state
       */
      milestones: async (): Promise<[bigint, boolean, boolean][] | undefined> => { return (await this.appClient.state.global.getValue("milestones")) as [bigint, boolean, boolean][] | undefined },
      /**
       * Get the current value of the released key in global state
       */
      released: async (): Promise<bigint | undefined> => { return (await this.appClient.state.global.getValue("released")) as bigint | undefined },
      /**
       * Get the current value of the withdrawn key in global state
       */
      withdrawn: async (): Promise<bigint | undefined> => { return (await this.appClient.state.global.getValue("withdrawn")) as bigint | undefined },
    },
  }

  public newGroup(): ProofOfUnlockComposer {
    const client = this
    const composer = this.algorand.newGroup()
    let promiseChain:Promise<unknown> = Promise.resolve()
    const resultMappers: Array<undefined | ((x: ABIReturn | undefined) => any)> = []
    return {
      /**
       * Add a create_contract(address,address,uint64,asset,uint64[])void method call against the ProofOfUnlock contract
       */
      createContract(params: CallParams<ProofOfUnlockArgs['obj']['create_contract(address,address,uint64,asset,uint64[])void'] | ProofOfUnlockArgs['tuple']['create_contract(address,address,uint64,asset,uint64[])void']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.createContract(params)))
        resultMappers.push(undefined)
        return this
      },
      /**
       * Add a fund_escrow(axfer)void method call against the ProofOfUnlock contract
       */
      fundEscrow(params: CallParams<ProofOfUnlockArgs['obj']['fund_escrow(axfer)void'] | ProofOfUnlockArgs['tuple']['fund_escrow(axfer)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.fundEscrow(params)))
        resultMappers.push(undefined)
        return this
      },
      /**
       * Add a approve_milestone(uint64)void method call against the ProofOfUnlock contract
       */
      approveMilestone(params: CallParams<ProofOfUnlockArgs['obj']['approve_milestone(uint64)void'] | ProofOfUnlockArgs['tuple']['approve_milestone(uint64)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.approveMilestone(params)))
        resultMappers.push(undefined)
        return this
      },
      /**
       * Add a release_funds(asset)void method call against the ProofOfUnlock contract
       */
      releaseFunds(params: CallParams<ProofOfUnlockArgs['obj']['release_funds(asset)void'] | ProofOfUnlockArgs['tuple']['release_funds(asset)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.releaseFunds(params)))
        resultMappers.push(undefined)
        return this
      },
      /**
       * Add a get_balance(asset)uint64 method call against the ProofOfUnlock contract
       */
      getBalance(params: CallParams<ProofOfUnlockArgs['obj']['get_balance(asset)uint64'] | ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.getBalance(params)))
        resultMappers.push((v) => client.decodeReturnValue('get_balance(asset)uint64', v))
        return this
      },
      /**
       * Add a clear state call to the ProofOfUnlock contract
       */
      clearState(params: AppClientBareCallParams) {
        promiseChain = promiseChain.then(() => composer.addAppCall(client.params.clearState(params)))
        return this
      },
      addTransaction(txn: Transaction, signer?: TransactionSigner) {
        promiseChain = promiseChain.then(() => composer.addTransaction(txn, signer))
        return this
      },
      async composer() {
        await promiseChain
        return composer
      },
      async simulate(options?: SimulateOptions) {
        await promiseChain
        const result = await (!options ? composer.simulate() : composer.simulate(options))
        return {
          ...result,
          returns: result.returns?.map((val, i) => resultMappers[i] !== undefined ? resultMappers[i]!(val) : val.returnValue)
        }
      },
      async send(params?: SendParams) {
        await promiseChain
        const result = await composer.send(params)
        return {
          ...result,
          returns: result.returns?.map((val, i) => resultMappers[i] !== undefined ? resultMappers[i]!(val) : val.returnValue)
        }
      }
    } as unknown as ProofOfUnlockComposer
  }
}
export type ProofOfUnlockComposer<TReturns extends [...any[]] = []> = {
  /**
   * Calls the create_contract(address,address,uint64,asset,uint64[])void ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  createContract(params?: CallParams<ProofOfUnlockArgs['obj']['create_contract(address,address,uint64,asset,uint64[])void'] | ProofOfUnlockArgs['tuple']['create_contract(address,address,uint64,asset,uint64[])void']>): ProofOfUnlockComposer<[...TReturns, ProofOfUnlockReturns['create_contract(address,address,uint64,asset,uint64[])void'] | undefined]>

  /**
   * Calls the fund_escrow(axfer)void ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  fundEscrow(params?: CallParams<ProofOfUnlockArgs['obj']['fund_escrow(axfer)void'] | ProofOfUnlockArgs['tuple']['fund_escrow(axfer)void']>): ProofOfUnlockComposer<[...TReturns, ProofOfUnlockReturns['fund_escrow(axfer)void'] | undefined]>

  /**
   * Calls the approve_milestone(uint64)void ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  approveMilestone(params?: CallParams<ProofOfUnlockArgs['obj']['approve_milestone(uint64)void'] | ProofOfUnlockArgs['tuple']['approve_milestone(uint64)void']>): ProofOfUnlockComposer<[...TReturns, ProofOfUnlockReturns['approve_milestone(uint64)void'] | undefined]>

  /**
   * Calls the release_funds(asset)void ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  releaseFunds(params?: CallParams<ProofOfUnlockArgs['obj']['release_funds(asset)void'] | ProofOfUnlockArgs['tuple']['release_funds(asset)void']>): ProofOfUnlockComposer<[...TReturns, ProofOfUnlockReturns['release_funds(asset)void'] | undefined]>

  /**
   * Calls the get_balance(asset)uint64 ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  getBalance(params?: CallParams<ProofOfUnlockArgs['obj']['get_balance(asset)uint64'] | ProofOfUnlockArgs['tuple']['get_balance(asset)uint64']>): ProofOfUnlockComposer<[...TReturns, ProofOfUnlockReturns['get_balance(asset)uint64'] | undefined]>

  /**
   * Makes a clear_state call to an existing instance of the ProofOfUnlock smart contract.
   *
   * @param args The arguments for the bare call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  clearState(params?: AppClientBareCallParams): ProofOfUnlockComposer<[...TReturns, undefined]>

  /**
   * Adds a transaction to the composer
   *
   * @param txn A transaction to add to the transaction group
   * @param signer The optional signer to use when signing this transaction.
   */
  addTransaction(txn: Transaction, signer?: TransactionSigner): ProofOfUnlockComposer<TReturns>
  /**
   * Returns the underlying AtomicTransactionComposer instance
   */
  composer(): Promise<TransactionComposer>
  /**
   * Simulates the transaction group and returns the result
   */
  simulate(): Promise<ProofOfUnlockComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  simulate(options: SkipSignaturesSimulateOptions): Promise<ProofOfUnlockComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  simulate(options: RawSimulateOptions): Promise<ProofOfUnlockComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  /**
   * Sends the transaction group to the network and returns the results
   */
  send(params?: SendParams): Promise<ProofOfUnlockComposerResults<TReturns>>
}
export type ProofOfUnlockComposerResults<TReturns extends [...any[]]> = Expand<SendAtomicTransactionComposerResults & {
  returns: TReturns
}>

