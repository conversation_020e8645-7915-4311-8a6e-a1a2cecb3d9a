from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

TRANSACTION_TYPES = [
    ("MINT", "Mint"),
    ("TRANSFER", "Transfer"),
    ("<PERSON>UR<PERSON>", "Burn"),
    ("<PERSON><PERSON><PERSON><PERSON>", "Pledge"),
    ("CONVERT_KTT_TO_KCT", "Convert KTT to KCT"),
    ("CONVERT_KCT_TO_KTT", "Convert KCT to KTT"),
]

STATUS_CHOICES = [
    ("PENDING", "Pending"),
    ("COMPLETED", "Completed"),
    ("FAILED", "Failed"),
]
PAYMENT_METHODS = [
    ("MTN", "Mobile Money Transfer"),
    ("CARD", "Credit Card/Debit Card"),
]


class Transaction(models.Model):
    transaction_id = models.Char<PERSON>ield(primary_key=True, editable=False)
    algorand_tx_id = models.Char<PERSON><PERSON>(max_length=52, unique=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="transactions"
    )
    from_address = models.CharField(max_length=58)
    to_address = models.Char<PERSON>ield(max_length=58)
    amount_usd = models.DecimalField(max_digits=18, decimal_places=6, default=0)
    amount_stablecoin_ktt = models.DecimalField(
        max_digits=18, decimal_places=6, default=0
    )
    amount_stablecoin_kct = models.DecimalField(
        max_digits=18, decimal_places=6, default=0
    )
    transaction_type = models.CharField(max_length=18, choices=TRANSACTION_TYPES)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default="PENDING")
    timestamp = models.DateTimeField(default=timezone.now)
    gas_fee = models.DecimalField(max_digits=18, decimal_places=6)
    payment_method = models.CharField(
        max_length=10, choices=PAYMENT_METHODS, default="CARD"
    )

    class Meta:
        indexes = [
            models.Index(fields=["user", "timestamp"]),
            models.Index(fields=["user", "transaction_type"]),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.transaction_type} - {self.amount}"


class MonthlyExpense(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="monthly_expenses"
    )
    year = models.IntegerField()
    month = models.IntegerField()  # 1-12 for January-December
    total_amount = models.DecimalField(max_digits=18, decimal_places=6, default=0)
    transaction_type = models.CharField(
        max_length=18, choices=TRANSACTION_TYPES, default="MINT"
    )
    transaction_count = models.IntegerField(default=0)

    class Meta:
        unique_together = ["user", "year", "month", "transaction_type"]
        indexes = [
            models.Index(fields=["user", "year", "month"]),
        ]
        ordering = ["year", "month"]

    def __str__(self):
        return f"{self.user.username} - {self.year}/{self.month}: {self.total_amount}"
