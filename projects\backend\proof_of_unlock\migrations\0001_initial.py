# Generated by Django 5.2 on 2025-05-08 16:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("crops", "0013_remove_cropstatus_crop_remove_cropstatus_trader_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ProofOfUnlock",
            fields=[
                (
                    "tx_id",
                    models.CharField(
                        help_text="Unique transaction identifier (e.g., 'tx-1')",
                        max_length=100,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "contract_address",
                    models.Char<PERSON>ield(
                        help_text="Ethereum contract address handling the escrow",
                        max_length=42,
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total amount agreed for the transaction",
                        max_digits=12,
                    ),
                ),
                (
                    "release_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Amount released so far",
                        max_digits=12,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("disputed", "Disputed"),
                            ("canceled", "Canceled"),
                        ],
                        default="active",
                        help_text="Current status of the transaction",
                        max_length=20,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        help_text="Timestamp when the transaction was created"
                    ),
                ),
                (
                    "last_updated",
                    models.DateTimeField(
                        help_text="Timestamp when the transaction was last updated"
                    ),
                ),
                (
                    "buyer",
                    models.ForeignKey(
                        help_text="User who buys the crops",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="purchases",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "seller",
                    models.ForeignKey(
                        help_text="User who sells the crops",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sales",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Milestone",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(help_text="Milestone name", max_length=255)),
                (
                    "description",
                    models.TextField(help_text="Detailed description of the milestone"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount tied to this milestone",
                        max_digits=12,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("released", "Released"),
                            ("completed", "Completed"),
                            ("disputed", "Disputed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        help_text="Current fulfillment status of the milestone",
                        max_length=20,
                    ),
                ),
                (
                    "completed_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date when the milestone was completed",
                        null=True,
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="milestones",
                        to="proof_of_unlock.proofofunlock",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "filename",
                    models.CharField(help_text="Name of the file", max_length=255),
                ),
                (
                    "file_type",
                    models.CharField(
                        help_text="MIME type of the document", max_length=50
                    ),
                ),
                (
                    "file_hash",
                    models.CharField(
                        help_text="Hash of the document for verification", max_length=64
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="proof_of_unlock.proofofunlock",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ProofOfUnlockCrop",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount of crop in the specified unit",
                        max_digits=10,
                    ),
                ),
                (
                    "unit",
                    models.CharField(
                        help_text="Unit of measurement (e.g., 'tonnes')", max_length=20
                    ),
                ),
                (
                    "crop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="crops.crops"
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transaction_crops",
                        to="proof_of_unlock.proofofunlock",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="proofofunlock",
            name="crops",
            field=models.ManyToManyField(
                related_name="transactions",
                through="proof_of_unlock.ProofOfUnlockCrop",
                to="crops.crops",
            ),
        ),
    ]
