name: Release

on:
  push:
    branches:
      - main
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - '.vscode/**'
      - '.idea/**'

permissions:
  contents: read
  packages: read

jobs:
  sasya-contracts-validate:
    name: Run sasya-contracts release
    secrets: inherit
    uses: ./.github/workflows/sasya-contracts-ci.yaml

  sasya-contracts-release:
    name: Run sasya-contracts release
    secrets: inherit
    uses: ./.github/workflows/sasya-contracts-cd.yaml
    needs: sasya-contracts-validate

  sasya-frontend-validate:
    name: Run sasya-frontend release
    secrets: inherit
    uses: ./.github/workflows/sasya-frontend-ci.yaml

  sasya-frontend-release:
    name: Run sasya-frontend release
    secrets: inherit
    uses: ./.github/workflows/sasya-frontend-cd.yaml
    needs: sasya-frontend-validate
