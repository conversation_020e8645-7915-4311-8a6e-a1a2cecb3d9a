import apiClient from '@/services/apiClient'
import { algorand } from '@/utils/algo'
import { getAlgodConfigFromViteEnvironment } from '@/utils/network/getAlgoClientConfigs'
import { VITE_ALGORAND_ASSET_ID_KTT, VITE_ALGORAND_DECIMAL } from '@/utils/variable'
import { useWallet } from '@txnlab/use-wallet-react'
import { BrowserQRCodeReader } from '@zxing/browser'
import algosdk from 'algosdk'
import clsx from 'clsx'
import { ArrowLeft, Calendar, Copy, Loader2, MapPin, QrCode } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'
import { useSupplyChainClient } from '../utils/algo'

interface PledgeFormData {
  transaction_id: string
  unit_cost: number
  cost: number
  from_user: string
  to_user: string
  crop: {
    crop_id: number
    crop_grade: string
    quantity: number
    location: {
      address: string
      coordinates: {
        lat: number
        long: number
      }
    }
    growth_period: {
      start_date: string
      harvest_date: string
    }
    soil_type: string
    unit: 'kg' | 'tons'
    irrigation_type: string
    fertilizers_used: string[]
    certification: string
  }
  termsAccepted: boolean
}

interface TransferFormData {
  amount: string
  address: string
}

const GRADES = [
  { value: 'A', label: 'Grade A' },
  { value: 'B', label: 'Grade B' },
  { value: 'C', label: 'Grade C' },
]

const SOIL_TYPES = ['Clay', 'Sandy', 'Silty', 'Peaty', 'Chalky', 'Loamy']
const FERTILIZER_OPTIONS = ['Nitrogen', 'Phosphorus', 'Potassium', 'Organic', 'Biofertilizer']
const IRRIGATION_METHODS = ['Drip', 'Sprinkler', 'Flood', 'Center Pivot', 'Manual']

export function PledgePage() {
  const [isLoading, setIsLoading] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [showError, setShowError] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [errors, setErrors] = useState<Partial<TransferFormData>>({})
  const { activeAddress, transactionSigner: signer } = useWallet()
  const supplyChainClient = useSupplyChainClient()
  const [kttBalance, setKttBalance] = useState(0)
  const [formData, setFormData] = useState<PledgeFormData>({
    transaction_id: '',
    unit_cost: 0,
    cost: 0,
    from_user: '',
    to_user: '',
    crop: {
      crop_id: 1,
      crop_grade: '',
      quantity: 0,
      location: {
        address: '',
        coordinates: {
          lat: 0,
          long: 0,
        },
      },
      growth_period: {
        start_date: '',
        harvest_date: '',
      },
      soil_type: '',
      unit: 'tons',
      irrigation_type: '',
      fertilizers_used: [],
      certification: '',
    },
    termsAccepted: false,
  })
  const navigate = useNavigate()
  const kttId = parseInt(VITE_ALGORAND_ASSET_ID_KTT)

  useEffect(() => {
    const fetchAsaBalance = async () => {
      if (!activeAddress || !kttId) {
        console.warn('Active address or asset ID is missing.')
        return
      }

      try {
        const algodConfig = getAlgodConfigFromViteEnvironment()
        const algod = new algosdk.Algodv2(algodConfig.token as string, algodConfig.server, algodConfig.port)
        const accountInfo = await algod.accountInformation(activeAddress).do()
        const assets = accountInfo.assets ?? []
        const ktt = assets.find((a) => a.assetId === BigInt(kttId))

        setKttBalance(ktt ? Number(ktt.amount) / Math.pow(10, VITE_ALGORAND_DECIMAL || 0) : 0)
      } catch (error) {
        console.error('Error fetching ASA balance:', error)
        setKttBalance(0)
      }
    }

    fetchAsaBalance()
  }, [activeAddress, kttId])

  const startQRScanner = async () => {
    setIsScanning(true)
    const codeReader = new BrowserQRCodeReader()
    try {
      const videoInputDevices = await BrowserQRCodeReader.listVideoInputDevices()
      if (videoInputDevices.length > 0) {
        const result = await codeReader.decodeOnceFromVideoDevice(undefined, 'qr-video')
        if (result) {
          setFormData((prev) => ({ ...prev, to_user: result.getText() }))
        }
      }
    } catch (error) {
      toast.error('Failed to access camera')
    } finally {
      setIsScanning(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    // Add address validation
    if (!formData.crop.location.address) {
      setErrors({ address: 'Address is required' })
      setIsLoading(false)
      return
    }
    setErrors({})
    try {
      if (parseFloat(((formData.unit_cost || 0) * (formData.crop.quantity || 0)).toFixed(2)) <= kttBalance) {
        const productInfo = {
          farmerAddress: activeAddress as string,
          cost: BigInt((formData.unit_cost * VITE_ALGORAND_DECIMAL).toFixed(0)),
          cropGreade: formData.crop.crop_grade,
          quantity: BigInt((formData.crop.quantity * (formData.crop.unit === 'tons' ? 1_000_000 : 1_000)).toFixed(0)),
          location: {
            address: formData.crop.location.address,
            coordinates: {
              lat: String(formData.crop.location.coordinates.lat),
              long: String(formData.crop.location.coordinates.long),
            },
          },
          growthPeriod: {
            startDate: formData.crop.growth_period.start_date,
            harvestDate: formData.crop.growth_period.harvest_date,
          },
          soilType: formData.crop.soil_type,
          irrigationType: formData.crop.irrigation_type,
          fertilizersUsed: formData.crop.fertilizers_used,
          certification: formData.crop.certification,
        }
        const result = await supplyChainClient.send.registerCrop({
          args: [productInfo],
          note: 'Registering new crop',
        })
        const transaction = {
          product_id: BigInt(result.return ?? 0),
          framer_address: activeAddress as string,
          quantity: BigInt((formData.crop.quantity * (formData.crop.unit === 'tons' ? 1_000_000 : 1_000)).toFixed(0)),
        }
        algorand.account.setSigner(activeAddress as string, signer)
        await algorand
          .newGroup()
          .addAssetOptIn({
            sender: activeAddress as string,
            assetId: BigInt(result.return ?? 0),
          })
          .addAppCallMethodCall(
            await supplyChainClient.params.transferAsa({
              args: {
                productId: transaction.product_id,
                framerAddress: transaction.framer_address,
                quantity: transaction.quantity,
              },
            }),
          )
          .send()
        formData.transaction_id = result.txIds[0]
        formData.crop.crop_id = Number(result.return) || 0
        formData.to_user = activeAddress as string
        formData.cost = parseFloat(((formData.unit_cost || 0) * (formData.crop.quantity || 0)).toFixed(2))
        await apiClient.post('/crops/crop-transfers/', formData)
        await apiClient.post('/transaction/transfer-ktt/', {
          amount: formData.cost,
          reciver: formData.from_user,
        })
        setShowSuccess(true)
        setShowError(false)
        toast.success('Crop registered successfully!')
        navigate('/dashboard')
      } else {
        toast.error('Insufficient balance')
        console.error('Insufficient balance')
        setShowError(true)
        setShowSuccess(false)
      }
    } catch (error) {
      console.error('Error registering crop:', error)
      setShowError(true)
      setShowSuccess(false)
      toast.error('Failed to register crop: ' + (error instanceof Error ? error.message : String(error)))
    } finally {
      setIsLoading(false)
    }
  }

  // const calculateRewards = () => {
  //   // Mock reward calculation based on quantity and growth period
  //   const baseRate = 10 // tokens per ton
  //   const quantity = formData.crop.quantity * (formData.crop.unit === 'tons' ? 1 : 0.001)
  //   return Math.round(quantity * baseRate)
  // }

  return (
    <div className="min-h-screen bg-main-bg py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button onClick={() => window.history.back()} className="mr-4 text-secondary-text hover:text-primary-text transition-colors">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-3xl font-bold text-primary-text">Pledge Your Crops</h1>
        </div>
        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Crop Details */}
          <div className="bg-card-bg p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-primary-text mb-4">Crop Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Crop Grade */}
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Crop Grade *</label>
                <select
                  className="w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text"
                  value={formData.crop.crop_grade}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      crop: {
                        ...prev.crop,
                        crop_grade: e.target.value,
                      },
                    }))
                  }
                  required
                >
                  <option value="">Select crop grade</option>
                  {GRADES.map((grade) => (
                    <option key={grade.value} value={grade.value}>
                      {grade.label}
                    </option>
                  ))}
                </select>
              </div>
              {/* Price */}
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Price per Unit (KTT) *</label>
                <input
                  type="number"
                  min="0"
                  max="999999.99"
                  step="0.01"
                  className="w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text"
                  value={formData.unit_cost || ''}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value)
                    if (!isNaN(value) && value >= 0 && value <= 999999.99) {
                      setFormData({ ...formData, unit_cost: value })
                    }
                  }}
                  required
                  placeholder="0.00"
                />
              </div>
              {/* Quantity */}
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Quantity *</label>
                <div className="flex">
                  <input
                    type="number"
                    min="0"
                    max={formData.crop.unit === 'tons' ? 999999.99 : 999999999.99}
                    step="0.01"
                    className="flex-1 bg-alt-bg border border-card-border rounded-l-lg px-4 py-2 text-primary-text"
                    value={formData.crop.quantity || ''}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value)
                      const maxValue = formData.crop.unit === 'tons' ? 999999.99 : 999999999.99
                      if (!isNaN(value) && value >= 0 && value <= maxValue) {
                        setFormData({
                          ...formData,
                          crop: {
                            ...formData.crop,
                            quantity: value,
                          },
                        })
                      }
                    }}
                    required
                    placeholder="0.00"
                  />
                  <select
                    className="w-24 bg-alt-bg border border-card-border rounded-r-lg px-2 py-2 text-primary-text"
                    value={formData.crop.unit}
                    onChange={(e) => {
                      const newUnit = e.target.value as 'kg' | 'tons'
                      const maxValue = newUnit === 'tons' ? 999999.99 : 999999999.99
                      setFormData({
                        ...formData,
                        crop: {
                          ...formData.crop,
                          unit: newUnit,
                          quantity: Math.min(formData.crop.quantity, maxValue),
                        },
                      })
                    }}
                  >
                    <option value="kg">kg</option>
                    <option value="tons">tons</option>
                  </select>
                </div>
              </div>
            </div>
            {/* Total Value Display */}
            <div className="mt-4 p-4 bg-alt-bg border border:primary-text rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-secondary-text">Total Price (KTT):</span>
                <span className="text-xl font-bold text-accent">
                  {((formData.unit_cost || 0) * (formData.crop.quantity || 0)).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </span>
              </div>
            </div>
          </div>
          {/* Farmer Details */}
          <div className="bg-card-bg p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-primary-text mb-4">Farmer Details</h2>
            <div>
              <label className="block text-secondary-text text-sm mb-2">Farmer Address</label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.from_user || ''}
                  onChange={(e) => setFormData((prev) => ({ ...prev, from_user: e.target.value }))}
                  className={clsx(
                    'w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text',
                    errors.address && 'ring-2 ring-button-danger',
                  )}
                  placeholder="0x..."
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                  <button
                    type="button"
                    onClick={() => startQRScanner()}
                    className="text-accent hover:text-accent-hover"
                    title="Scan QR Code"
                    disabled={isScanning}
                  >
                    <QrCode size={20} />
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      navigator.clipboard.writeText(activeAddress || '')
                      toast.success('Address copied!')
                    }}
                    className="text-accent hover:text-accent-hover"
                    title="Copy Address"
                  >
                    <Copy size={20} />
                  </button>
                </div>
              </div>
              {errors.address && <p className="text-button-danger text-sm mt-1">{errors.address}</p>}
            </div>
          </div>
          {/* Location */}
          <div className="bg-card-bg p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-primary-text mb-4">Growth Location</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Address *</label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full bg-alt-bg border border-card-border rounded-lg pl-10 pr-4 py-2 text-primary-text"
                    value={formData.crop.location.address}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        crop: {
                          ...formData.crop,
                          location: {
                            ...formData.crop.location,
                            address: e.target.value,
                          },
                        },
                      })
                    }
                    required
                  />
                  <MapPin className="absolute left-3 top-2.5 h-5 w-5 text-secondary-text" />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-text mb-2">Latitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    className="w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text"
                    value={formData.crop.location.coordinates.lat}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        crop: {
                          ...prev.crop,
                          location: {
                            ...prev.crop.location,
                            coordinates: {
                              ...prev.crop.location.coordinates,
                              lat: Number(e.target.value),
                            },
                          },
                        },
                      }))
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-text mb-2">Longitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    className="w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text"
                    value={formData.crop.location.coordinates.long}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        crop: {
                          ...prev.crop,
                          location: {
                            ...prev.crop.location,
                            coordinates: {
                              ...prev.crop.location.coordinates,
                              long: Number(e.target.value),
                            },
                          },
                        },
                      }))
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          {/* Growth Period */}
          <div className="bg-card-bg p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-primary-text mb-4">Growth Period</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Start Date *</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full bg-alt-bg border border-card-border rounded-lg pl-10 pr-4 py-2 text-primary-text"
                    value={formData.crop.growth_period.start_date}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        crop: {
                          ...formData.crop,
                          growth_period: {
                            ...formData.crop.growth_period,
                            start_date: e.target.value,
                          },
                        },
                      })
                    }
                    required
                  />
                  <Calendar className="absolute left-3 top-2.5 h-5 w-5 text-secondary-text" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Expected Harvest Date *</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full bg-alt-bg border border-card-border rounded-lg pl-10 pr-4 py-2 text-primary-text"
                    value={formData.crop.growth_period.harvest_date}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        crop: {
                          ...formData.crop,
                          growth_period: {
                            ...formData.crop.growth_period,
                            harvest_date: e.target.value,
                          },
                        },
                      })
                    }
                    required
                  />
                  <Calendar className="absolute left-3 top-2.5 h-5 w-5 text-secondary-text" />
                </div>
              </div>
            </div>
          </div>
          {/* Additional Information */}
          <div className="bg-card-bg p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-primary-text mb-4">Additional Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Soil Type</label>
                <select
                  className="w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text"
                  value={formData.crop.soil_type}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      crop: {
                        ...prev.crop,
                        soil_type: e.target.value,
                      },
                    }))
                  }
                >
                  <option value="">Select soil type</option>
                  {SOIL_TYPES.map((soil) => (
                    <option key={soil} value={soil}>
                      {soil}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-text mb-2">Irrigation Method</label>
                <select
                  className="w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text"
                  value={formData.crop.irrigation_type}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      crop: {
                        ...prev.crop,
                        irrigation_type: e.target.value,
                      },
                    }))
                  }
                >
                  <option value="">Select irrigation method</option>
                  {IRRIGATION_METHODS.map((method) => (
                    <option key={method} value={method}>
                      {method}
                    </option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-secondary-text mb-2">Fertilizers Used</label>
                <div className="flex flex-wrap gap-2">
                  {FERTILIZER_OPTIONS.map((fertilizer) => (
                    <label key={fertilizer} className="inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-accent bg-alt-bg border-card-border rounded focus:ring-accent"
                        checked={formData.crop.fertilizers_used.includes(fertilizer)}
                        onChange={(e) => {
                          const newFertilizers = e.target.checked
                            ? [...formData.crop.fertilizers_used, fertilizer]
                            : formData.crop.fertilizers_used.filter((f) => f !== fertilizer)
                          setFormData({
                            ...formData,
                            crop: {
                              ...formData.crop,
                              fertilizers_used: newFertilizers,
                            },
                          })
                        }}
                      />
                      <span className="ml-2 text-sm text-primary-text">{fertilizer}</span>
                    </label>
                  ))}
                </div>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-secondary-text mb-2">Certifications</label>
                <textarea
                  className="w-full bg-alt-bg border border-card-border rounded-lg px-4 py-2 text-primary-text"
                  rows={3}
                  value={formData.crop.certification}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      crop: {
                        ...prev.crop,
                        certification: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter any certification details..."
                />
              </div>
            </div>
          </div>
          {/* Terms & Conditions */}
          <div className="bg-card-bg p-6 rounded-lg">
            <label className="flex items-start space-x-3">
              <input
                type="checkbox"
                className="form-checkbox h-4 w-4 text-accent bg-alt-bg border-card-border rounded focus:ring-accent"
                checked={formData.termsAccepted}
                onChange={(e) => setFormData({ ...formData, termsAccepted: e.target.checked })}
                required
              />
              <span className="text-sm text-secondary-text">
                I accept the{' '}
                <a href="#" className="text-accent hover:text-accent-hover">
                  terms and conditions
                </a>{' '}
                for pledging my crops and understand the reward calculation process.
              </span>
            </label>
          </div>
          {/* Success/Error Messages */}
          {showSuccess && (
            <div className="bg-status-active-bg text-status-active-text p-4 rounded-lg">
              Pledge submitted successfully! You will receive a confirmation email shortly.
            </div>
          )}
          {showError && (
            <div className="bg-button-danger text-white-text p-4 rounded-lg">
              An error occurred while submitting your pledge. Please try again.
            </div>
          )}
          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-accent text-primary-text px-6 py-3 rounded-lg hover:bg-accent-hover transition-colors duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Submitting...</span>
                </>
              ) : (
                <span>Submit Pledge</span>
              )}
            </button>
            <button
              type="button"
              onClick={() => window.history.back()}
              className="flex-1 sm:flex-none bg-card-bg text-primary-text px-6 py-3 rounded-lg hover:bg-alt-bg transition-colors duration-300"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
