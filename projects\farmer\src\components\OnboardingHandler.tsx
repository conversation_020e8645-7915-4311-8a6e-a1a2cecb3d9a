import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore, useIsOnboardingCompleted, useRequiresOnboarding } from '../stores/authStore'

/**
 * Example component showing how to handle onboarding completion
 * This would typically be used in your OnboardingFlow component
 */
const OnboardingHandler = () => {
  const navigate = useNavigate()
  const { updateUser } = useAuthStore()
  const requiresOnboarding = useRequiresOnboarding()
  const isOnboardingCompleted = useIsOnboardingCompleted()

  // Redirect if user has already completed onboarding
  useEffect(() => {
    if (isOnboardingCompleted) {
      navigate('/dashboard')
    }
  }, [isOnboardingCompleted, navigate])

  // Example function to complete onboarding when wallet is connected
  const handleWalletConnected = (walletAddress: string) => {
    // Update user with the wallet address
    updateUser({ account_address: walletAddress })

    // Navigate to dashboard after onboarding is complete
    navigate('/dashboard')
  }

  // Example function to handle opt-in completion
  const handleOptInCompleted = () => {
    updateUser({ opt_in: true })
  }

  if (!requiresOnboarding || isOnboardingCompleted) {
    // User doesn't need onboarding or has already completed it
    return null
  }

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Complete Your Setup</h2>
      <p className="text-gray-600 mb-6">Please connect your wallet to continue using the platform.</p>

      <div className="space-y-4">
        <button
          onClick={() => handleWalletConnected('EXAMPLE_WALLET_ADDRESS_123')}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Connect Wallet
        </button>

        <button
          onClick={handleOptInCompleted}
          className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          Complete Opt-in
        </button>
      </div>

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-800">
          <strong>Note:</strong> You need to connect your wallet before accessing other features.
        </p>
      </div>
    </div>
  )
}

export default OnboardingHandler
