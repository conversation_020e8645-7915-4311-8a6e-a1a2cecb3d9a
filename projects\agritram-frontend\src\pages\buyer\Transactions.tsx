import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Search, Filter, FileText, LockKeyhole, Key, AlertTriangle, CheckCircle2 } from 'lucide-react';
import AppLayout from '../../components/layout/AppLayout';
import TransactionCard from '../../components/transactions/TransactionCard';
import MilestoneProgress from '../../components/transactions/MilestoneProgress';
import { useTransactionStore, Transaction, Milestone } from '../../stores/transactionStore';

const Transactions = () => {
  const location = useLocation();
  const { transactions, activeTransaction, setActiveTransaction, loading, approveMilestone, releaseFunds, disputeMilestone, fetchTransactions } = useTransactionStore();
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Get transaction ID from URL query params if it exists
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const id = params.get('id');
    
    if (id) {
      setActiveTransaction(id);
    }
    
    fetchTransactions();
  }, [location.search, setActiveTransaction, fetchTransactions]);
  
  // Filter transactions based on status and search query
  const filteredTransactions = transactions.filter((tx) => {
    const matchesFilter = filter === 'all' || tx.status === filter;
    const matchesSearch = tx.productName.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         tx.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });
  
  const handleSelectTransaction = (transaction: Transaction) => {
    setActiveTransaction(transaction.id);
  };
  
  const handleApprove = (milestoneId: number) => {
    if (activeTransaction) {
      approveMilestone(activeTransaction.id, milestoneId);
    }
  };
  
  const handleRelease = (milestoneId: number) => {
    if (activeTransaction) {
      releaseFunds(activeTransaction.id, milestoneId);
    }
  };
  
  const handleDispute = (milestoneId: number) => {
    if (activeTransaction) {
      disputeMilestone(activeTransaction.id, milestoneId);
    }
  };
  
  const renderMilestoneActions = (milestone: Milestone) => {
    switch (milestone.status) {
      case 'pending':
        return (
          <div className="flex gap-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleApprove(milestone.id)}
              className="px-3 py-1 text-sm font-medium text-primary-700 bg-primary-50 rounded-lg hover:bg-primary-100"
            >
              Approve
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleDispute(milestone.id)}
              className="px-3 py-1 text-sm font-medium text-error-700 bg-error-50 rounded-lg hover:bg-error-100"
            >
              Dispute
            </motion.button>
          </div>
        );
      case 'verified':
        return (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleRelease(milestone.id)}
            className="px-3 py-1 text-sm font-medium text-success-700 bg-success-50 rounded-lg hover:bg-success-100"
          >
            Release Funds
          </motion.button>
        );
      case 'completed':
        return (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleRelease(milestone.id)}
            className="px-3 py-1 text-sm font-medium text-success-700 bg-success-50 rounded-lg hover:bg-success-100"
          >
            Release Funds
          </motion.button>
        );
      case 'released':
        return (
          <div className="flex items-center gap-2 text-success-700">
            <CheckCircle2 className="w-4 h-4" />
            <span className="text-sm font-medium">Funds Released</span>
          </div>
        );
      case 'disputed':
        return (
          <div className="flex items-center gap-2 text-error-700">
            <AlertTriangle className="w-4 h-4" />
            <span className="text-sm font-medium">In Dispute</span>
          </div>
        );
      default:
        return null;
    }
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-primary-100 text-primary-700">
            Active
          </span>
        );
      case 'completed':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-success-100 text-success-700">
            Completed
          </span>
        );
      case 'pending':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-secondary-100 text-secondary-700">
            Pending
          </span>
        );
      case 'disputed':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-error-100 text-error-700">
            Disputed
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-neutral-100 text-neutral-700">
            {status}
          </span>
        );
    }
  };
  
  return (
    <AppLayout title="Transactions">
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="lg:w-1/3">
          <div className="bg-white p-4 rounded-xl shadow-sm mb-6">
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full rounded-lg border border-neutral-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex flex-wrap gap-2 mb-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter('all')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  filter === 'all'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                All
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter('active')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  filter === 'active'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                Active
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter('pending')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  filter === 'pending'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                Pending
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter('completed')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  filter === 'completed'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                Completed
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter('disputed')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  filter === 'disputed'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                Disputed
              </motion.button>
            </div>
          </div>
          
          <div className="space-y-4">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
              </div>
            ) : filteredTransactions.length > 0 ? (
              filteredTransactions.map((transaction) => (
                <TransactionCard
                  key={transaction.id}
                  transaction={transaction}
                  onClick={() => handleSelectTransaction(transaction)}
                />
              ))
            ) : (
              <div className="bg-white rounded-xl shadow-sm p-8 text-center">
                <Filter className="w-8 h-8 text-neutral-400 mx-auto mb-2" />
                <p className="text-neutral-900 font-medium">No transactions found</p>
                <p className="text-neutral-500 text-sm">Try changing your search or filter</p>
              </div>
            )}
          </div>
        </div>
        
        <div className="lg:w-2/3">
          {activeTransaction ? (
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="p-6 border-b border-neutral-200">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <h2 className="text-xl font-semibold text-neutral-900">
                        {activeTransaction.productName}
                      </h2>
                      {getStatusBadge(activeTransaction.status)}
                    </div>
                    <p className="text-neutral-500">
                      {activeTransaction.quantity} units — ${activeTransaction.totalAmount.toLocaleString()}
                    </p>
                  </div>
                  
                  <div className="flex flex-col items-end">
                    <p className="text-sm text-neutral-500">Created on</p>
                    <p className="text-sm font-medium">
                      {new Date(activeTransaction.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-lg font-semibold text-neutral-900 mb-4">Contract Details</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="p-4 bg-neutral-50 rounded-lg">
                    <p className="text-sm font-medium text-neutral-500">Contract Address</p>
                    <p className="text-sm font-medium text-neutral-900 font-mono mt-1">
                      {activeTransaction.contractAddress}
                    </p>
                  </div>
                  
                  <div className="p-4 bg-neutral-50 rounded-lg">
                    <p className="text-sm font-medium text-neutral-500">Seller Address</p>
                    <p className="text-sm font-medium text-neutral-900 font-mono mt-1">
                      {activeTransaction.sellerAddress}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-neutral-900">Milestone Progress</h3>
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium text-success-700">
                      {activeTransaction.releaseAmount.toLocaleString()}
                    </span>
                    <span className="text-sm text-neutral-500">
                      / {activeTransaction.totalAmount.toLocaleString()} released
                    </span>
                  </div>
                </div>
                
                <MilestoneProgress milestones={activeTransaction.milestones} />
                
                <div className="mt-6">
                  <div className="space-y-4">
                    {activeTransaction.milestones.map((milestone) => (
                      <div
                        key={milestone.id}
                        className="p-4 border border-neutral-100 rounded-lg"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-neutral-900">{milestone.name}</h4>
                            <p className="text-sm text-neutral-500">{milestone.description}</p>
                          </div>
                          <p className="text-lg font-medium text-primary-700">${milestone.amount}</p>
                        </div>
                        
                        <div className="mt-4 flex flex-wrap justify-between items-center gap-2">
                          <div className="flex items-center">
                            <p className="text-xs text-neutral-500 mr-4">
                              Due: {new Date(milestone.dueDate).toLocaleDateString()}
                            </p>
                            {milestone.completedDate && (
                              <p className="text-xs text-success-700">
                                Completed: {new Date(milestone.completedDate).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                          
                          {renderMilestoneActions(milestone)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="mt-8">
                  <h3 className="text-lg font-semibold text-neutral-900 mb-4">Documents</h3>
                  
                  {activeTransaction.documents.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {activeTransaction.documents.map((document, index) => (
                        <motion.div
                          key={index}
                          whileHover={{ scale: 1.02 }}
                          className="flex items-center p-4 bg-neutral-50 rounded-lg cursor-pointer"
                        >
                          <FileText className="w-5 h-5 text-primary-600 mr-3" />
                          <span className="text-sm font-medium text-neutral-900">{document}</span>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-neutral-500">No documents available</p>
                  )}
                </div>
                
                {activeTransaction.status === 'active' && (
                  <div className="mt-8 bg-primary-50 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-full bg-primary-100 text-primary-700">
                        <LockKeyhole className="w-5 h-5" />
                      </div>
                      <div>
                        <p className="font-medium text-primary-900">Funds in Escrow</p>
                        <p className="text-sm text-primary-700 mt-1">
                          ${(activeTransaction.totalAmount - activeTransaction.releaseAmount).toLocaleString()} is currently locked in the smart contract escrow.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                
                {activeTransaction.status === 'disputed' && (
                  <div className="mt-8 bg-error-50 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-full bg-error-100 text-error-700">
                        <AlertTriangle className="w-5 h-5" />
                      </div>
                      <div>
                        <p className="font-medium text-error-900">Dispute in Progress</p>
                        <p className="text-sm text-error-700 mt-1">
                          This transaction is currently under dispute. Please contact support for resolution.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                
                {activeTransaction.status === 'completed' && (
                  <div className="mt-8 bg-success-50 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-full bg-success-100 text-success-700">
                        <CheckCircle2 className="w-5 h-5" />
                      </div>
                      <div>
                        <p className="font-medium text-success-900">Transaction Completed</p>
                        <p className="text-sm text-success-700 mt-1">
                          All funds have been released and the transaction is complete.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-sm p-12 text-center flex flex-col items-center justify-center h-full">
              <Key className="w-12 h-12 text-neutral-300 mb-4" />
              <h3 className="text-xl font-semibold text-neutral-900 mb-2">Select a Transaction</h3>
              <p className="text-neutral-500 max-w-md mx-auto">
                Choose a transaction from the list to view details, manage escrow funds, and track milestones.
              </p>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
};

export default Transactions;