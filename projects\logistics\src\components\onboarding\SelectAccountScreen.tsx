import apiClient from '@/services/apiClient'
import { useWallet } from '@txnlab/use-wallet-react'
import { <PERSON>ertCircle, CheckCircle2, Co<PERSON>, ExternalLink, User } from 'lucide-react'
import { useState } from 'react'
import { ellipseAddress } from '../../utils/ellipseAddress'

interface SelectAccountScreenProps {
  handleNext: () => void
  handleBack: () => void
  selectedAccount: string
  setSelectedAccount: (address: string) => void
}

/**
 * React component for selecting and confirming a blockchain account from connected wallets.
 *
 * Displays a list of available accounts, allows the user to select one, copy its address, and confirm the selection to proceed.
 *
 * @param handleNext - Callback invoked after successful account confirmation.
 * @param handleBack - Callback invoked when the user navigates back.
 * @param selectedAccount - The currently selected account address.
 * @param setSelectedAccount - Setter for updating the selected account address.
 *
 * @returns The rendered account selection screen.
 *
 * @remark Only the first connected wallet's accounts are displayed. The selected account is set as active in the wallet upon confirmation.
 */
export default function SelectAccountScreen({ handleNext, handleBack, selectedAccount, setSelectedAccount }: SelectAccountScreenProps) {
  const [copySuccess, setCopySuccess] = useState<string | null>(null)
  const { wallets } = useWallet()

  const handleAccountSelect = (address: string) => {
    setSelectedAccount(address)
  }

  const handleCopyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address)
      setCopySuccess(address)
      setTimeout(() => setCopySuccess(null), 2000)
    } catch (err) {
      console.error('Failed to copy address:', err)
    }
  }

  const handleAccountConfirm = async () => {
    try {
      if (selectedAccount) {
        console.log('Selected account:', selectedAccount)
        const response = await apiClient.post('/user/connect-account/', { account_address: selectedAccount })
        const activeWallet = wallets[0]
        if (activeWallet) {
          await activeWallet.connect()
          activeWallet.setActiveAccount(selectedAccount)
        }
        if (response.status !== 200) {
          throw new Error('Wallet connection failed')
        }
        handleNext()
      }
    } catch (error) {
      console.error('Wallet connection failed:', error)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">Select Account</h2>
        <p className="text-[#BDBDBD]">
          Choose which account you'd like to use with FarmChain. This account will be used for all transactions and asset management.
        </p>
      </div>

      <div className="bg-[#303030] p-6 rounded-lg space-y-6">
        <div className="space-y-4">
          {wallets[0]?.accounts?.map((account) => (
            <label
              key={account.address}
              className={`flex items-center gap-4 p-4 rounded-lg cursor-pointer transition-colors duration-200 ${
                selectedAccount === account.address
                  ? 'bg-[#1B5E20] bg-opacity-20 border border-[#00E676]'
                  : 'bg-[#212121] border border-transparent hover:border-[#424242]'
              }`}
            >
              <input
                type="radio"
                name="account"
                className="h-4 w-4 text-[#00E676] bg-[#303030] border-[#424242] focus:ring-[#00E676]"
                checked={selectedAccount === account.address}
                onChange={() => handleAccountSelect(account.address)}
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm text-[#BDBDBD] font-mono">{ellipseAddress(account.address)}</span>
                  <button
                    onClick={() => handleCopyAddress(account.address)}
                    className="text-[#BDBDBD] hover:text-white transition-colors p-1 rounded-md hover:bg-[#424242]"
                    title="Copy address"
                  >
                    {copySuccess === account.address ? <CheckCircle2 className="h-4 w-4 text-[#00E676]" /> : <Copy className="h-4 w-4" />}
                  </button>
                  <a
                    href={`https://lora.algokit.io/localnet/account/${account.address}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#BDBDBD] hover:text-white transition-colors p-1 rounded-md hover:bg-[#424242]"
                    title="View on explorer"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </div>
              </div>
            </label>
          ))}
        </div>

        {!selectedAccount && (
          <div className="flex items-start gap-3 p-4 bg-[#212121] rounded-lg text-[#BDBDBD]">
            <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
            <p className="text-sm">
              Please select an account to continue. Make sure the selected account has sufficient ALGO balance for transactions and asset
              opt-ins.
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-[#424242] text-[#BDBDBD] hover:text-white hover:border-white transition-colors duration-300"
        >
          Back
        </button>
        <button
          onClick={handleAccountConfirm}
          disabled={!selectedAccount}
          className="bg-[#1B5E20] text-white px-6 py-2 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <User className="h-5 w-5 mr-2" />
          Continue with Selected Account
        </button>
      </div>
    </div>
  )
}
