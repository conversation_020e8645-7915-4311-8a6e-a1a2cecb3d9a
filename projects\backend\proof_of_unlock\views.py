from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.response import Response
from rest_framework import status
from decimal import Decimal
from django.db import transaction
from typing import Dict, Any

from crops.models import CropTransfer
from inventory.models import InventoryCropStatus, InventoryQuantity
from .serializers import ProofOfUnlockSerializer, MilestoneSerializer
from django.utils import timezone
from .models import Milestone, ProofOfUnlock
from django.shortcuts import get_object_or_404
from crops.serializers import CropTransferSerializer
from user.models import User


# Create your views here.
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def create_proof_of_unlock(request) -> Response:
    """
    Create a new proof of unlock transaction with optimized database operations and error handling.
    
    Args:
        request: The HTTP request containing transaction data
        
    Returns:
        Response: HTTP response with created transaction data or error message
    """
    try:
        # Prepare request data with default values
        request_data = _prepare_request_data(request.data)
        print('Request data', request_data)
        
        # Validate and create crop transfer
        crop_transfer = _create_crop_transfer(request_data)
        if not crop_transfer:
            return Response(
                {"error": "Invalid crop transfer data"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Process the transaction within a database transaction
        with transaction.atomic():
            # Create proof of unlock
            proof_of_unlock = _create_proof_of_unlock(request_data)
            
            # Update inventory
            _update_inventory(request_data)
            
            return Response(
                ProofOfUnlockSerializer(proof_of_unlock).data,
                status=status.HTTP_201_CREATED
            )
            
    except User.DoesNotExist:
        return Response(
            {"error": "Seller with the provided account address not found"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        print('Error', e)
        return Response(
            {"error": str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )

def _prepare_request_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare and validate request data with default values."""
    data["created_at"] = timezone.now()
    data["last_updated"] = timezone.now()
    data.setdefault("release_amount", 0)
    data.setdefault("status", "active")
    return data

def _create_crop_transfer(data: Dict[str, Any]) -> bool:
    """Create and validate crop transfer."""
    crop_transfer = CropTransfer.objects.create(
        transaction_id=data["tx_id"],
        crop_id=data["crops"][0]["crop_id"],
        from_user=User.objects.get(account_address=data["seller"]),
        to_user=User.objects.get(account_address=data["buyer"]),
        cost=data["total_amount"],
        status="PENDING"
    )
    
    if crop_transfer:
        return True
    return False

def _create_proof_of_unlock(data: Dict[str, Any]) -> ProofOfUnlock:
    """Create proof of unlock transaction."""
    serializer = ProofOfUnlockSerializer(data=data)
    if not serializer.is_valid():
        raise ValueError(serializer.errors)
    return serializer.save()

def _update_inventory(data: Dict[str, Any]) -> None:
    """Update inventory quantities and status."""
    seller = User.objects.get(
        account_address=data["seller"]
    )
    
    quantity = Decimal(str(data["crops"][0]["quantity"]))
    crop_id = data["crops"][0]["crop_id"]
    
    # Update inventory quantity
    inventory_quantity = InventoryQuantity.objects.get(
        trader=seller.id,
    )
    inventory_quantity.ready_to_sell_quantity -= quantity
    inventory_quantity.ready_to_sell_batches -= 1
    inventory_quantity.sold_quantity += quantity
    inventory_quantity.sold_batches += 1
    inventory_quantity.save()
    
    # Update inventory crop status
    inventory_crop_status = InventoryCropStatus.objects.select_related('crop').get(
        trader=seller.id,
        crop=crop_id
    )
    inventory_crop_status.status = "in_progress"
    inventory_crop_status.save()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_proof_of_unlock_by_user(request):
    """
    Retrieve all milestones for a given user ID.
    """
    try:
        # Filter transactions where the user is either the buyer or the seller
        if request.user.role == "manufacturer":
            transactions = ProofOfUnlock.objects.filter(
                buyer_id=request.user.id
            ).order_by('-created_at')
            serializer = ProofOfUnlockSerializer(transactions, many=True)
        elif request.user.role == "trader":
            transactions = ProofOfUnlock.objects.filter(
                seller_id=request.user.id
            ).order_by('-created_at')  
            serializer = ProofOfUnlockSerializer(transactions, many=True)
            
        else:
            return Response(
                {"error": "User is not a manufacturer or trader."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            serializer.data,
            status=status.HTTP_200_OK,
        )
    except ProofOfUnlock.DoesNotExist:
        return Response(
            {"error": "No transactions found for this user."},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def update_milestone(request):
    """
    Update milestone status and transaction status.

    Expected request data:
    {
        "milestone": {
            "id": "milestone_id",
            "status": "new_status"
        },
        "tx_id": "transaction_id",
        "status": "new_transaction_status"
    }
    """
    try:
        # Get milestone and transaction
        milestone = get_object_or_404(Milestone, id=request.data["milestone"]["id"])
        transaction : ProofOfUnlock = get_object_or_404(ProofOfUnlock, tx_id=request.data["tx_id"])

        # Verify milestone belongs to the transaction
        if milestone.transaction != transaction:
            return Response(
                {"error": "Milestone does not belong to the specified transaction"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Update milestone status
        milestone.status = request.data["milestone"]["status"]
        if request.data["milestone"]["status"] == "completed":
            milestone.completed_date = timezone.now()
        milestone.save()
        if request.data["milestone"]["status"] == "released":
            transaction.release_amount = transaction.total_amount + milestone.amount
            transaction.save()
        
        # Update transaction status if provided
        if "status" in request.data and request.data["status"] == "completed":
            transaction.status = request.data["status"]
            transaction.last_updated = timezone.now()
            transaction.save()
            # TODO: Update inventory crop status
            inventory_crop_status = InventoryCropStatus.objects.select_for_update().get(
                trader=transaction.seller.id,
                crop=transaction.crops[0].crop_id
            )
            inventory_crop_status.status = "sold"
            inventory_crop_status.save()
            # TODO: Update crop transfer status
            crop_transfer = CropTransfer.objects.select_for_update().get(
                transaction_id=transaction.tx_id,
                crop_id=transaction.crops[0].crop_id
            )
            crop_transfer.status = "COMPLETED"
            crop_transfer.save()
            

        # Return updated milestone data
        serializer = MilestoneSerializer(milestone)
        return Response(serializer.data, status=status.HTTP_200_OK)

    except KeyError as e:
        print('KeyError', e)
        return Response(
            {"error": f"Missing required field: {str(e)}"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
