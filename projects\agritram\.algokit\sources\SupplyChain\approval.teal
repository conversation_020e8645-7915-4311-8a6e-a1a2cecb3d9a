#pragma version 10
#pragma typetrack false


main:
    intcblock 1 0 4
    bytecblock "product_count" 0x151f7c75
    txn ApplicationID
    bnz main_after_if_else@2


    bytec_0
    intc_1
    app_global_put

main_after_if_else@2:


    txn NumAppArgs
    bz main_bare_routing@12
    pushbytess 0x813520f5 0x51257a47 0x0c4cc514 0xbf6cdf64 0x4a0c1694 0x33b3499e 0xb53e2593
    txna ApplicationArgs 0
    match main_register_crop_route@5 main_opt_in_asa_route@6 main_transfer_asa_route@7 main_get_product_info_route@8 main_get_product_count_route@9 main_delete_application_route@10 main_update_application_route@11

main_after_if_else@14:


    intc_1
    return

main_update_application_route@11:


    txn OnCompletion
    intc_2
    ==
    assert
    txn ApplicationID
    assert
    callsub update_application
    intc_0
    return

main_delete_application_route@10:


    txn OnCompletion
    pushint 5
    ==
    assert
    txn ApplicationID
    assert
    callsub delete_application
    intc_0
    return

main_get_product_count_route@9:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert
    callsub get_product_count
    itob
    bytec_1
    swap
    concat
    log
    intc_0
    return

main_get_product_info_route@8:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    btoi


    callsub get_product_info
    bytec_1
    swap
    concat
    log
    intc_0
    return

main_transfer_asa_route@7:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    btoi
    txna ApplicationArgs 2
    btoi
    txnas Accounts
    txna ApplicationArgs 3


    callsub transfer_asa
    intc_0
    return

main_opt_in_asa_route@6:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    btoi
    txna ApplicationArgs 2
    btoi
    txnas Accounts


    callsub opt_in_asa
    intc_0
    return

main_register_crop_route@5:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1


    callsub register_crop
    itob
    bytec_1
    swap
    concat
    log
    intc_0
    return

main_bare_routing@12:


    txn OnCompletion
    bnz main_after_if_else@14
    txn ApplicationID
    !
    assert
    intc_0
    return



register_crop:






    proto 1 1












    itxn_begin


    global MinTxnFee


    frame_dig -1
    pushint 42
    extract_uint64


    intc_1
    bytec_0
    app_global_get_ex
    assert
    itob


    global CurrentApplicationAddress




    dupn 3
    itxn_field ConfigAssetClawback
    itxn_field ConfigAssetFreeze
    itxn_field ConfigAssetReserve
    itxn_field ConfigAssetManager
    itxn_field ConfigAssetName


    pushbytes "CROP"
    itxn_field ConfigAssetUnitName


    intc_1
    itxn_field ConfigAssetDecimals
    itxn_field ConfigAssetTotal


    pushint 3
    itxn_field TypeEnum
    itxn_field Fee












    itxn_submit
    itxn CreatedAssetID


    dup
    itob


    frame_dig -1
    box_put


    intc_1
    bytec_0
    app_global_get_ex
    assert
    intc_0
    +
    bytec_0
    swap
    app_global_put


    retsub



opt_in_asa:







    proto 2 0







    itxn_begin


    global MinTxnFee


    intc_1
    itxn_field AssetAmount
    frame_dig -2
    itxn_field XferAsset
    frame_dig -1
    itxn_field AssetReceiver


    intc_2
    itxn_field TypeEnum
    itxn_field Fee







    itxn_submit


    retsub



transfer_asa:








    proto 3 0







    itxn_begin


    global MinTxnFee


    frame_dig -1
    btoi
    itxn_field AssetAmount
    frame_dig -3
    itxn_field XferAsset
    frame_dig -2
    itxn_field AssetReceiver


    intc_2
    itxn_field TypeEnum
    itxn_field Fee







    itxn_submit


    retsub



get_product_info:



    proto 1 1


    frame_dig -1
    itob


    box_get
    pop


    retsub



get_product_count:


    intc_1
    bytec_0
    app_global_get_ex
    assert
    retsub



delete_application:


    txn Sender
    global CreatorAddress
    ==
    assert
    retsub



update_application:


    txn Sender
    global CreatorAddress
    ==
    assert
    retsub