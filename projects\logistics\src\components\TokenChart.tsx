import { Card } from '@/components/ui/card'
import apiClient from '@/services/apiClient'
import { useEffect, useState } from 'react'
import { Area, AreaChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

export const TokenChart = () => {
  const [data, setData] = useState([])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await apiClient.get('/transaction/monthly-expenses-minting/')
        const result = await response.data
        console.log(result)

        // Map the fetched data to the format required for the chart
        const chartData = result.map((item) => ({
          date: item.month_name,
          tokens: parseFloat(item.total_amount).toFixed(2), // Convert total_amount to a number
        }))

        setData(chartData)
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }

    fetchData()
  }, [])

  return (
    <Card className="p-6 bg-secondary-bg border-none h-[400px]">
      <h3 className="text-primary-text text-lg font-medium mb-4">Token History</h3>
      <ResponsiveContainer className="pb-8" width="100%" height="100%">
        <AreaChart data={data}>
          <defs>
            <linearGradient id="tokenGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#00E676" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#00E676" stopOpacity={0} />
            </linearGradient>
          </defs>
          <XAxis dataKey="date" stroke="#BDBDBD" tick={{ fill: '#BDBDBD' }} />
          <YAxis stroke="#BDBDBD" tick={{ fill: '#BDBDBD' }} />
          <Tooltip
            contentStyle={{
              backgroundColor: '#303030',
              border: 'none',
              borderRadius: '8px',
              color: '#FFFFFF',
            }}
          />
          <Area type="monotone" dataKey="tokens" stroke="#00E676" fillOpacity={1} fill="url(#tokenGradient)" />
        </AreaChart>
      </ResponsiveContainer>
    </Card>
  )
}
