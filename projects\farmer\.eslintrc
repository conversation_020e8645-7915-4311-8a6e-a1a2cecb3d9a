{"root": true, "env": {"node": true}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "rules": {"prettier/prettier": "warn", "no-console": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"ignoreRestSiblings": true, "argsIgnorePattern": "^_", "destructuredArrayIgnorePattern": "^_"}], "prefer-template": "error"}}