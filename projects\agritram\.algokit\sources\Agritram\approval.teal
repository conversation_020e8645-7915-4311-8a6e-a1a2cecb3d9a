#pragma version 10
#pragma typetrack false


main:


    txn NumAppArgs
    bz main_bare_routing@6
    pushbytes 0x02bece11
    txna ApplicationArgs 0
    match main_hello_route@3

main_after_if_else@10:


    pushint 0
    return

main_hello_route@3:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    extract 2 0


    callsub hello
    dup
    len
    itob
    extract 6 2
    swap
    concat
    pushbytes 0x151f7c75
    swap
    concat
    log
    pushint 1
    return

main_bare_routing@6:


    txn OnCompletion
    bnz main_after_if_else@10
    txn ApplicationID
    !
    assert
    pushint 1
    return



hello:



    proto 1 1


    pushbytes "Hello, "
    frame_dig -1
    concat
    retsub