import { useWallet, type Wallet as WalletType } from '@txnlab/use-wallet-react'
import { Loader2, Wallet, X } from 'lucide-react'
import React, { useEffect, useState } from 'react'

interface WalletModalProps {
  isOpen: boolean
  onClose: () => void
  isLoading: boolean
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
  handleNext: () => void
}

const WalletModal: React.FC<WalletModalProps> = ({ isOpen, onClose, isLoading, setIsLoading, handleNext }) => {
  const { wallets: providers, activeAddress } = useWallet()
  const [isConnecting, setIsConnecting] = useState(false)
  const isKmd = (provider: WalletType) => provider.metadata.name.toLowerCase() === 'kmd'

  // Use effect to handle the API call when activeAddress changes
  useEffect(() => {
    const connectWalletToBackend = async () => {
      if (isConnecting && activeAddress) {
        setIsConnecting(false)
        try {
          handleNext()
        } catch (error) {
          console.error('Wallet connection failed:', error)
        } finally {
          setIsLoading(false)
        }
      }
    }

    connectWalletToBackend()
  }, [activeAddress, isConnecting, setIsLoading, handleNext])

  const handleConnectWallet = async () => {
    setIsLoading(true)
    setIsConnecting(true)
    try {
      if (providers && providers.length > 0 && isKmd(providers[0])) {
        await providers[0].connect()
        // The API call will be handled by the useEffect
      }
    } catch (error) {
      console.error('Wallet connection failed:', error)
      setIsLoading(false)
      setIsConnecting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-primary-text bg-opacity-50">
      <div className="bg-alt-bg p-8 rounded-lg max-w-md w-full relative shadow-lg">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-primary-text opacity-60 hover:text-primary-text hover:opacity-100 transition-colors"
        >
          <X className="h-6 w-6" />
        </button>
        <h2 className="text-2xl font-bold text-primary-text mb-6 text-center">Connect Your Wallet</h2>
        <div className="space-y-4">
          {!activeAddress ? (
            providers?.map((provider) => (
              <button
                key={provider.metadata.name}
                onClick={handleConnectWallet}
                disabled={isLoading}
                className="w-full bg-button-bg text-button-text px-6 py-3 rounded-lg hover:bg-button-bg-hover transition-colors duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="animate-spin h-5 w-5 mr-2" />
                    Connecting...
                  </>
                ) : (
                  <>
                    {!isKmd(provider) && (
                      <img
                        alt={`wallet_icon_${provider.metadata.name}`}
                        src={provider.metadata.icon}
                        style={{ objectFit: 'contain', width: '30px', height: 'auto' }}
                        className="mr-2"
                      />
                    )}
                    <>
                      {isKmd(provider) ? (
                        <>
                          <Wallet className="h-5 w-5 mr-2" />
                          LocalNet Wallet
                        </>
                      ) : (
                        provider.metadata.name
                      )}
                    </>
                  </>
                )}
              </button>
            ))
          ) : (
            <>
              <button
                onClick={() => {
                  if (providers) {
                    const activeProvider = providers.find((p) => p.isActive)
                    if (activeProvider) {
                      activeProvider.disconnect()
                    } else {
                      localStorage.removeItem('txnlab-use-wallet')
                      window.location.reload()
                    }
                  }
                }}
                disabled={isLoading}
                className="w-full bg-button-danger text-button-text px-6 py-3 rounded-lg hover:bg-button-danger-hover transition-colors duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="animate-spin h-5 w-5 mr-2" />
                    Disconnecting...
                  </>
                ) : (
                  <>Log out</>
                )}
              </button>
              <button
                onClick={() => {
                  handleNext()
                }}
                disabled={isLoading}
                className="w-full bg-button-bg text-button-text px-6 py-3 rounded-lg hover:bg-button-bg-hover transition-colors duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default WalletModal
