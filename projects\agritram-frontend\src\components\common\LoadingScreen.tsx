import { Loader2 } from 'lucide-react';

const LoadingScreen = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-neutral-50">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="w-12 h-12 text-primary-500 animate-spin" />
        <p className="text-lg font-medium text-neutral-700">Loading...</p>
      </div>
    </div>
  );
};

export default LoadingScreen;