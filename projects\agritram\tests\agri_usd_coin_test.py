import logging
from unittest import result
import pytest
from algokit_utils import (
    AlgoAmount,
    AlgorandClient,
    AppFactoryDeployResult,
    SendSingleTransactionResult,
    SigningAccount,
    PaymentParams,
    OnUpdate,
    OnSchemaBreak,
    CommonAppCallParams,
)
from algosdk.abi.string_type import StringType

from smart_contracts.artifacts.agri_usd.agri_usd_coin_client import (
    AgriUsdCoinClient,
    AgriUsdCoinFactory,
    MintTokensArgs,
    BurnTokensArgs,
    TransferTokensArgs,
)

from algosdk.v2client.algod import AlgodClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@pytest.fixture(scope="session")
def factory_agri_usd_coin(algorand_client: AlgorandClient, deployer: SigningAccount) -> AgriUsdCoinFactory:
    return algorand_client.client.get_typed_app_factory(
        AgriUsdCoinFactory,
        default_sender=deployer.address,
    )

@pytest.fixture(scope="function")
def deploy_agri_usd_coin(factory_agri_usd_coin: AgriUsdCoinFactory) -> tuple[AgriUsdCoinClient, AppFactoryDeployResult]:
    result = factory_agri_usd_coin.deploy(
        on_update=OnUpdate.ReplaceApp,
        on_schema_break=OnSchemaBreak.ReplaceApp,
    )
    assert result is not None, "Deploy result should not be None"
    client, deploy_result = result
    logger.info(f"App ID: {deploy_result.app.app_id}")
    logger.info(f"App Address: {deploy_result.app.app_address}")
    return result

@pytest.fixture(scope="function")
def agri_usd_coin_payment(
    algorand_client: AlgorandClient,
    deployer: SigningAccount,
    deploy_agri_usd_coin: tuple[AgriUsdCoinClient, AppFactoryDeployResult],
) -> SendSingleTransactionResult:
    _, deploy_result = deploy_agri_usd_coin
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_address, "App address not found"
    
    try:
        payment_result = algorand_client.send.payment(
            PaymentParams(
                amount=AlgoAmount(algo=100),
                sender=deployer.address,
                receiver=deploy_result.app.app_address,
            )
        )
        return payment_result
    except Exception as e:
        if "transaction already in ledger" in str(e):
            logger.info("Payment transaction already exists")
            # Create a mock result since the transaction already exists
            return SendSingleTransactionResult(
                transaction=None,
                tx_id=None,
                confirmation=None,
                abi_return=None,
            )
        raise e

def test_create_agri_usd_coin(
    deployer: SigningAccount,
    deploy_agri_usd_coin: tuple[AgriUsdCoinClient, AppFactoryDeployResult],
    agri_usd_coin_payment: SendSingleTransactionResult,
) -> None:
    # Arrange
    client, deploy_result = deploy_agri_usd_coin

    # Verify the app was deployed successfully
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_id > 0, "Invalid app ID"
    assert deploy_result.app.app_address, "App address not found"
    logger.info(f"Deployed app at {deploy_result.app.app_address}")
    logger.info(f"Deployed app id {deploy_result.app.app_id}")
    logger.info(f"Deployed app version {deploy_result.app.version}")
    
    
    if agri_usd_coin_payment.transaction is not None:
        assert agri_usd_coin_payment.tx_id is not None, "Payment transaction ID not found"
    
    asset_id = client.send.get_asset_id(
        params=CommonAppCallParams(
            sender=deployer.address,
            signer=deployer.signer,
        ),
    )
    logger.info(f"Existing ASA ID: {asset_id.abi_return}")
    # Act: Create the ASA
    if asset_id.abi_return == 0:
        logger.info("Creating Agri USD Coin ASA")
        # Create ASA using the new recommended approach
        create_result = client.send.create(
            params=CommonAppCallParams(
                sender=deployer.address,
                signer=deployer.signer,
                note="Creating ASA",
            ),
        )
        assert create_result.transaction is not None, "ASA creation failed"
        assert create_result.tx_id is not None, "ASA creation transaction ID not found"
    else:
        logger.info("ASA already exists")
        # Get the asset ID to verify it exists
        asset_id = client.send.get_asset_id(
            params=CommonAppCallParams(
                sender=deployer.address,
                signer=deployer.signer,
            ),
        )
        assert asset_id.abi_return is not None, "Failed to get asset ID"
        assert asset_id.abi_return > 0, "Invalid asset ID"
        logger.info(f"Existing ASA ID: {asset_id.abi_return}")
        
def test_opt_in_asa(
    deployer: SigningAccount,
    deploy_agri_usd_coin: tuple[AgriUsdCoinClient, AppFactoryDeployResult],
    algorand_client: AlgorandClient,
) -> None:
    # Arrange
    client, deploy_result = deploy_agri_usd_coin

    # Verify the app was deployed successfully
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_id > 0, "Invalid app ID"
    assert deploy_result.app.app_address, "App address not found"

    # Get the asset ID
    asset_id = client.send.get_asset_id(
        params=CommonAppCallParams(
            sender=deployer.address,
            signer=deployer.signer,
        ),
    )
    assert asset_id.abi_return is not None, "Failed to get asset ID"
    assert asset_id.abi_return > 0, "Invalid asset ID"
    logger.info(f"Asset ID: {asset_id.abi_return}")
    
    try:
        # Opt-in the app account to the ASA using the new recommended approach
        result = algorand_client.asset.bulk_opt_in(
            account=deployer,
            asset_ids=[asset_id.abi_return],
        )

        # Assert opt-in was successful
        assert len(result) > 0, "No opt-in results returned"
        assert result[0].asset_id == asset_id.abi_return, "Asset ID mismatch"
        assert result[0].transaction_id, "Transaction ID not found"
        if result[0].transaction_id is not None:
            logger.info(f"Opt-in return value: {result[0].transaction_id}")
    except Exception as e:
        if "transaction already in ledger" in str(e):
            logger.info("Account already opted in to the asset")
        else:
            raise e


def test_deploy_and_mint_tokens(
    deployer: SigningAccount,
    deploy_agri_usd_coin: tuple[AgriUsdCoinClient, AppFactoryDeployResult],
) -> None:
    # Arrange
    client, deploy_result = deploy_agri_usd_coin
    
    # Verify the app was deployed successfully
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_id > 0, "Invalid app ID"
    assert deploy_result.app.app_address, "App address not found"
    
    # Create mint tokens data
    mint_tokens = MintTokensArgs(
        amount=1000_00,  
        receiver=deployer.address,
    )

    # Act: Mint the tokens
    mint_result = client.send.mint_tokens(
        args=mint_tokens,
        params=CommonAppCallParams(
            sender=deployer.address,
            signer=deployer.signer,
            note="Minting tokens",
        ),
    )
    
    # Assert: Verify the minting transaction
    assert mint_result.transaction is not None, "Minting transaction failed"
    assert mint_result.tx_id is not None, "Minting transaction ID not found"
    logger.info(f"Minting transaction ID: {mint_result.tx_id}")
    
def test_burn_tokens(
    deployer: SigningAccount,
    deploy_agri_usd_coin: tuple[AgriUsdCoinClient, AppFactoryDeployResult],
) -> None:
    # Arrange
    client, deploy_result = deploy_agri_usd_coin
    
    # Verify the app was deployed successfully
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_id > 0, "Invalid app ID"
    assert deploy_result.app.app_address, "App address not found"

    # Create burn tokens data
    burn_tokens = BurnTokensArgs(
        amount=100,
        address=deployer.address,
    )

    # Act: Burn the tokens
    burn_result = client.send.burn_tokens(
        args=burn_tokens,
        params=CommonAppCallParams(
            sender=deployer.address,
            signer=deployer.signer,
            note="Burning tokens",
        ),
    )

    # Assert: Verify the burning transaction
    assert burn_result.transaction is not None, "Burning transaction failed"
    assert burn_result.tx_id is not None, "Burning transaction ID not found"
    logger.info(f"Burning transaction ID: {burn_result.tx_id}")
    
def test_transfer_tokens(
    deployer: SigningAccount,
    deploy_agri_usd_coin: tuple[AgriUsdCoinClient, AppFactoryDeployResult],
) -> None:
    # Arrange
    client, deploy_result = deploy_agri_usd_coin
    
    # Verify the app was deployed successfully
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_id > 0, "Invalid app ID"
    assert deploy_result.app.app_address, "App address not found"

   
    # Create transfer tokens data
    transfer_tokens = TransferTokensArgs(
        amount=10,
        receiver=deployer.address,
        account=deployer.address,
    )

    # Act: Transfer the tokens
    transfer_result = client.send.transfer_tokens(
        args=transfer_tokens,
        params=CommonAppCallParams(
            sender=deployer.address,
            signer=deployer.signer,
            note="Transferring tokens",
        ),
    )

    # Assert: Verify the transfer transaction
    assert transfer_result.transaction is not None, "Transfer transaction failed"
    assert transfer_result.tx_id is not None, "Transfer transaction ID not found"
    logger.info(f"Transfer transaction ID: {transfer_result.tx_id}")

