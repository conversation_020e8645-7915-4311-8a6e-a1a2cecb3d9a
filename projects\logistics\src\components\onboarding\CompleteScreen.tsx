import { useWallet } from '@txnlab/use-wallet-react'
import { CheckCircle2, LayoutDashboard } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface CompleteScreenProps {
  handleBack: () => void
  selectedAccount: string
}

/**
 * Displays a confirmation screen indicating successful setup completion, summarizing completed steps and providing navigation options.
 *
 * @param handleBack - Callback invoked when the user clicks the "Back" button.
 * @param selectedAccount - The address of the account selected during setup, used to display the corresponding wallet name.
 */
export default function CompleteScreen({ handleBack, selectedAccount }: CompleteScreenProps) {
  const navigate = useNavigate()
  const { wallets } = useWallet()
  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <div className="w-16 h-16 bg-[#1B5E20] rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle2 className="h-8 w-8 text-[#00E676]" />
        </div>
        <h2 className="text-3xl font-bold text-white mb-4">Setup Complete!</h2>
        <p className="text-[#BDBDBD]">You're all set to start using FarmChain. Here's what you've completed:</p>
      </div>

      <div className="bg-[#303030] p-6 rounded-lg space-y-4">
        <div className="flex items-center gap-3 text-[#00E676]">
          <CheckCircle2 className="h-5 w-5" />
          <span>Wallet connected successfully</span>
        </div>
        <div className="flex items-center gap-3 text-[#00E676]">
          <CheckCircle2 className="h-5 w-5" />
          <span>Account selected: {wallets.find((wallet) => wallet.accounts[0].address === selectedAccount)?.metadata.name}</span>
        </div>
        <div className="flex items-center gap-3 text-[#00E676]">
          <CheckCircle2 className="h-5 w-5" />
          <span>Smart contract approved</span>
        </div>
        <div className="flex items-center gap-3 text-[#00E676]">
          <CheckCircle2 className="h-5 w-5" />
          <span>2 assets opted-in</span>
        </div>
      </div>

      <div className="flex flex-col items-center gap-4">
        <button
          onClick={() => navigate('/dashboard')}
          className="bg-[#1B5E20] text-white px-8 py-3 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center"
        >
          <LayoutDashboard className="h-5 w-5 mr-2" />
          Go to Dashboard
        </button>
        <button onClick={() => alert('Starting tutorial...')} className="text-[#BDBDBD] hover:text-white transition-colors duration-300">
          Watch Quick Tutorial
        </button>
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-[#424242] text-[#BDBDBD] hover:text-white hover:border-white transition-colors duration-300"
        >
          {/* TODO:: */}
          Back
        </button>
      </div>
    </div>
  )
}
