{"version": 3, "sources": ["../../agri_usd/contract.py"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASQ;AAAqB;AAArB;AACA;AAAoB;AAApB;AACA;AAAa;AAAb;AARR;;AAAA;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAAA;;;;;;;;;;;;;;;;AAAA;;AA4EK;;AAAA;AAAA;AAAA;;AAAA;AA5EL;;;AAAA;;;AAAA;AAAA;;AAAA;;;AAAA;AAAA;;AA4EK;;;AAAA;AAAA;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAjBA;;AAAA;AAAA;AAAA;;AAAA;AA3DL;;;AAAA;;;AAAA;AAAA;;AA2DK;;;AAAA;AAAA;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAhBA;;AAAA;AAAA;AAAA;;AAAA;AA3CL;;;AAAA;;;AAAA;AAAA;;AA2CK;;;AAAA;AAAA;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAJA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAvBA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAVL;;AAAA;;;;;;;;;AAce;;AAAc;;AAAd;AAAP;AACO;AAAA;AAAA;AAAA;AAAA;AAAP;AAEe;AASP;;AAJI;;AACA;;;;;;;;;;AAFE;;;;;;;;;;;;AADC;;;;;;;;;;;;;;;;;AADF;;;;AADH;;;;;;;;;;;;;AADK;;;;;;AAAA;;;AAaf;;;AAAA;AACA;AAIO;AAAA;AAAA;AAAA;AAAP;AAGO;AAAA;AAAA;AAAA;AAAP;AAIO;AAAA;AAAA;AAAA;AAAP;AACR;;;AAMsB;AAIN;;AAFO;AAAA;AAAA;AAAA;AACE;;AAAA;;;;;;;;;;;AAHH;;;;;;AAAA;;;AAOd;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA;AAER;;;AAMsB;AAKN;;AAJW;;AACJ;AAAA;AAAA;AAAA;AACE;;AAAA;;;;;;;;;;;;;;AAHH;;;;;;AAAA;;;AAQd;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA;AAER;;;AAQsB;AAKN;;AAHO;AAAA;AAAA;AAAA;AACE;;AAAA;;;;;;;;;;;;;;;AAHH;;;;;;AAAA;;;AAQd;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA", "op_pc_offset": 0, "pc_events": {"1": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.__algopy_entrypoint_with_init", "params": {}, "block": "main", "stack_in": [], "op": "intcblock 0 1 4"}, "6": {"op": "bytecblock \"asset\" 0x151f7c75 \"minted_tokens\" \"burnt_tokens\""}, "46": {"op": "txn ApplicationID", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "48": {"op": "bnz main_after_if_else@2", "stack_out": []}, "51": {"op": "bytec_2 // \"minted_tokens\"", "defined_out": ["\"minted_tokens\""], "stack_out": ["\"minted_tokens\""]}, "52": {"op": "intc_0 // 0", "defined_out": ["\"minted_tokens\"", "0"], "stack_out": ["\"minted_tokens\"", "0"]}, "53": {"op": "app_global_put", "stack_out": []}, "54": {"op": "bytec_3 // \"burnt_tokens\"", "defined_out": ["\"burnt_tokens\""], "stack_out": ["\"burnt_tokens\""]}, "55": {"op": "intc_0 // 0", "stack_out": ["\"burnt_tokens\"", "0"]}, "56": {"op": "app_global_put", "stack_out": []}, "57": {"op": "bytec_0 // \"asset\"", "defined_out": ["\"asset\""], "stack_out": ["\"asset\""]}, "58": {"op": "intc_0 // 0", "stack_out": ["\"asset\"", "0"]}, "59": {"op": "app_global_put", "stack_out": []}, "60": {"block": "main_after_if_else@2", "stack_in": [], "op": "txn NumAppArgs", "defined_out": ["tmp%0#2"], "stack_out": ["tmp%0#2"]}, "62": {"op": "bz main_bare_routing@12", "stack_out": []}, "65": {"op": "pushbytess 0x8213ade6 0x5ba22a84 0x528f9dcb 0x9a988f95 0x7aca8dfa 0x2d9a0de7 0xc372f979 // method \"create()byte[]\", method \"get_asset_id()uint64\", method \"get_minted_tokens()uint64\", method \"get_burnt_tokens()uint64\", method \"mint_tokens(uint64,account)byte[]\", method \"burn_tokens(uint64,account)byte[]\", method \"transfer_tokens(uint64,account,account)byte[]\"", "defined_out": ["Method(burn_tokens(uint64,account)byte[])", "Method(create()byte[])", "Method(get_asset_id()uint64)", "Method(get_burnt_tokens()uint64)", "Method(get_minted_tokens()uint64)", "Method(mint_tokens(uint64,account)byte[])", "Method(transfer_tokens(uint64,account,account)byte[])"], "stack_out": ["Method(create()byte[])", "Method(get_asset_id()uint64)", "Method(get_minted_tokens()uint64)", "Method(get_burnt_tokens()uint64)", "Method(mint_tokens(uint64,account)byte[])", "Method(burn_tokens(uint64,account)byte[])", "Method(transfer_tokens(uint64,account,account)byte[])"]}, "102": {"op": "txna ApplicationArgs 0", "defined_out": ["Method(burn_tokens(uint64,account)byte[])", "Method(create()byte[])", "Method(get_asset_id()uint64)", "Method(get_burnt_tokens()uint64)", "Method(get_minted_tokens()uint64)", "Method(mint_tokens(uint64,account)byte[])", "Method(transfer_tokens(uint64,account,account)byte[])", "tmp%2#0"], "stack_out": ["Method(create()byte[])", "Method(get_asset_id()uint64)", "Method(get_minted_tokens()uint64)", "Method(get_burnt_tokens()uint64)", "Method(mint_tokens(uint64,account)byte[])", "Method(burn_tokens(uint64,account)byte[])", "Method(transfer_tokens(uint64,account,account)byte[])", "tmp%2#0"]}, "105": {"op": "match main_create_route@5 main_get_asset_id_route@6 main_get_minted_tokens_route@7 main_get_burnt_tokens_route@8 main_mint_tokens_route@9 main_burn_tokens_route@10 main_transfer_tokens_route@11", "stack_out": []}, "121": {"block": "main_after_if_else@14", "stack_in": [], "op": "intc_0 // 0", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "122": {"op": "return", "stack_out": []}, "123": {"block": "main_transfer_tokens_route@11", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%37#0"], "stack_out": ["tmp%37#0"]}, "125": {"op": "!", "defined_out": ["tmp%38#0"], "stack_out": ["tmp%38#0"]}, "126": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "127": {"op": "txn ApplicationID", "defined_out": ["tmp%39#0"], "stack_out": ["tmp%39#0"]}, "129": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "130": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[8]%2#0"], "stack_out": ["reinterpret_bytes[8]%2#0"]}, "133": {"op": "txna ApplicationArgs 2", "defined_out": ["reinterpret_bytes[1]%2#0", "reinterpret_bytes[8]%2#0"], "stack_out": ["reinterpret_bytes[8]%2#0", "reinterpret_bytes[1]%2#0"]}, "136": {"op": "btoi", "defined_out": ["reinterpret_bytes[8]%2#0", "tmp%41#0"], "stack_out": ["reinterpret_bytes[8]%2#0", "tmp%41#0"]}, "137": {"op": "txnas Accounts", "defined_out": ["reinterpret_bytes[8]%2#0", "tmp%42#0"], "stack_out": ["reinterpret_bytes[8]%2#0", "tmp%42#0"]}, "139": {"op": "txna ApplicationArgs 3", "defined_out": ["reinterpret_bytes[1]%3#0", "reinterpret_bytes[8]%2#0", "tmp%42#0"], "stack_out": ["reinterpret_bytes[8]%2#0", "tmp%42#0", "reinterpret_bytes[1]%3#0"]}, "142": {"op": "btoi", "defined_out": ["reinterpret_bytes[8]%2#0", "tmp%42#0", "tmp%43#0"], "stack_out": ["reinterpret_bytes[8]%2#0", "tmp%42#0", "tmp%43#0"]}, "143": {"op": "txnas Accounts", "defined_out": ["reinterpret_bytes[8]%2#0", "tmp%42#0", "tmp%44#0"], "stack_out": ["reinterpret_bytes[8]%2#0", "tmp%42#0", "tmp%44#0"]}, "145": {"callsub": "smart_contracts.agri_usd.contract.AgriUSDCoin.transfer_tokens", "op": "callsub transfer_tokens", "defined_out": ["to_encode%6#0"], "stack_out": ["to_encode%6#0"]}, "148": {"op": "dup", "defined_out": ["to_encode%6#0", "to_encode%6#0 (copy)"], "stack_out": ["to_encode%6#0", "to_encode%6#0 (copy)"]}, "149": {"op": "len", "defined_out": ["length%3#0", "to_encode%6#0"], "stack_out": ["to_encode%6#0", "length%3#0"]}, "150": {"op": "itob", "defined_out": ["as_bytes%3#0", "to_encode%6#0"], "stack_out": ["to_encode%6#0", "as_bytes%3#0"]}, "151": {"op": "extract 6 2", "defined_out": ["length_uint16%3#0", "to_encode%6#0"], "stack_out": ["to_encode%6#0", "length_uint16%3#0"]}, "154": {"op": "swap", "stack_out": ["length_uint16%3#0", "to_encode%6#0"]}, "155": {"op": "concat", "defined_out": ["encoded_value%3#0"], "stack_out": ["encoded_value%3#0"]}, "156": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "encoded_value%3#0"], "stack_out": ["encoded_value%3#0", "0x151f7c75"]}, "157": {"op": "swap", "stack_out": ["0x151f7c75", "encoded_value%3#0"]}, "158": {"op": "concat", "defined_out": ["tmp%45#0"], "stack_out": ["tmp%45#0"]}, "159": {"op": "log", "stack_out": []}, "160": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "161": {"op": "return", "stack_out": []}, "162": {"block": "main_burn_tokens_route@10", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%30#0"], "stack_out": ["tmp%30#0"]}, "164": {"op": "!", "defined_out": ["tmp%31#0"], "stack_out": ["tmp%31#0"]}, "165": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "166": {"op": "txn ApplicationID", "defined_out": ["tmp%32#0"], "stack_out": ["tmp%32#0"]}, "168": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "169": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[8]%1#0"], "stack_out": ["reinterpret_bytes[8]%1#0"]}, "172": {"op": "txna ApplicationArgs 2", "defined_out": ["reinterpret_bytes[1]%1#0", "reinterpret_bytes[8]%1#0"], "stack_out": ["reinterpret_bytes[8]%1#0", "reinterpret_bytes[1]%1#0"]}, "175": {"op": "btoi", "defined_out": ["reinterpret_bytes[8]%1#0", "tmp%34#0"], "stack_out": ["reinterpret_bytes[8]%1#0", "tmp%34#0"]}, "176": {"op": "txnas Accounts", "defined_out": ["reinterpret_bytes[8]%1#0", "tmp%35#0"], "stack_out": ["reinterpret_bytes[8]%1#0", "tmp%35#0"]}, "178": {"callsub": "smart_contracts.agri_usd.contract.AgriUSDCoin.burn_tokens", "op": "callsub burn_tokens", "defined_out": ["to_encode%5#0"], "stack_out": ["to_encode%5#0"]}, "181": {"op": "dup", "defined_out": ["to_encode%5#0", "to_encode%5#0 (copy)"], "stack_out": ["to_encode%5#0", "to_encode%5#0 (copy)"]}, "182": {"op": "len", "defined_out": ["length%2#0", "to_encode%5#0"], "stack_out": ["to_encode%5#0", "length%2#0"]}, "183": {"op": "itob", "defined_out": ["as_bytes%2#0", "to_encode%5#0"], "stack_out": ["to_encode%5#0", "as_bytes%2#0"]}, "184": {"op": "extract 6 2", "defined_out": ["length_uint16%2#0", "to_encode%5#0"], "stack_out": ["to_encode%5#0", "length_uint16%2#0"]}, "187": {"op": "swap", "stack_out": ["length_uint16%2#0", "to_encode%5#0"]}, "188": {"op": "concat", "defined_out": ["encoded_value%2#0"], "stack_out": ["encoded_value%2#0"]}, "189": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "encoded_value%2#0"], "stack_out": ["encoded_value%2#0", "0x151f7c75"]}, "190": {"op": "swap", "stack_out": ["0x151f7c75", "encoded_value%2#0"]}, "191": {"op": "concat", "defined_out": ["tmp%36#0"], "stack_out": ["tmp%36#0"]}, "192": {"op": "log", "stack_out": []}, "193": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "194": {"op": "return", "stack_out": []}, "195": {"block": "main_mint_tokens_route@9", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%23#0"], "stack_out": ["tmp%23#0"]}, "197": {"op": "!", "defined_out": ["tmp%24#0"], "stack_out": ["tmp%24#0"]}, "198": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "199": {"op": "txn ApplicationID", "defined_out": ["tmp%25#0"], "stack_out": ["tmp%25#0"]}, "201": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "202": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[8]%0#0"], "stack_out": ["reinterpret_bytes[8]%0#0"]}, "205": {"op": "txna ApplicationArgs 2", "defined_out": ["reinterpret_bytes[1]%0#0", "reinterpret_bytes[8]%0#0"], "stack_out": ["reinterpret_bytes[8]%0#0", "reinterpret_bytes[1]%0#0"]}, "208": {"op": "btoi", "defined_out": ["reinterpret_bytes[8]%0#0", "tmp%27#0"], "stack_out": ["reinterpret_bytes[8]%0#0", "tmp%27#0"]}, "209": {"op": "txnas Accounts", "defined_out": ["reinterpret_bytes[8]%0#0", "tmp%28#0"], "stack_out": ["reinterpret_bytes[8]%0#0", "tmp%28#0"]}, "211": {"callsub": "smart_contracts.agri_usd.contract.AgriUSDCoin.mint_tokens", "op": "callsub mint_tokens", "defined_out": ["to_encode%4#0"], "stack_out": ["to_encode%4#0"]}, "214": {"op": "dup", "defined_out": ["to_encode%4#0", "to_encode%4#0 (copy)"], "stack_out": ["to_encode%4#0", "to_encode%4#0 (copy)"]}, "215": {"op": "len", "defined_out": ["length%1#0", "to_encode%4#0"], "stack_out": ["to_encode%4#0", "length%1#0"]}, "216": {"op": "itob", "defined_out": ["as_bytes%1#0", "to_encode%4#0"], "stack_out": ["to_encode%4#0", "as_bytes%1#0"]}, "217": {"op": "extract 6 2", "defined_out": ["length_uint16%1#0", "to_encode%4#0"], "stack_out": ["to_encode%4#0", "length_uint16%1#0"]}, "220": {"op": "swap", "stack_out": ["length_uint16%1#0", "to_encode%4#0"]}, "221": {"op": "concat", "defined_out": ["encoded_value%1#0"], "stack_out": ["encoded_value%1#0"]}, "222": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "encoded_value%1#0"], "stack_out": ["encoded_value%1#0", "0x151f7c75"]}, "223": {"op": "swap", "stack_out": ["0x151f7c75", "encoded_value%1#0"]}, "224": {"op": "concat", "defined_out": ["tmp%29#0"], "stack_out": ["tmp%29#0"]}, "225": {"op": "log", "stack_out": []}, "226": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "227": {"op": "return", "stack_out": []}, "228": {"block": "main_get_burnt_tokens_route@8", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%18#0"], "stack_out": ["tmp%18#0"]}, "230": {"op": "!", "defined_out": ["tmp%19#0"], "stack_out": ["tmp%19#0"]}, "231": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "232": {"op": "txn ApplicationID", "defined_out": ["tmp%20#0"], "stack_out": ["tmp%20#0"]}, "234": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "235": {"callsub": "smart_contracts.agri_usd.contract.AgriUSDCoin.get_burnt_tokens", "op": "callsub get_burnt_tokens", "defined_out": ["to_encode%3#0"], "stack_out": ["to_encode%3#0"]}, "238": {"op": "itob", "defined_out": ["val_as_bytes%2#0"], "stack_out": ["val_as_bytes%2#0"]}, "239": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "val_as_bytes%2#0"], "stack_out": ["val_as_bytes%2#0", "0x151f7c75"]}, "240": {"op": "swap", "stack_out": ["0x151f7c75", "val_as_bytes%2#0"]}, "241": {"op": "concat", "defined_out": ["tmp%22#0"], "stack_out": ["tmp%22#0"]}, "242": {"op": "log", "stack_out": []}, "243": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "244": {"op": "return", "stack_out": []}, "245": {"block": "main_get_minted_tokens_route@7", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%13#0"], "stack_out": ["tmp%13#0"]}, "247": {"op": "!", "defined_out": ["tmp%14#0"], "stack_out": ["tmp%14#0"]}, "248": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "249": {"op": "txn ApplicationID", "defined_out": ["tmp%15#0"], "stack_out": ["tmp%15#0"]}, "251": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "252": {"callsub": "smart_contracts.agri_usd.contract.AgriUSDCoin.get_minted_tokens", "op": "callsub get_minted_tokens", "defined_out": ["to_encode%2#0"], "stack_out": ["to_encode%2#0"]}, "255": {"op": "itob", "defined_out": ["val_as_bytes%1#0"], "stack_out": ["val_as_bytes%1#0"]}, "256": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "val_as_bytes%1#0"], "stack_out": ["val_as_bytes%1#0", "0x151f7c75"]}, "257": {"op": "swap", "stack_out": ["0x151f7c75", "val_as_bytes%1#0"]}, "258": {"op": "concat", "defined_out": ["tmp%17#0"], "stack_out": ["tmp%17#0"]}, "259": {"op": "log", "stack_out": []}, "260": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "261": {"op": "return", "stack_out": []}, "262": {"block": "main_get_asset_id_route@6", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%8#0"], "stack_out": ["tmp%8#0"]}, "264": {"op": "!", "defined_out": ["tmp%9#0"], "stack_out": ["tmp%9#0"]}, "265": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "266": {"op": "txn ApplicationID", "defined_out": ["tmp%10#0"], "stack_out": ["tmp%10#0"]}, "268": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "269": {"callsub": "smart_contracts.agri_usd.contract.AgriUSDCoin.get_asset_id", "op": "callsub get_asset_id", "defined_out": ["to_encode%1#0"], "stack_out": ["to_encode%1#0"]}, "272": {"op": "itob", "defined_out": ["val_as_bytes%0#0"], "stack_out": ["val_as_bytes%0#0"]}, "273": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "val_as_bytes%0#0"], "stack_out": ["val_as_bytes%0#0", "0x151f7c75"]}, "274": {"op": "swap", "stack_out": ["0x151f7c75", "val_as_bytes%0#0"]}, "275": {"op": "concat", "defined_out": ["tmp%12#0"], "stack_out": ["tmp%12#0"]}, "276": {"op": "log", "stack_out": []}, "277": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "278": {"op": "return", "stack_out": []}, "279": {"block": "main_create_route@5", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%3#0"], "stack_out": ["tmp%3#0"]}, "281": {"op": "!", "defined_out": ["tmp%4#0"], "stack_out": ["tmp%4#0"]}, "282": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "283": {"op": "txn ApplicationID", "defined_out": ["tmp%5#0"], "stack_out": ["tmp%5#0"]}, "285": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "286": {"callsub": "smart_contracts.agri_usd.contract.AgriUSDCoin.create", "op": "callsub create", "defined_out": ["to_encode%0#0"], "stack_out": ["to_encode%0#0"]}, "289": {"op": "dup", "defined_out": ["to_encode%0#0", "to_encode%0#0 (copy)"], "stack_out": ["to_encode%0#0", "to_encode%0#0 (copy)"]}, "290": {"op": "len", "defined_out": ["length%0#0", "to_encode%0#0"], "stack_out": ["to_encode%0#0", "length%0#0"]}, "291": {"op": "itob", "defined_out": ["as_bytes%0#0", "to_encode%0#0"], "stack_out": ["to_encode%0#0", "as_bytes%0#0"]}, "292": {"op": "extract 6 2", "defined_out": ["length_uint16%0#0", "to_encode%0#0"], "stack_out": ["to_encode%0#0", "length_uint16%0#0"]}, "295": {"op": "swap", "stack_out": ["length_uint16%0#0", "to_encode%0#0"]}, "296": {"op": "concat", "defined_out": ["encoded_value%0#0"], "stack_out": ["encoded_value%0#0"]}, "297": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "encoded_value%0#0"], "stack_out": ["encoded_value%0#0", "0x151f7c75"]}, "298": {"op": "swap", "stack_out": ["0x151f7c75", "encoded_value%0#0"]}, "299": {"op": "concat", "defined_out": ["tmp%7#0"], "stack_out": ["tmp%7#0"]}, "300": {"op": "log", "stack_out": []}, "301": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "302": {"op": "return", "stack_out": []}, "303": {"block": "main_bare_routing@12", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%46#0"], "stack_out": ["tmp%46#0"]}, "305": {"op": "bnz main_after_if_else@14", "stack_out": []}, "308": {"op": "txn ApplicationID", "defined_out": ["tmp%47#0"], "stack_out": ["tmp%47#0"]}, "310": {"op": "!", "defined_out": ["tmp%48#0"], "stack_out": ["tmp%48#0"]}, "311": {"error": "can only call when creating", "op": "assert // can only call when creating", "stack_out": []}, "312": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "313": {"op": "return", "stack_out": []}, "314": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.create", "params": {}, "block": "create", "stack_in": [], "op": "txn Sender", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "316": {"op": "global CreatorAddress", "defined_out": ["tmp%0#0", "tmp%1#0"], "stack_out": ["tmp%0#0", "tmp%1#0"]}, "318": {"op": "==", "defined_out": ["tmp%2#0"], "stack_out": ["tmp%2#0"]}, "319": {"error": "Only the creator can create the ASA", "op": "assert // Only the creator can create the ASA", "stack_out": []}, "320": {"op": "intc_0 // 0", "defined_out": ["0"], "stack_out": ["0"]}, "321": {"op": "bytec_0 // \"asset\"", "defined_out": ["\"asset\"", "0"], "stack_out": ["0", "\"asset\""]}, "322": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["maybe_value%0#0", "maybe_exists%0#0"]}, "323": {"error": "check self.asset exists", "op": "assert // check self.asset exists", "stack_out": ["maybe_value%0#0"]}, "324": {"op": "!", "defined_out": ["tmp%3#0"], "stack_out": ["tmp%3#0"]}, "325": {"error": "ASA already created", "op": "assert // ASA already created", "stack_out": []}, "326": {"op": "itxn_begin"}, "327": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "329": {"op": "global CurrentApplicationAddress", "defined_out": ["inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0"]}, "331": {"op": "dupn 3", "defined_out": ["inner_txn_params%0%%param_ConfigAssetClawback_idx_0#0", "inner_txn_params%0%%param_ConfigAssetFreeze_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0", "inner_txn_params%0%%param_ConfigAssetFreeze_idx_0#0", "inner_txn_params%0%%param_ConfigAssetClawback_idx_0#0"]}, "333": {"op": "itxn_field ConfigAssetClawback", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0", "inner_txn_params%0%%param_ConfigAssetFreeze_idx_0#0"]}, "335": {"op": "itxn_field ConfigAssetFreeze", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0"]}, "337": {"op": "itxn_field ConfigAssetReserve", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0"]}, "339": {"op": "itxn_field ConfigAssetManager", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "341": {"op": "pushbytes \"AGRI-USD\"", "defined_out": ["\"AGRI-USD\"", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "\"AGRI-USD\""]}, "351": {"op": "itxn_field ConfigAssetUnitName", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "353": {"op": "pushbytes \"Agri USD Coin\"", "defined_out": ["\"Agri USD Coin\"", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "\"Agri USD Coin\""]}, "368": {"op": "itxn_field ConfigAssetName", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "370": {"op": "pushint 2 // 2", "defined_out": ["2", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "2"]}, "372": {"op": "itxn_field ConfigAssetDecimals", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "374": {"op": "pushint 18446744073709551615 // 18446744073709551615", "defined_out": ["18446744073709551615", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "18446744073709551615"]}, "385": {"op": "itxn_field ConfigAssetTotal", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "387": {"op": "pushint 3 // acfg", "defined_out": ["acfg", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "acfg"]}, "389": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "391": {"op": "itxn_field Fee", "stack_out": []}, "393": {"op": "itxn_submit"}, "394": {"op": "itxn TxID", "defined_out": ["asset_result.TxID#0"], "stack_out": ["asset_result.TxID#0"]}, "396": {"op": "bytec_0 // \"asset\"", "stack_out": ["asset_result.TxID#0", "\"asset\""]}, "397": {"op": "itxn CreatedAssetID", "defined_out": ["\"asset\"", "asset_result.CreatedAssetID#0", "asset_result.TxID#0"], "stack_out": ["asset_result.TxID#0", "\"asset\"", "asset_result.CreatedAssetID#0"]}, "399": {"op": "app_global_put", "stack_out": ["asset_result.TxID#0"]}, "400": {"retsub": true, "op": "retsub"}, "401": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.get_asset_id", "params": {}, "block": "get_asset_id", "stack_in": [], "op": "intc_0 // 0", "defined_out": ["0"], "stack_out": ["0"]}, "402": {"op": "bytec_0 // \"asset\"", "defined_out": ["\"asset\"", "0"], "stack_out": ["0", "\"asset\""]}, "403": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["maybe_value%0#0", "maybe_exists%0#0"]}, "404": {"error": "check self.asset exists", "op": "assert // check self.asset exists", "stack_out": ["maybe_value%0#0"]}, "405": {"retsub": true, "op": "retsub"}, "406": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.get_minted_tokens", "params": {}, "block": "get_minted_tokens", "stack_in": [], "op": "intc_0 // 0", "defined_out": ["0"], "stack_out": ["0"]}, "407": {"op": "bytec_2 // \"minted_tokens\"", "defined_out": ["\"minted_tokens\"", "0"], "stack_out": ["0", "\"minted_tokens\""]}, "408": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["maybe_value%0#0", "maybe_exists%0#0"]}, "409": {"error": "check self.minted_tokens exists", "op": "assert // check self.minted_tokens exists", "stack_out": ["maybe_value%0#0"]}, "410": {"retsub": true, "op": "retsub"}, "411": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.get_burnt_tokens", "params": {}, "block": "get_burnt_tokens", "stack_in": [], "op": "intc_0 // 0", "defined_out": ["0"], "stack_out": ["0"]}, "412": {"op": "bytec_3 // \"burnt_tokens\"", "defined_out": ["\"burnt_tokens\"", "0"], "stack_out": ["0", "\"burnt_tokens\""]}, "413": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["maybe_value%0#0", "maybe_exists%0#0"]}, "414": {"error": "check self.burnt_tokens exists", "op": "assert // check self.burnt_tokens exists", "stack_out": ["maybe_value%0#0"]}, "415": {"retsub": true, "op": "retsub"}, "416": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.mint_tokens", "params": {"amount#0": "bytes", "receiver#0": "bytes"}, "block": "mint_tokens", "stack_in": [], "op": "proto 2 1"}, "419": {"op": "itxn_begin"}, "420": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "422": {"op": "intc_0 // 0", "defined_out": ["0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "0"]}, "423": {"op": "bytec_0 // \"asset\"", "defined_out": ["\"asset\"", "0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "0", "\"asset\""]}, "424": {"op": "app_global_get_ex", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "maybe_exists%0#0"]}, "425": {"error": "check self.asset exists", "op": "assert // check self.asset exists", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"]}, "426": {"op": "frame_dig -2", "defined_out": ["amount#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "amount#0 (copy)"]}, "428": {"op": "btoi", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "429": {"op": "dup", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0 (copy)"]}, "430": {"op": "itxn_field AssetAmount", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "432": {"op": "swap", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "maybe_value%0#0"]}, "433": {"op": "itxn_field XferAsset", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "435": {"op": "frame_dig -1", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "receiver#0 (copy)"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "receiver#0 (copy)"]}, "437": {"op": "itxn_field AssetReceiver", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "439": {"op": "intc_2 // axfer", "defined_out": ["axfer", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "axfer"]}, "440": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "442": {"op": "swap", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"]}, "443": {"op": "itxn_field Fee", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "445": {"op": "itxn_submit"}, "446": {"op": "itxn TxID", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"]}, "448": {"op": "intc_0 // 0", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "0"]}, "449": {"op": "bytec_2 // \"minted_tokens\"", "defined_out": ["\"minted_tokens\"", "0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "0", "\"minted_tokens\""]}, "450": {"op": "app_global_get_ex", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_exists%1#0", "maybe_value%1#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_value%1#0", "maybe_exists%1#0"]}, "451": {"error": "check self.minted_tokens exists", "op": "assert // check self.minted_tokens exists", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_value%1#0"]}, "452": {"op": "uncover 2", "stack_out": ["itxn_result.TxID#0", "maybe_value%1#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "454": {"op": "+", "defined_out": ["itxn_result.TxID#0", "new_state_value%0#0"], "stack_out": ["itxn_result.TxID#0", "new_state_value%0#0"]}, "455": {"op": "bytec_2 // \"minted_tokens\"", "stack_out": ["itxn_result.TxID#0", "new_state_value%0#0", "\"minted_tokens\""]}, "456": {"op": "swap", "stack_out": ["itxn_result.TxID#0", "\"minted_tokens\"", "new_state_value%0#0"]}, "457": {"op": "app_global_put", "stack_out": ["itxn_result.TxID#0"]}, "458": {"retsub": true, "op": "retsub"}, "459": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.burn_tokens", "params": {"amount#0": "bytes", "address#0": "bytes"}, "block": "burn_tokens", "stack_in": [], "op": "proto 2 1"}, "462": {"op": "itxn_begin"}, "463": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "465": {"op": "global CurrentApplicationAddress", "defined_out": ["inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0"]}, "467": {"op": "intc_0 // 0", "defined_out": ["0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "0"]}, "468": {"op": "bytec_0 // \"asset\"", "defined_out": ["\"asset\"", "0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "0", "\"asset\""]}, "469": {"op": "app_global_get_ex", "defined_out": ["inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0", "maybe_exists%0#0"]}, "470": {"error": "check self.asset exists", "op": "assert // check self.asset exists", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0"]}, "471": {"op": "frame_dig -2", "defined_out": ["amount#0 (copy)", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0", "amount#0 (copy)"]}, "473": {"op": "btoi", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "474": {"op": "frame_dig -1", "defined_out": ["address#0 (copy)", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "address#0 (copy)"]}, "476": {"op": "itxn_field AssetSender", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "478": {"op": "dup", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0 (copy)", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0 (copy)"]}, "479": {"op": "itxn_field AssetAmount", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "481": {"op": "swap", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "maybe_value%0#0"]}, "482": {"op": "itxn_field XferAsset", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "484": {"op": "swap", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0"]}, "485": {"op": "itxn_field AssetReceiver", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "487": {"op": "intc_2 // axfer", "defined_out": ["axfer", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "axfer"]}, "488": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "490": {"op": "swap", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"]}, "491": {"op": "itxn_field Fee", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "493": {"op": "itxn_submit"}, "494": {"op": "itxn TxID", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"]}, "496": {"op": "intc_0 // 0", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "0"]}, "497": {"op": "bytec_3 // \"burnt_tokens\"", "defined_out": ["\"burnt_tokens\"", "0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "0", "\"burnt_tokens\""]}, "498": {"op": "app_global_get_ex", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_exists%1#0", "maybe_value%1#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_value%1#0", "maybe_exists%1#0"]}, "499": {"error": "check self.burnt_tokens exists", "op": "assert // check self.burnt_tokens exists", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_value%1#0"]}, "500": {"op": "uncover 2", "stack_out": ["itxn_result.TxID#0", "maybe_value%1#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "502": {"op": "+", "defined_out": ["itxn_result.TxID#0", "new_state_value%0#0"], "stack_out": ["itxn_result.TxID#0", "new_state_value%0#0"]}, "503": {"op": "bytec_3 // \"burnt_tokens\"", "stack_out": ["itxn_result.TxID#0", "new_state_value%0#0", "\"burnt_tokens\""]}, "504": {"op": "swap", "stack_out": ["itxn_result.TxID#0", "\"burnt_tokens\"", "new_state_value%0#0"]}, "505": {"op": "app_global_put", "stack_out": ["itxn_result.TxID#0"]}, "506": {"retsub": true, "op": "retsub"}, "507": {"subroutine": "smart_contracts.agri_usd.contract.AgriUSDCoin.transfer_tokens", "params": {"amount#0": "bytes", "receiver#0": "bytes", "account#0": "bytes"}, "block": "transfer_tokens", "stack_in": [], "op": "proto 3 1"}, "510": {"op": "itxn_begin"}, "511": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "513": {"op": "intc_0 // 0", "defined_out": ["0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "0"]}, "514": {"op": "bytec_0 // \"asset\"", "defined_out": ["\"asset\"", "0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "0", "\"asset\""]}, "515": {"op": "app_global_get_ex", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "maybe_exists%0#0"]}, "516": {"error": "check self.asset exists", "op": "assert // check self.asset exists", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"]}, "517": {"op": "frame_dig -3", "defined_out": ["amount#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "amount#0 (copy)"]}, "519": {"op": "btoi", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "520": {"op": "frame_dig -1", "defined_out": ["account#0 (copy)", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "account#0 (copy)"]}, "522": {"op": "itxn_field AssetSender", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "524": {"op": "dup", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0 (copy)"]}, "525": {"op": "itxn_field AssetAmount", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "527": {"op": "swap", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "maybe_value%0#0"]}, "528": {"op": "itxn_field XferAsset", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "530": {"op": "frame_dig -2", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "receiver#0 (copy)"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "receiver#0 (copy)"]}, "532": {"op": "itxn_field AssetReceiver", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "534": {"op": "intc_2 // axfer", "defined_out": ["axfer", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "axfer"]}, "535": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "537": {"op": "swap", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"]}, "538": {"op": "itxn_field Fee", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "540": {"op": "itxn_submit"}, "541": {"op": "itxn TxID", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"]}, "543": {"op": "intc_0 // 0", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "0"]}, "544": {"op": "bytec_2 // \"minted_tokens\"", "defined_out": ["\"minted_tokens\"", "0", "inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "0", "\"minted_tokens\""]}, "545": {"op": "app_global_get_ex", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_exists%1#0", "maybe_value%1#0"], "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_value%1#0", "maybe_exists%1#0"]}, "546": {"error": "check self.minted_tokens exists", "op": "assert // check self.minted_tokens exists", "stack_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "itxn_result.TxID#0", "maybe_value%1#0"]}, "547": {"op": "uncover 2", "stack_out": ["itxn_result.TxID#0", "maybe_value%1#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "549": {"op": "+", "defined_out": ["itxn_result.TxID#0", "new_state_value%0#0"], "stack_out": ["itxn_result.TxID#0", "new_state_value%0#0"]}, "550": {"op": "bytec_2 // \"minted_tokens\"", "stack_out": ["itxn_result.TxID#0", "new_state_value%0#0", "\"minted_tokens\""]}, "551": {"op": "swap", "stack_out": ["itxn_result.TxID#0", "\"minted_tokens\"", "new_state_value%0#0"]}, "552": {"op": "app_global_put", "stack_out": ["itxn_result.TxID#0"]}, "553": {"retsub": true, "op": "retsub"}}}