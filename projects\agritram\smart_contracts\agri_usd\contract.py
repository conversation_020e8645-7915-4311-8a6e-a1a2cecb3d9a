from algopy import Account, Asset, ARC4Contract, Global, Txn, UInt64, arc4, itxn


class AgriUSDCoin(ARC4Contract):
    minted_tokens: UInt64
    burnt_tokens: UInt64
    asset: Asset

    def __init__(self) -> None:
        self.minted_tokens = UInt64(0)
        self.burnt_tokens = UInt64(0)
        self.asset = Asset(0)
        
    @arc4.abimethod
    def create(self) -> None:
        """Create the Agri USD Coin ASA."""
        # Create ASA once at deployment
        assert Txn.sender == Global.creator_address, "Only the creator can create the ASA"
        assert self.asset.id == 0, "ASA already created"
        # Create the ASA with maximum supply and 2 decimals
        asset_result = itxn.AssetConfig(
            total=18446744073709551615,
            decimals=2,
            asset_name="Agri USD Coin",
            unit_name="AGRI-USD",
            manager=Global.current_application_address,
            reserve=Global.current_application_address,
            freeze=Global.current_application_address,
            clawback=Global.current_application_address,
            fee=Global.min_txn_fee,
        ).submit()

        # Save ASA ID into contract storage
        self.asset = Asset(asset_result.created_asset.id)

    @arc4.abimethod(readonly=True)
    def get_asset_id(self) -> UInt64:
        return self.asset.id
    @arc4.abimethod(readonly=True)
    def get_minted_tokens(self) -> UInt64:
        return self.minted_tokens

    @arc4.abimethod(readonly=True)
    def get_burnt_tokens(self) -> UInt64:
        return self.burnt_tokens
    @arc4.abimethod
    def mint_tokens(
        self,
        amount: arc4.UInt64,
        receiver: Account,
    ) -> None:
        itxn.AssetTransfer(
            asset_receiver=receiver,
            xfer_asset=self.asset.id,
            asset_amount=amount.native,
            fee=Global.min_txn_fee,
        ).submit()

        self.minted_tokens += amount.native

    @arc4.abimethod
    def burn_tokens(
        self,
        amount: arc4.UInt64,
        address: Account,
    ) -> None:
        itxn.AssetTransfer(
            asset_receiver=Global.current_application_address,
            xfer_asset=self.asset.id,
            asset_amount=amount.native,
            asset_sender=address,
            fee=Global.min_txn_fee,
        ).submit()

        self.burnt_tokens += amount.native

    @arc4.abimethod
    def transfer_tokens(
        self,
        amount: arc4.UInt64,
        receiver: Account,
        account: Account,
    ) -> None:

        itxn.AssetTransfer(
                asset_receiver=receiver,
            xfer_asset=self.asset.id,
            asset_amount=amount.native,
            asset_sender=account,
            fee=Global.min_txn_fee,
        ).submit()

        self.minted_tokens += amount.native
        
