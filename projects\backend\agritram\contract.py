from beaker import Application, Authorize, GlobalStateValue
from pyteal import (
    Assert,
    Div,
    Expr,
    Global,
    InnerTxn,
    InnerTxnBuilder,
    Int,
    Seq,
    TealType,
    TxnField,
    TxnType,
    abi,
)


class CocoaToken:
    # Global State Variables
    minted_tokens = GlobalStateValue(
        stack_type=TealType.uint64,
        default=Int(0),
        descr="Total number of tokens minted",
    )

    burnt_tokens = GlobalStateValue(
        stack_type=TealType.uint64, default=Int(0), descr="Total number of tokens burnt"
    )


app = Application("CocoaToken", state=CocoaToken())


# Application Create
@app.create
def create() -> Expr:
    return Seq(app.initialize_global_state())


@app.external(read_only=True)
def get_minted_tokens(*, output: abi.Uint64) -> Expr:
    return output.set(app.state.minted_tokens.get())


@app.external(read_only=True)
def get_burnt_tokens(*, output: abi.Uint64) -> Expr:
    return output.set(app.state.burnt_tokens.get())


@app.external(authorize=Authorize.only_creator())
def create_asa(
    asset_name: abi.String,
    unit_name: abi.String,
    total: abi.Uint64,
    decimals: abi.Uint32,
    *,
    output: abi.Uint64
) -> Expr:
    return Seq(
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetConfig,
                TxnField.config_asset_name: asset_name.get(),
                TxnField.config_asset_unit_name: unit_name.get(),
                TxnField.config_asset_total: total.get(),
                TxnField.config_asset_decimals: decimals.get(),
                TxnField.config_asset_manager: Global.current_application_address(),
                TxnField.config_asset_reserve: Global.current_application_address(),
                TxnField.config_asset_freeze: Global.current_application_address(),
                TxnField.config_asset_clawback: Global.current_application_address(),
            }
        ),
        output.set(InnerTxn.created_asset_id()),
    )


@app.external(authorize=Authorize.only_creator())
def mint_tokens(
    amount: abi.Uint64,
    receiver: abi.Address,
    token_id: abi.Uint64,
) -> Expr:
    return Seq(
        Assert(token_id.get() != Int(0)),
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetTransfer,
                TxnField.xfer_asset: token_id.get(),
                TxnField.asset_amount: amount.get(),
                TxnField.asset_receiver: receiver.get(),
                TxnField.asset_sender: Global.current_application_address(),
            }
        ),
        app.state.minted_tokens.set(app.state.minted_tokens.get() + amount.get()),
    )


@app.external(authorize=Authorize.only_creator())
def burn_tokens(
    amount: abi.Uint64,
    address: abi.Address,
    token_id: abi.Uint64,
) -> Expr:
    return Seq(
        Assert(token_id.get() != Int(0)),
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetTransfer,
                TxnField.xfer_asset: token_id.get(),
                TxnField.asset_amount: amount.get(),
                TxnField.asset_receiver: Global.current_application_address(),
                TxnField.asset_sender: address.get(),
            }
        ),
        app.state.burnt_tokens.set(app.state.burnt_tokens.get() + amount.get()),
    )


@app.external(authorize=Authorize.only_creator())
def transfer_tokens(
    amount: abi.Uint64,
    receiver: abi.Address,
    account: abi.Address,
    token_id: abi.Uint64,
) -> Expr:
    return Seq(
        Assert(token_id.get() != Int(0)),
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetTransfer,
                TxnField.xfer_asset: token_id.get(),
                TxnField.asset_amount: amount.get(),
                TxnField.asset_receiver: receiver.get(),
                TxnField.asset_sender: account.get(),
            }
        ),
        app.state.minted_tokens.set(app.state.minted_tokens.get() + amount.get()),
    )


@app.external(authorize=Authorize.only_creator())
def convert_ktt_tokens_to_kct(
    amount: abi.Uint64,
    address: abi.Address,
    token_ktt_id: abi.Uint64,
    token_kct_id: abi.Uint64,
) -> Expr:
    return Seq(
        Assert(token_ktt_id.get() != Int(0)),
        Assert(token_kct_id.get() != Int(0)),
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetTransfer,
                TxnField.xfer_asset: token_ktt_id.get(),
                TxnField.asset_amount: amount.get(),
                TxnField.asset_receiver: Global.current_application_address(),
                TxnField.asset_sender: address.get(),
            }
        ),
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetTransfer,
                TxnField.xfer_asset: token_kct_id.get(),
                TxnField.asset_amount: amount.get(),
                TxnField.asset_receiver: address.get(),
                TxnField.asset_sender: Global.current_application_address(),
            }
        ),
    )


@app.external(authorize=Authorize.only_creator())
def convert_kct_tokens_to_ktt(
    amount: abi.Uint64,
    address: abi.Address,
    token_ktt_id: abi.Uint64,
    token_kct_id: abi.Uint64,
) -> Expr:
    adjusted_amount = Div(amount.get(), Int(10)) + amount.get()
    return Seq(
        Assert(token_ktt_id.get() != Int(0)),
        Assert(token_kct_id.get() != Int(0)),
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetTransfer,
                TxnField.xfer_asset: token_kct_id.get(),
                TxnField.asset_amount: amount.get(),
                TxnField.asset_receiver: Global.current_application_address(),
                TxnField.asset_sender: address.get(),
            }
        ),
        InnerTxnBuilder.Execute(
            {
                TxnField.type_enum: TxnType.AssetTransfer,
                TxnField.xfer_asset: token_ktt_id.get(),
                TxnField.asset_amount: adjusted_amount,
                TxnField.asset_receiver: address.get(),
                TxnField.asset_sender: Global.current_application_address(),
            }
        ),
    )
