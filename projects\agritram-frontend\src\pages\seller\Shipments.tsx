import { useState } from 'react';
import { motion } from 'framer-motion';
import { Truck, Search, Calendar, Filter, CheckCircle2, Clock, Package, Eye, FileText } from 'lucide-react';
import AppLayout from '../../components/layout/AppLayout';

// Mock shipment data
const mockShipments = [
  { 
    id: 'ship-1', 
    productName: 'Organic Wheat', 
    quantity: 500, 
    unit: 'kg',
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), 
    transactionId: 'tx-1',
    status: 'pending',
    buyer: '0xabcdef1234567890abcdef1234567890abcdef12',
    destination: '123 Main St, Anytown, USA',
    trackingNumber: '',
    documents: []
  },
  { 
    id: 'ship-2', 
    productName: 'Organic Rice', 
    quantity: 300, 
    unit: 'kg',
    dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), 
    transactionId: 'tx-2',
    status: 'pending',
    buyer: '0xabcdef1234567890abcdef1234567890abcdef12',
    destination: '456 Oak Ave, Somewhere, USA',
    trackingNumber: '',
    documents: []
  },
  { 
    id: 'ship-3', 
    productName: 'Premium Coffee Beans', 
    quantity: 100, 
    unit: 'kg',
    dueDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), 
    transactionId: 'tx-3',
    status: 'shipped',
    buyer: '0xabcdef1234567890abcdef1234567890abcdef12',
    destination: '789 Pine Rd, Elsewhere, USA',
    trackingNumber: 'TRK123456789',
    documents: ['shipping_label.pdf', 'customs_form.pdf']
  },
  { 
    id: 'ship-4', 
    productName: 'Organic Rice', 
    quantity: 200, 
    unit: 'kg',
    dueDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), 
    transactionId: 'tx-4',
    status: 'delivered',
    buyer: '0x7890abcdef1234567890abcdef1234567890abcd',
    destination: '321 Elm St, Nowhere, USA',
    trackingNumber: 'TRK987654321',
    documents: ['shipping_label.pdf', 'delivery_confirmation.pdf']
  }
];

const Shipments = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedShipment, setSelectedShipment] = useState<typeof mockShipments[0] | null>(null);
  
  // Filter shipments based on search query and status
  const filteredShipments = mockShipments.filter((shipment) => {
    const matchesSearch = shipment.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         shipment.transactionId.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || shipment.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });
  
  const handleViewShipment = (shipment: typeof mockShipments[0]) => {
    setSelectedShipment(shipment);
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-secondary-100 text-secondary-700">
            <Clock className="w-3 h-3" />
            <span className="text-xs font-medium">Pending</span>
          </div>
        );
      case 'shipped':
        return (
          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-primary-100 text-primary-700">
            <Truck className="w-3 h-3" />
            <span className="text-xs font-medium">Shipped</span>
          </div>
        );
      case 'delivered':
        return (
          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-success-100 text-success-700">
            <CheckCircle2 className="w-3 h-3" />
            <span className="text-xs font-medium">Delivered</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-neutral-100 text-neutral-700">
            <span className="text-xs font-medium">{status}</span>
          </div>
        );
    }
  };
  
  return (
    <AppLayout title="Shipment Management">
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="lg:w-2/3">
          <div className="bg-white p-4 rounded-xl shadow-sm mb-6">
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search shipments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full rounded-lg border border-neutral-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex flex-wrap gap-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedStatus('all')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  selectedStatus === 'all'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                All
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedStatus('pending')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  selectedStatus === 'pending'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                Pending
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedStatus('shipped')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  selectedStatus === 'shipped'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                Shipped
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedStatus('delivered')}
                className={`px-3 py-1 text-sm font-medium rounded-lg ${
                  selectedStatus === 'delivered'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                Delivered
              </motion.button>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="p-6 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Shipments</h2>
            </div>
            
            {filteredShipments.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-neutral-200">
                      <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Product</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Due Date</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Quantity</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Status</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Tracking</th>
                      <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredShipments.map((shipment) => (
                      <tr
                        key={shipment.id}
                        className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors"
                      >
                        <td className="py-4 px-4">
                          <div>
                            <p className="font-medium text-neutral-900">{shipment.productName}</p>
                            <p className="text-xs text-neutral-500">TX: {shipment.transactionId}</p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4 text-neutral-400" />
                            <p className="text-sm text-neutral-900">
                              {shipment.dueDate.toLocaleDateString()}
                            </p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <p className="text-sm font-medium text-neutral-900">
                            {shipment.quantity} {shipment.unit}
                          </p>
                        </td>
                        <td className="py-4 px-4">
                          {getStatusBadge(shipment.status)}
                        </td>
                        <td className="py-4 px-4">
                          {shipment.trackingNumber ? (
                            <p className="text-sm font-mono text-primary-700">
                              {shipment.trackingNumber}
                            </p>
                          ) : (
                            <p className="text-sm text-neutral-500">—</p>
                          )}
                        </td>
                        <td className="py-4 px-4">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handleViewShipment(shipment)}
                            className="flex items-center gap-1 text-sm font-medium text-primary-600 hover:text-primary-700"
                          >
                            <Eye className="w-4 h-4" />
                            <span>View</span>
                          </motion.button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="py-12 text-center">
                <Filter className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
                <p className="text-lg font-medium text-neutral-900 mb-2">No shipments found</p>
                <p className="text-neutral-500 max-w-md mx-auto">
                  Try adjusting your search or filter to find what you're looking for.
                </p>
              </div>
            )}
          </div>
        </div>
        
        <div className="lg:w-1/3">
          {selectedShipment ? (
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="p-6 border-b border-neutral-200">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-lg font-semibold text-neutral-900 mb-1">
                      Shipment Details
                    </h2>
                    <p className="text-sm text-neutral-500">ID: {selectedShipment.id}</p>
                  </div>
                  {getStatusBadge(selectedShipment.status)}
                </div>
              </div>
              
              <div className="p-6">
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-neutral-500 mb-2">Product Information</h3>
                  <div className="bg-neutral-50 p-4 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 rounded-lg bg-primary-100 text-primary-700">
                        <Package className="w-4 h-4" />
                      </div>
                      <div>
                        <p className="font-medium text-neutral-900">{selectedShipment.productName}</p>
                        <p className="text-sm text-neutral-500">
                          {selectedShipment.quantity} {selectedShipment.unit}
                        </p>
                      </div>
                    </div>
                    <p className="text-sm text-neutral-500 mt-2">
                      Transaction: {selectedShipment.transactionId}
                    </p>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-neutral-500 mb-2">Shipping Information</h3>
                  <div className="bg-neutral-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-neutral-900 mb-1">Destination</p>
                    <p className="text-sm text-neutral-700 mb-3">{selectedShipment.destination}</p>
                    
                    <p className="text-sm font-medium text-neutral-900 mb-1">Due Date</p>
                    <p className="text-sm text-neutral-700 mb-3">
                      {selectedShipment.dueDate.toLocaleDateString()}
                    </p>
                    
                    <p className="text-sm font-medium text-neutral-900 mb-1">Tracking Number</p>
                    {selectedShipment.trackingNumber ? (
                      <p className="text-sm font-mono text-primary-700">
                        {selectedShipment.trackingNumber}
                      </p>
                    ) : (
                      <p className="text-sm text-neutral-500">No tracking number yet</p>
                    )}
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-neutral-500 mb-2">Buyer Information</h3>
                  <div className="bg-neutral-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-neutral-900 mb-1">Wallet Address</p>
                    <p className="text-sm font-mono text-neutral-700 break-all">
                      {selectedShipment.buyer}
                    </p>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-neutral-500 mb-2">Documents</h3>
                  
                  {selectedShipment.documents.length > 0 ? (
                    <div className="space-y-2">
                      {selectedShipment.documents.map((document, index) => (
                        <motion.div
                          key={index}
                          whileHover={{ x: 4 }}
                          className="flex items-center p-3 bg-neutral-50 rounded-lg cursor-pointer"
                        >
                          <FileText className="w-4 h-4 text-primary-600 mr-3" />
                          <span className="text-sm text-neutral-900">{document}</span>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-neutral-500">No documents available</p>
                  )}
                </div>
                
                <div className="flex gap-2">
                  {selectedShipment.status === 'pending' && (
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      <Truck className="w-4 h-4" />
                      <span>Mark as Shipped</span>
                    </motion.button>
                  )}
                  
                  {selectedShipment.status === 'shipped' && (
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-success-600 text-white rounded-lg hover:bg-success-700 transition-colors"
                    >
                      <CheckCircle2 className="w-4 h-4" />
                      <span>Mark as Delivered</span>
                    </motion.button>
                  )}
                  
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex items-center justify-center gap-2 px-4 py-2 bg-neutral-100 text-neutral-700 rounded-lg hover:bg-neutral-200 transition-colors"
                  >
                    <FileText className="w-4 h-4" />
                    <span>Upload Document</span>
                  </motion.button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-sm p-12 text-center flex flex-col items-center justify-center h-full">
              <Truck className="w-12 h-12 text-neutral-300 mb-4" />
              <h3 className="text-xl font-semibold text-neutral-900 mb-2">Select a Shipment</h3>
              <p className="text-neutral-500 max-w-md mx-auto">
                Choose a shipment from the list to view details, update status, and manage documents.
              </p>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
};

export default Shipments;