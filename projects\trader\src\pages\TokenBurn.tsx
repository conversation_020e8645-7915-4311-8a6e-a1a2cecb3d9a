import { useToast } from '@/components/ui/use-toast'
import apiClient from '@/services/apiClient'
import { Al<PERSON><PERSON>riangle, ArrowLeft, Flame, HelpCircle, Loader2, XCircle } from 'lucide-react'
import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

export function TokenBurn() {
  const location = useLocation()
  const currentBalance = location.state?.asaBalance || 0
  const navigate = useNavigate()
  const { toast } = useToast()
  const [amount, setAmount] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [acknowledged, setAcknowledged] = useState(false)

  const parsedAmount = parseFloat(amount) || 0
  const remainingBalance = currentBalance - parsedAmount
  const isValidAmount = parsedAmount > 0 && parsedAmount <= currentBalance

  const handleSetMax = () => {
    setAmount(currentBalance.toString())
  }

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setAmount(value)
      setError(null)
    }
  }

  const handleBurn = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isValidAmount || !acknowledged) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await apiClient.post('/transaction/burn-tokens/', {
        amount: parsedAmount,
      })

      if (response.status === 200) {
        toast({
          title: 'Success!',
          description: 'Tokens burned successfully.',
        })
        navigate(-1)
      } else {
        throw new Error('Transaction failed.')
      }
    } catch (e) {
      console.error(e)
      toast({ title: 'Error!', description: 'Transaction failed. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-main-bg py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button onClick={() => navigate(-1)} className="mr-4 text-accent hover:text-accent-hover transition-colors">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-3xl font-bold text-primary-text flex items-center">
            <Flame className="h-8 w-8 mr-3 text-button-danger" />
            Burn Tokens
          </h1>
        </div>
        <div className="bg-card-bg p-6 rounded-lg shadow-md border border-card-border">
          {/* Current Balance */}
          <div className="bg-accent-light/5 p-6 rounded-lg mb-6">
            <div className="flex justify-between items-center">
              <span className="text-secondary-text">Current Balance</span>
              <span className="text-2xl font-bold text-primary-text">{currentBalance.toLocaleString()} KTT</span>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-button-danger/10 p-4 rounded-lg mb-6 border border-button-danger/20">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-button-danger mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-semibold text-button-danger mb-1">Warning: Irreversible Action</p>
                <p className="text-secondary-text">
                  Token burning is permanent and cannot be undone. The tokens will be permanently removed from circulation.
                </p>
              </div>
            </div>
          </div>

          {/* Burn Form */}
          <form onSubmit={handleBurn} className="space-y-6">
            <div className="bg-card-bg p-6 rounded-lg border border-card-border">
              <div className="space-y-4">
                {/* Amount Input */}
                <div>
                  <label className="block text-sm font-medium text-primary-text mb-2">Amount to Burn</label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <input
                        type="text"
                        className={`w-full bg-card-bg border ${
                          error ? 'border-button-danger' : 'border-card-border'
                        } rounded-lg px-4 py-2 text-primary-text focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-200`}
                        value={amount}
                        onChange={handleAmountChange}
                        placeholder="0.00"
                        required
                      />
                      <span className="absolute right-3 top-2.5 text-secondary-text">KTT</span>
                    </div>
                    <button
                      type="button"
                      onClick={handleSetMax}
                      className="px-4 py-2 bg-accent text-button-text rounded-lg hover:bg-accent-hover transition-colors"
                    >
                      Max
                    </button>
                  </div>
                  {error && (
                    <div className="flex items-center text-button-danger bg-button-danger/10 p-3 rounded-lg mt-2">
                      <XCircle className="h-5 w-5 mr-2" />
                      <p className="text-sm">{error}</p>
                    </div>
                  )}
                </div>

                {/* Preview Panel */}
                {isValidAmount && (
                  <div className="mt-4 p-4 rounded-lg bg-accent-light/5 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-secondary-text">Amount to Burn:</span>
                      <span className="text-primary-text font-medium">{parsedAmount.toLocaleString()} KTT</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-secondary-text">Remaining Balance:</span>
                      <span className="text-primary-text font-medium">{remainingBalance.toLocaleString()} KTT</span>
                    </div>
                  </div>
                )}

                {/* Acknowledgment */}
                <div className="mt-4">
                  <label className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      className="mt-1 form-checkbox h-4 w-4 accent-accent border-card-border rounded focus:ring-accent"
                      checked={acknowledged}
                      onChange={(e) => setAcknowledged(e.target.checked)}
                      required
                    />
                    <span className="text-sm text-secondary-text">
                      I understand that this action is irreversible and the tokens will be permanently destroyed.
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={!isValidAmount || !acknowledged || isLoading}
              className="w-full bg-button-danger text-button-text px-6 py-3 rounded-lg hover:bg-button-danger-hover transition-colors duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Flame className="h-5 w-5" />
                  <span>Burn Tokens</span>
                </>
              )}
            </button>
          </form>

          {/* Help Tooltip */}
          <div className="mt-8 p-4 rounded-lg bg-accent-light/5 border border-card-border">
            <div className="flex items-start space-x-3">
              <HelpCircle className="h-10 w-10 text-accent mt-0.5" />
              <div>
                <h3 className="text-primary-text font-medium">What is token burning?</h3>
                <p className="text-secondary-text text-sm mt-1">
                  Token burning is the process of permanently removing tokens from circulation. This action is irreversible and can affect
                  the token's value by reducing the total supply. Before burning tokens, ensure you understand the implications of this
                  action.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
