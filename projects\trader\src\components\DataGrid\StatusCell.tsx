import { getStatusConfig, getStatusOptions } from '@/utils/statusUtils'
import { BatchStatus } from '@/utils/types'
import { ChevronDown, Lock } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'

// Create a global state to track the currently open dropdown
let activeDropdownId: string | null = null
// Custom event to notify other dropdowns to close
const DROPDOWN_TOGGLE_EVENT = 'status-dropdown-toggle'

interface StatusCellProps {
  value: BatchStatus
  onChange: (newValue: BatchStatus) => void
  id?: string // Optional ID to uniquely identify this dropdown
}

const StatusCell: React.FC<StatusCellProps> = ({ value, onChange, id = `status-${Math.random().toString(36).substr(2, 9)}` }) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const statusConfig = getStatusConfig(value) || {
    label: value,
    icon: () => null,
    color: '#B3B3B3',
    bgColor: 'rgba(179, 179, 179, 0.1)',
  }
  const options = getStatusOptions()
  const StatusIcon = statusConfig.icon

  // Check if status is "sold" - if so, we'll disable editing
  const isStatusLocked = value === 'sold' || value === 'in_progress'

  // Handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    // Listen for custom event to close this dropdown if another one opens
    const handleDropdownToggle = (event: CustomEvent) => {
      const { dropdownId } = event.detail
      if (dropdownId !== id && isOpen) {
        setIsOpen(false)
      }
    }

    // Add event listeners
    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener(DROPDOWN_TOGGLE_EVENT, handleDropdownToggle as EventListener)

    return () => {
      // Clean up event listeners
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener(DROPDOWN_TOGGLE_EVENT, handleDropdownToggle as EventListener)
    }
  }, [id, isOpen])

  // Function to toggle dropdown
  const toggleDropdown = () => {
    if (isStatusLocked) return

    const newState = !isOpen
    setIsOpen(newState)

    if (newState) {
      // Notify other dropdowns to close
      activeDropdownId = id
      document.dispatchEvent(
        new CustomEvent(DROPDOWN_TOGGLE_EVENT, {
          detail: { dropdownId: id },
        }),
      )
    }
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={toggleDropdown}
        className={`w-full flex items-center justify-between space-x-2 px-3 py-2 rounded-md text-sm ${
          !isStatusLocked ? 'focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50' : 'cursor-not-allowed'
        }`}
        style={{ backgroundColor: statusConfig.bgColor }}
        aria-haspopup={!isStatusLocked ? 'listbox' : undefined}
        aria-expanded={isOpen}
        disabled={isStatusLocked}
      >
        <span className="flex items-center">
          <StatusIcon className="mr-2 h-4 w-4" style={{ color: statusConfig.color }} />
          <span style={{ color: statusConfig.color }}>{statusConfig.label}</span>
        </span>
        {isStatusLocked ? <Lock className="h-4 w-4 text-secondary-text" /> : <ChevronDown className="h-4 w-4 text-secondary-text" />}
      </button>

      {isOpen && !isStatusLocked && (
        <div className="absolute z-10 mt-1 w-full bg-card-bg border border-card-border rounded-md shadow-lg">
          <ul className="py-1 max-h-60 overflow-auto" role="listbox" aria-labelledby="status-dropdown">
            {options.map((option) => {
              const optionConfig = getStatusConfig(option.value as BatchStatus) || {
                label: option.label,
                icon: () => null,
                color: '#B3B3B3',
                bgColor: 'rgba(179, 179, 179, 0.1)',
              }
              const OptionIcon = optionConfig.icon

              return (
                <li
                  key={option.value}
                  role="option"
                  aria-selected={value === option.value}
                  className={`cursor-pointer hover:bg-alt-bg px-3 py-2 transition-colors ${value === option.value ? 'bg-alt-bg' : ''}`}
                  onClick={() => {
                    onChange(option.value as BatchStatus)
                    setIsOpen(false)
                  }}
                >
                  <span className="flex items-center">
                    <OptionIcon className="mr-2 h-4 w-4" style={{ color: optionConfig.color }} />
                    <span className="text-primary-text">{option.label}</span>
                  </span>
                </li>
              )
            })}
          </ul>
        </div>
      )}
    </div>
  )
}

export default StatusCell
