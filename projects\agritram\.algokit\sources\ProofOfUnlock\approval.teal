#pragma version 10
#pragma typetrack false


main:
    intcblock 0 1 65 4
    bytecblock 0x00 "released" "withdrawn" "buyer" "milestones" "seller" "total_amount" 0x0000000000000000


    txn NumAppArgs
    bz main_bare_routing@10
    pushbytess 0x866aa25b 0x8446de14 0x2030a656 0x0ec3967a 0xc675ae0b
    txna ApplicationArgs 0
    match main_create_contract_route@3 main_fund_escrow_route@4 main_approve_milestone_route@5 main_release_funds_route@6 main_get_balance_route@7

main_after_if_else@14:


    intc_0
    return

main_get_balance_route@7:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    btoi
    txnas Assets


    callsub get_balance
    pushbytes 0x151f7c75
    swap
    concat
    log
    intc_1
    return

main_release_funds_route@6:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    btoi
    txnas Assets


    callsub release_funds
    intc_1
    return

main_approve_milestone_route@5:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1


    callsub approve_milestone
    intc_1
    return

main_fund_escrow_route@4:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txn GroupIndex
    intc_1
    -
    dup
    gtxns TypeEnum
    intc_3
    ==
    assert


    callsub fund_escrow
    intc_1
    return

main_create_contract_route@3:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    txna ApplicationArgs 2
    txna ApplicationArgs 3
    txna ApplicationArgs 4
    btoi
    txnas Assets
    txna ApplicationArgs 5


    callsub create_contract
    intc_1
    return

main_bare_routing@10:


    txn OnCompletion
    bnz main_after_if_else@14
    txn ApplicationID
    !
    assert
    intc_1
    return



create_contract:










    proto 5 0
    intc_0


    pushbytess "" "create_contract called"
    frame_dig -5
    concat
    frame_dig -4
    concat
    frame_dig -3
    concat
    frame_dig -2
    itob
    concat
    frame_dig -1
    concat
    log



    intc_0


    frame_dig -1
    intc_0
    extract_uint16
    intc_0

create_contract_for_header@1:


    frame_dig 4
    frame_dig 3
    <
    bz create_contract_after_for@4
    frame_dig -1
    extract 2 0
    frame_dig 4
    dup
    cover 2
    pushint 8
    *


    extract_uint64
    frame_dig 2
    +
    frame_bury 2
    intc_1
    +
    frame_bury 4
    b create_contract_for_header@1

create_contract_after_for@4:


    frame_dig 2
    pushint 100
    ==
    assert



    bytec_3
    frame_dig -5
    app_global_put


    bytec 5
    frame_dig -4
    app_global_put


    bytec 6
    frame_dig -3
    app_global_put


    bytec_1
    bytec 7
    app_global_put


    bytec_2
    bytec 7
    app_global_put


    intc_0
    bytec_3
    app_global_get_ex
    assert
    pushbytes "State after storing contract data"
    swap
    concat
    intc_0
    bytec 5
    app_global_get_ex
    assert
    concat
    intc_0
    bytec 6
    app_global_get_ex
    assert
    concat
    intc_0
    bytec_1
    app_global_get_ex
    assert
    concat
    intc_0
    bytec_2
    app_global_get_ex
    assert
    concat
    log







    itxn_begin


    global MinTxnFee


    global CurrentApplicationAddress


    intc_0
    itxn_field AssetAmount
    frame_dig -2
    itxn_field XferAsset
    itxn_field AssetReceiver


    intc_3
    itxn_field TypeEnum
    itxn_field Fee







    itxn_submit



    pushbytes 0x0000
    frame_bury 0
    intc_0
    frame_bury 1

create_contract_for_header@6:


    frame_dig 1
    frame_dig 3
    <
    bz create_contract_after_for@9
    frame_dig -1
    extract 2 0
    frame_dig 1
    dup
    cover 2
    pushint 8
    *
    pushint 8
    extract3








    frame_dig 0
    extract 2 0






    swap


    bytec_0






    concat
    intc_2
    intc_0
    setbit








    concat
    dup
    len
    pushint 9
    /
    itob
    extract 6 2
    swap
    concat
    frame_bury 0
    intc_1
    +
    frame_bury 1
    b create_contract_for_header@6

create_contract_after_for@9:


    bytec 4
    frame_dig 0
    app_global_put


    intc_0
    bytec 4
    app_global_get_ex
    assert
    pushbytes "Milestones after storing"
    swap
    concat
    log
    retsub



fund_escrow:



    proto 1 0


    frame_dig -1
    gtxns AssetSender
    pushbytes "fund_escrow called"
    dig 1
    concat
    frame_dig -1
    gtxns AssetReceiver
    swap
    dig 1
    concat
    frame_dig -1
    gtxns AssetAmount
    dup
    itob
    uncover 2
    dig 1
    concat
    log



    txn GroupIndex
    intc_1
    ==
    assert



    intc_0
    bytec_3
    app_global_get_ex
    assert
    dig 4
    ==
    assert


    global CurrentApplicationAddress
    uncover 3
    ==




    assert


    intc_0
    bytec 6
    app_global_get_ex
    assert
    btoi
    uncover 2
    ==
    assert


    pushbytes "Escrow funded"
    uncover 2
    concat
    swap
    concat
    log
    retsub



approve_milestone:



    proto 1 0


    pushbytes "approve_milestone called"
    frame_dig -1
    concat
    txn Sender
    concat
    log


    frame_dig -1
    btoi
    dup


    intc_0
    bytec 4
    app_global_get_ex
    swap
    dup
    cover 2
    cover 3
    assert


    dup
    intc_0
    extract_uint16
    dig 2
    >
    assert



    extract 2 0
    swap
    pushint 9
    *
    dup
    cover 2
    pushint 9
    extract3
    dup


    pushint 64
    getbit
    bytec_0
    intc_0
    uncover 2
    setbit
    bytec_0
    !=




    bz approve_milestone_bool_false@3


    frame_dig 3
    intc_2
    getbit
    bytec_0
    intc_0
    uncover 2
    setbit
    bytec_0
    !=




    bz approve_milestone_bool_false@3
    intc_1

approve_milestone_bool_merge@4:




    !
    assert



    txn Sender
    intc_0
    bytec_3
    app_global_get_ex
    assert
    ==
    assert


    frame_dig 3
    pushint 64
    intc_1
    setbit


    intc_2
    intc_1
    setbit
    dup
    frame_bury 3



    frame_dig 2
    pushint 2
    +
    frame_dig 1
    swap
    dig 2
    replace3


    bytec 4
    swap
    app_global_put


    frame_dig 0
    itob
    pushbytes "Milestone after approval"
    swap
    concat
    dig 1
    concat
    intc_0
    bytec 4
    app_global_get_ex
    assert
    concat
    log



    pushint 64
    getbit
    bytec_0
    intc_0
    uncover 2
    setbit
    bytec_0
    !=
    bz approve_milestone_after_if_else@10
    frame_dig 3
    intc_2
    getbit
    bytec_0
    intc_0
    uncover 2
    setbit
    bytec_0
    !=
    bz approve_milestone_after_if_else@10


    frame_dig 3
    intc_0
    extract_uint64


    intc_0
    bytec_1
    app_global_get_ex
    assert
    btoi


    intc_0
    bytec 6
    app_global_get_ex
    assert
    btoi
    uncover 2
    *
    pushint 100
    /


    +
    itob
    bytec_1
    swap
    app_global_put


    intc_0
    bytec_1
    app_global_get_ex
    assert
    pushbytes "Released updated"
    swap
    concat
    log

approve_milestone_after_if_else@10:
    retsub

approve_milestone_bool_false@3:
    intc_0
    b approve_milestone_bool_merge@4



release_funds:



    proto 1 0


    frame_dig -1
    itob
    pushbytes "release_funds called"
    swap
    concat
    txn Sender
    concat
    log


    txn Sender
    intc_0
    bytec_3
    app_global_get_ex
    assert
    !=
    bz release_funds_after_if_else@2


    pushbytes "Withdraw failed: Only buyer can release"
    txn Sender
    concat
    log


    err

release_funds_after_if_else@2:


    intc_0
    bytec_1
    app_global_get_ex
    assert
    btoi
    intc_0
    bytec_2
    app_global_get_ex
    assert
    btoi
    -
    dup


    intc_0
    <=
    bz release_funds_after_if_else@4


    itob
    pushbytes "Withdraw failed: No funds available"
    swap
    concat
    log


    err

release_funds_after_if_else@4:



    intc_0
    bytec_2
    app_global_get_ex
    assert
    btoi
    dig 1
    +
    itob
    bytec_2
    swap
    app_global_put


    intc_0
    bytec_2
    app_global_get_ex
    assert
    pushbytes "Withdrawn updated"
    swap
    concat
    log



    intc_0
    bytec 5
    app_global_get_ex
    assert
    pushbytes "Withdraw success"
    swap
    concat
    dig 1
    itob
    concat
    log








    itxn_begin


    global MinTxnFee


    intc_0
    bytec 5
    app_global_get_ex
    assert
    frame_dig -1
    itxn_field XferAsset
    uncover 2
    itxn_field AssetAmount
    itxn_field AssetReceiver



    intc_3
    itxn_field TypeEnum
    itxn_field Fee








    itxn_submit
    retsub



get_balance:



    proto 1 1


    frame_dig -1
    itob
    pushbytes "get_balance called"
    swap
    concat
    log


    global CurrentApplicationAddress


    frame_dig -1
    asset_holding_get AssetBalance



    bz get_balance_ternary_false@2
    frame_dig 0
    itob

get_balance_ternary_merge@3:



    swap
    retsub

get_balance_ternary_false@2:



    bytec 7
    b get_balance_ternary_merge@3