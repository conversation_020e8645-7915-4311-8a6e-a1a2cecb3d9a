export interface OwnershipTransfer {
  transaction_id: string
  timestamp: string
  from_user: {
    name: string
    account_address: string
    role: 'farmer' | 'trader' | 'manufacturer'
  }
  to_user: {
    name: string
    account_address: string
    role: 'farmer' | 'trader' | 'manufacturer'
  }
  cost: number
  transactionHash?: string
  notes?: string
  conditions?: string[]
  verifiedBy?: string
  documents?: {
    type: string
    url: string
  }[]
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
}

export interface Crop {
  crop_id: string
  quantity: number
  unit: 'kg' | 'tons'
  crop_grade: 'A' | 'B' | 'C'
  certifications?: string
  location: {
    address: string
    coordinates: {
      lat: number
      long: number
    }
  }
  growth_period: {
    start_date: string
    harvest_date: string
  }
  soil_type: string
  irrigation_type: string
  fertilizers_used: string[]
  created_at: string
}

export interface InventoryItem {
  id: number
  name: string
  description: string
  unit: string
  total_quantity_to_date: number
  ready_to_sell_quantity: number
  sold_quantity: number
  total_quantity_in_storage: number
  total_quantity_batches: number
  category: string
  icon?: React.ReactNode
}

export type BatchStatus = 'storage' | 'sold' | 'ready' | 'in_progress'

export type QualityGrade = 'A' | 'B' | 'C'

export interface Batch {
  crop_id: string
  created_at: string
  status: BatchStatus
  quantity: number
  unit: string
  crop_grade: QualityGrade
}
