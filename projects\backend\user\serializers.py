from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from rest_framework import serializers

from .models import User


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ["id", "name", "email", "password", "role", "account_address", "opt_in"]
        extra_kwargs = {
            "password": {"write_only": True},
        }

    def create(self, validated_data):
        """
        Create a new User instance with a hashed password.
        """
        password = validated_data.pop("password")
        print(validated_data)
        user = User(**validated_data)
        try:
            validate_password(password, user)
        except ValidationError as e:
            raise serializers.ValidationError({"error": e.messages})
        user.set_password(password)
        user.save()
        return user

    def update(self, instance, validated_data):
        """
        Update an existing User instance. If a new password is provided,
        hash it before saving.
        """
        if "password" in validated_data:
            raise serializers.ValidationError(
                {"password": "Password update is not allowed in this endpoint."}
            )
        if "email" in validated_data:
            raise serializers.ValidationError(
                {"email": "Email update is not allowed. Please contact support."}
            )
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
