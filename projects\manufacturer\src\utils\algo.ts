import { ProofOfUnlockFactory } from '@/contracts/ProofOfUnlock'
import { AlgorandClient } from '@algorandfoundation/algokit-utils'
import { TransactionSigner } from 'algosdk'
import { getAlgodConfigFromViteEnvironment, getIndexerConfigFromViteEnvironment } from './network/getAlgoClientConfigs'

const algodConfig = getAlgodConfigFromViteEnvironment()
const indexerConfig = getIndexerConfigFromViteEnvironment()

export const algorand = AlgorandClient.fromConfig({ algodConfig, indexerConfig })

export async function proofOfUnlockFactory(appName: string, activeAddress: string, signer: TransactionSigner) {
  const factory = algorand.client.getTypedAppFactory(ProofOfUnlockFactory, {
    defaultSender: activeAddress as string,
    defaultSigner: signer,
  })
  return factory.deploy({
    appName: appName,
  })
}
