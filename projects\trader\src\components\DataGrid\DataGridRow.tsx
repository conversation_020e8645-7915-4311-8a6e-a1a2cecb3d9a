import apiClient from '@/services/apiClient'
import { Batch, BatchStatus } from '@/utils/types'
import { Wheat } from 'lucide-react'
import React, { useState } from 'react'
import StatusCell from '../DataGrid/StatusCell'

interface DataGridRowProps {
  batch: Batch
  isEven: boolean
}

const DataGridRow: React.FC<DataGridRowProps> = ({ batch, isEven }) => {
  const [status, setStatus] = useState(batch.status)

  const bgColor = isEven ? 'bg-card-bg' : 'bg-alt-bg'

  const formatDate = (isoString: string) => {
    const date = new Date(isoString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  const handleStatusChange = (newStatus: BatchStatus) => {
    if (newStatus === status) return
    setStatus(newStatus)
    apiClient.put(`/inventory/crop-status/${batch.crop_id}/`, { status: newStatus })
  }

  return (
    <tr className={`${bgColor} border-b border-card-border hover:bg-accent-light/10 transition-colors`}>
      <td className="px-4 py-3 text-sm font-medium text-primary-text">
        <div className="flex items-center space-x-2">
          <Wheat className="h-4 w-4 text-accent" />
          <span>{batch.crop_id}</span>
        </div>
      </td>

      <td className="px-4 py-3 text-sm text-primary-text">{formatDate(batch.created_at)}</td>

      <td className="px-4 py-3 text-sm">
        <StatusCell value={status} onChange={handleStatusChange} />
      </td>

      <td className="px-4 py-3 text-sm text-primary-text">{batch.quantity}</td>

      <td className="px-4 py-3 text-sm">
        <div
          className={`w-full px-3 py-2 text-center font-medium ${
            batch.crop_grade === 'A'
              ? 'text-status-active-text'
              : batch.crop_grade === 'B'
              ? 'text-status-completed-text'
              : batch.crop_grade === 'C'
              ? 'text-status-pending-text'
              : 'text-button-danger'
          }`}
        >
          {batch.crop_grade}
        </div>
      </td>
    </tr>
  )
}

export default DataGridRow
