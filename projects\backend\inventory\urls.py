from django.urls import path
from .views import (
    get_entire_inventory,
    get_inventory,
    get_inventory_crop_status,
    get_trader_inventory_crop_status,
    update_crop_status,
)

urlpatterns = [
    path("inventory-quantity/", get_inventory, name="get-inventory"),
    path(
        "crop-status/",
        get_inventory_crop_status,
        name="get-inventory-crop-status",
    ),
    path("crop-status/<str:crop_id>/", update_crop_status, name="update-crop-status"),
    path("entire-inventory/", get_entire_inventory, name="get-entire-inventory"),
    path("trader-inventory-crop-status/<str:trader_id>/", get_trader_inventory_crop_status, name="get-trader-inventory-crop-status"),
]
