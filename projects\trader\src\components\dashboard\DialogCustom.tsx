import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

interface DialogCustomProps {
  asaBalance: number
  showBuyModal: boolean
  setShowBuyModal: (value: boolean) => void
}

export default function DialogCustom({ asaBalance, showBuyModal, setShowBuyModal }: DialogCustomProps) {
  const [tokenAmount, setTokenAmount] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [estimatedPrice, setEstimatedPrice] = useState(0)
  const navigate = useNavigate()

  const handleBuyTokens = async () => {
    if (!tokenAmount || parseFloat(tokenAmount) <= 0) {
      setError('Please enter a valid token amount')
      return
    }

    setIsLoading(true)
    try {
      setShowBuyModal(false)
      navigate('/buy', {
        state: {
          tokenAmount: estimatedPrice.toString(),
        },
      })
    } finally {
      setIsLoading(false)
    }
  }

  const TOKEN_PRICE = 1

  useEffect(() => {
    // Calculate estimated price whenever token amount changes
    const price = parseFloat(tokenAmount) * TOKEN_PRICE || 0
    setEstimatedPrice(price)
  }, [tokenAmount])

  return (
    <Dialog open={showBuyModal} onOpenChange={setShowBuyModal}>
      <DialogContent className="sm:max-w-[425px] bg-card-bg border border-card-border shadow-lg">
        <DialogHeader>
          <DialogTitle className="text-primary-text text-xl font-bold">Buy KTT Tokens</DialogTitle>
          <DialogDescription className="text-secondary-text">Enter the amount of tokens you want to purchase.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="tokenAmount" className="text-right text-primary-text">
              Amount
            </Label>
            <Input
              id="tokenAmount"
              type="number"
              value={tokenAmount}
              onChange={(e) => {
                setTokenAmount(e.target.value)
                setError('')
              }}
              className="col-span-3 bg-card-bg border-card-border focus:border-accent focus:ring-accent/20 transition-colors"
              placeholder="Enter token amount"
              min="0"
              disabled={isLoading}
            />
          </div>

          {/* Estimated Price Display */}
          <div className="bg-accent-light/5 p-3 rounded-lg">
            <div className="text-secondary-text text-sm mb-1">Estimated Price</div>
            <div className="text-primary-text font-medium">${estimatedPrice.toFixed(2)} USD</div>
          </div>

          {/* Balance Display */}
          <div className="bg-accent-light/5 p-3 rounded-lg">
            <div className="text-secondary-text text-sm mb-1">Current Balance</div>
            <div className="text-primary-text font-medium">{asaBalance.toFixed(2)} KTT</div>
          </div>

          {error && (
            <div className="bg-button-danger/10 p-3 rounded-lg">
              <p className="text-button-danger text-sm">{error}</p>
            </div>
          )}
        </div>

        <DialogFooter className="gap-3">
          <Button
            variant="outline"
            onClick={() => {
              setShowBuyModal(false)
              setTokenAmount('')
              setError('')
            }}
            disabled={isLoading}
            className="bg-card-bg border-card-border text-primary-text hover:bg-button-danger hover:text-button-text hover:border-button-danger transition-colors"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleBuyTokens}
            disabled={isLoading}
            className="bg-accent text-button-text hover:bg-accent-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-button-text"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </div>
            ) : (
              'Confirm Purchase'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
