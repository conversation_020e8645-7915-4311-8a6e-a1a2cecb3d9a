# flake8: noqa
# fmt: off
# mypy: ignore-errors
# This file was automatically generated by algokit-client-generator.
# DO NOT MODIFY IT BY HAND.
# requires: algokit-utils@^3.0.0

# common
import dataclasses
import typing
# core algosdk
import algosdk
from algosdk.transaction import OnComplete
from algosdk.atomic_transaction_composer import TransactionSigner
from algosdk.source_map import SourceMap
from algosdk.transaction import Transaction
from algosdk.v2client.models import SimulateTraceConfig
# utils
import algokit_utils
from algokit_utils import AlgorandClient as _AlgoKitAlgorandClient

_APP_SPEC_JSON = r"""{"arcs": [22, 28], "bareActions": {"call": [], "create": ["NoOp"]}, "methods": [{"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "address", "name": "buyer"}, {"type": "address", "name": "seller"}, {"type": "uint64", "name": "total_amount"}, {"type": "asset", "name": "asset"}, {"type": "uint64[]", "name": "milestone_percentages"}], "name": "create_contract", "returns": {"type": "void"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "axfer", "name": "payment"}], "name": "fund_escrow", "returns": {"type": "void"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "milestone_index"}], "name": "approve_milestone", "returns": {"type": "void"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "asset", "name": "asset"}], "name": "release_funds", "returns": {"type": "void"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "asset", "name": "asset"}], "name": "get_balance", "returns": {"type": "uint64"}, "events": [], "readonly": true, "recommendations": {}}], "name": "ProofOfUnlock", "state": {"keys": {"box": {}, "global": {"buyer": {"key": "YnV5ZXI=", "keyType": "AVMString", "valueType": "address"}, "seller": {"key": "c2VsbGVy", "keyType": "AVMString", "valueType": "address"}, "total_amount": {"key": "dG90YWxfYW1vdW50", "keyType": "AVMString", "valueType": "uint64"}, "milestones": {"key": "bWlsZXN0b25lcw==", "keyType": "AVMString", "valueType": "(uint64,bool,bool)[]"}, "released": {"key": "cmVsZWFzZWQ=", "keyType": "AVMString", "valueType": "uint64"}, "withdrawn": {"key": "d2l0aGRyYXdu", "keyType": "AVMString", "valueType": "uint64"}}, "local": {}}, "maps": {"box": {}, "global": {}, "local": {}}, "schema": {"global": {"bytes": 6, "ints": 0}, "local": {"bytes": 0, "ints": 0}}}, "structs": {}, "byteCode": {"approval": "CiAEAAFBBCYIAQAIcmVsZWFzZWQJd2l0aGRyYXduBWJ1eWVyCm1pbGVzdG9uZXMGc2VsbGVyDHRvdGFsX2Ftb3VudAgAAAAAAAAAADEbQQCcggUEhmqiWwSERt4UBCAwplYEDsOWegTGda4LNhoAjgUAVAA+AC8AHQACIkMxGRREMRhENhoBF8AwiAP3gAQVH3x1TFCwI0MxGRREMRhENhoBF8AwiALqI0MxGRREMRhENhoBiAHWI0MxGRREMRhEMRYjCUk4ECUSRIgBWyNDMRkURDEYRDYaATYaAjYaAzYaBBfAMDYaBYgADSNDMRlA/4kxGBREI0OKBQAiggIAFmNyZWF0ZV9jb250cmFjdCBjYWxsZWSL+1CL/FCL/VCL/hZQi/9QsCKL/yJZIosEiwMMQQAai/9XAgCLBElOAoEIC1uLAgiMAiMIjARC/96LAoFkEkQri/tnJwWL/GcnBov9ZyknB2cqJwdnIitlRIAhU3RhdGUgYWZ0ZXIgc3RvcmluZyBjb250cmFjdCBkYXRhTFAiJwVlRFAiJwZlRFAiKWVEUCIqZURQsLEyADIKIrISi/6yEbIUJbIQsgGzgAIAAIwAIowBiwGLAwxBADCL/1cCAIsBSU4CgQgLgQhYiwBXAgBMKFAkIlRQSRWBCQoWVwYCTFCMACMIjAFC/8gnBIsAZyInBGVEgBhNaWxlc3RvbmVzIGFmdGVyIHN0b3JpbmdMULCJigEAi/84E4ASZnVuZF9lc2Nyb3cgY2FsbGVkSwFQi/84FExLAVCL/zgSSRZPAksBULAxFiMSRCIrZURLBBJEMgpPAxJEIicGZUQXTwISRIANRXNjcm93IGZ1bmRlZE8CUExQsImKAQCAGGFwcHJvdmVfbWlsZXN0b25lIGNhbGxlZIv/UDEAULCL/xdJIicEZUxJTgJOA0RJIllLAg1EVwIATIEJC0lOAoEJWEmBQFMoIk8CVCgTQQCsiwMkUygiTwJUKBNBAJ4jFEQxACIrZUQSRIsDgUAjVCQjVEmMA4sCgQIIiwFMSwJdJwRMZ4sAFoAYTWlsZXN0b25lIGFmdGVyIGFwcHJvdmFsTFBLAVAiJwRlRFCwgUBTKCJPAlQoE0EAQYsDJFMoIk8CVCgTQQAziwMiWyIpZUQXIicGZUQXTwILgWQKCBYpTGciKWVEgBBSZWxlYXNlZCB1cGRhdGVkTFCwiSJC/1+KAQCL/xaAFHJlbGVhc2VfZnVuZHMgY2FsbGVkTFAxAFCwMQAiK2VEE0EALoAnV2l0aGRyYXcgZmFpbGVkOiBPbmx5IGJ1eWVyIGNhbiByZWxlYXNlMQBQsAAiKWVEFyIqZUQXCUkiDkEAKhaAI1dpdGhkcmF3IGZhaWxlZDogTm8gZnVuZHMgYXZhaWxhYmxlTFCwACIqZUQXSwEIFipMZyIqZUSAEVdpdGhkcmF3biB1cGRhdGVkTFCwIicFZUSAEFdpdGhkcmF3IHN1Y2Nlc3NMUEsBFlCwsTIAIicFZUSL/7IRTwKyErIUJbIQsgGziYoBAYv/FoASZ2V0X2JhbGFuY2UgY2FsbGVkTFCwMgqL/3AAQQAFiwAWTIknB0L/+Q==", "clear": "CoEBQw=="}, "compilerInfo": {"compiler": "puya", "compilerVersion": {"major": 4, "minor": 7, "patch": 0}}, "events": [], "networks": {}, "source": {"approval": "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", "clear": "I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"}, "sourceInfo": {"approval": {"pcOffsetMethod": "none", "sourceInfo": [{"pc": [754], "errorMessage": "Already approved"}, {"pc": [630], "errorMessage": "Incorrect amount"}, {"pc": [479, 723], "errorMessage": "Index access is out of bounds"}, {"pc": [614], "errorMessage": "Invalid buyer"}, {"pc": [710], "errorMessage": "Invalid milestone index"}, {"pc": [620], "errorMessage": "Invalid receiver"}, {"pc": [606], "errorMessage": "Must be second transaction in group"}, {"pc": [1062], "errorMessage": "No funds available"}, {"pc": [128, 155, 173, 188, 210], "errorMessage": "OnCompletion is not NoOp"}, {"pc": [1003], "errorMessage": "Only buyer can release"}, {"pc": [340], "errorMessage": "Total percentages must equal 100%"}, {"pc": [762], "errorMessage": "Unauthorized"}, {"pc": [245], "errorMessage": "can only call when creating"}, {"pc": [131, 158, 176, 191, 213], "errorMessage": "can only call when not creating"}, {"pc": [366, 610, 760, 953], "errorMessage": "check self.buyer exists"}, {"pc": [521, 703, 828], "errorMessage": "check self.milestones exists"}, {"pc": [419, 865, 887, 1007], "errorMessage": "check self.released exists"}, {"pc": [408, 1105, 1138], "errorMessage": "check self.seller exists"}, {"pc": [414, 625, 871], "errorMessage": "check self.total_amount exists"}, {"pc": [424, 1012, 1066, 1078], "errorMessage": "check self.withdrawn exists"}, {"pc": [201], "errorMessage": "transaction type is axfer"}]}, "clear": {"pcOffsetMethod": "none", "sourceInfo": []}}, "templateVariables": {}}"""
APP_SPEC = algokit_utils.Arc56Contract.from_json(_APP_SPEC_JSON)

def _parse_abi_args(args: object | None = None) -> list[object] | None:
    """Helper to parse ABI args into the format expected by underlying client"""
    if args is None:
        return None

    def convert_dataclass(value: object) -> object:
        if dataclasses.is_dataclass(value):
            return tuple(convert_dataclass(getattr(value, field.name)) for field in dataclasses.fields(value))
        elif isinstance(value, (list, tuple)):
            return type(value)(convert_dataclass(item) for item in value)
        return value

    match args:
        case tuple():
            method_args = list(args)
        case _ if dataclasses.is_dataclass(args):
            method_args = [getattr(args, field.name) for field in dataclasses.fields(args)]
        case _:
            raise ValueError("Invalid 'args' type. Expected 'tuple' or 'TypedDict' for respective typed arguments.")

    return [
        convert_dataclass(arg) if not isinstance(arg, algokit_utils.AppMethodCallTransactionArgument) else arg
        for arg in method_args
    ] if method_args else None

def _init_dataclass(cls: type, data: dict) -> object:
    """
    Recursively instantiate a dataclass of type `cls` from `data`.

    For each field on the dataclass, if the field type is also a dataclass
    and the corresponding data is a dict, instantiate that field recursively.
    """
    field_values = {}
    for field in dataclasses.fields(cls):
        field_value = data.get(field.name)
        # Check if the field expects another dataclass and the value is a dict.
        if dataclasses.is_dataclass(field.type) and isinstance(field_value, dict):
            field_values[field.name] = _init_dataclass(typing.cast(type, field.type), field_value)
        else:
            field_values[field.name] = field_value
    return cls(**field_values)

@dataclasses.dataclass(frozen=True, kw_only=True)
class CreateContractArgs:
    """Dataclass for create_contract arguments"""
    buyer: str
    seller: str
    total_amount: int
    asset: int
    milestone_percentages: list[int]

    @property
    def abi_method_signature(self) -> str:
        return "create_contract(address,address,uint64,asset,uint64[])void"

@dataclasses.dataclass(frozen=True, kw_only=True)
class FundEscrowArgs:
    """Dataclass for fund_escrow arguments"""
    payment: algokit_utils.AppMethodCallTransactionArgument

    @property
    def abi_method_signature(self) -> str:
        return "fund_escrow(axfer)void"

@dataclasses.dataclass(frozen=True, kw_only=True)
class ApproveMilestoneArgs:
    """Dataclass for approve_milestone arguments"""
    milestone_index: int

    @property
    def abi_method_signature(self) -> str:
        return "approve_milestone(uint64)void"

@dataclasses.dataclass(frozen=True, kw_only=True)
class ReleaseFundsArgs:
    """Dataclass for release_funds arguments"""
    asset: int

    @property
    def abi_method_signature(self) -> str:
        return "release_funds(asset)void"

@dataclasses.dataclass(frozen=True, kw_only=True)
class GetBalanceArgs:
    """Dataclass for get_balance arguments"""
    asset: int

    @property
    def abi_method_signature(self) -> str:
        return "get_balance(asset)uint64"


class ProofOfUnlockParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def create_contract(
        self,
        args: tuple[str, str, int, int, list[int]] | CreateContractArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "create_contract(address,address,uint64,asset,uint64[])void",
            "args": method_args,
        }))

    def fund_escrow(
        self,
        args: tuple[algokit_utils.AppMethodCallTransactionArgument] | FundEscrowArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "fund_escrow(axfer)void",
            "args": method_args,
        }))

    def approve_milestone(
        self,
        args: tuple[int] | ApproveMilestoneArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "approve_milestone(uint64)void",
            "args": method_args,
        }))

    def release_funds(
        self,
        args: tuple[int] | ReleaseFundsArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "release_funds(asset)void",
            "args": method_args,
        }))

    def get_balance(
        self,
        args: tuple[int] | GetBalanceArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_balance(asset)uint64",
            "args": method_args,
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> algokit_utils.AppCallParams:
        return self.app_client.params.bare.clear_state(
            params,
            
        )


class ProofOfUnlockCreateTransactionParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def create_contract(
        self,
        args: tuple[str, str, int, int, list[int]] | CreateContractArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "create_contract(address,address,uint64,asset,uint64[])void",
            "args": method_args,
        }))

    def fund_escrow(
        self,
        args: tuple[algokit_utils.AppMethodCallTransactionArgument] | FundEscrowArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "fund_escrow(axfer)void",
            "args": method_args,
        }))

    def approve_milestone(
        self,
        args: tuple[int] | ApproveMilestoneArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "approve_milestone(uint64)void",
            "args": method_args,
        }))

    def release_funds(
        self,
        args: tuple[int] | ReleaseFundsArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "release_funds(asset)void",
            "args": method_args,
        }))

    def get_balance(
        self,
        args: tuple[int] | GetBalanceArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_balance(asset)uint64",
            "args": method_args,
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> Transaction:
        return self.app_client.create_transaction.bare.clear_state(
            params,
            
        )


class ProofOfUnlockSend:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def create_contract(
        self,
        args: tuple[str, str, int, int, list[int]] | CreateContractArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "create_contract(address,address,uint64,asset,uint64[])void",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[None], parsed_response)

    def fund_escrow(
        self,
        args: tuple[algokit_utils.AppMethodCallTransactionArgument] | FundEscrowArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "fund_escrow(axfer)void",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[None], parsed_response)

    def approve_milestone(
        self,
        args: tuple[int] | ApproveMilestoneArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "approve_milestone(uint64)void",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[None], parsed_response)

    def release_funds(
        self,
        args: tuple[int] | ReleaseFundsArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "release_funds(asset)void",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[None], parsed_response)

    def get_balance(
        self,
        args: tuple[int] | GetBalanceArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[int]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_balance(asset)uint64",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[int], parsed_response)

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[algokit_utils.ABIReturn]:
        return self.app_client.send.bare.clear_state(
            params,
            send_params=send_params,
        )


class GlobalStateValue(typing.TypedDict):
    """Shape of global_state state key values"""
    buyer: str
    seller: str
    total_amount: int
    milestones: list[tuple[int, bool, bool]]
    released: int
    withdrawn: int

class ProofOfUnlockState:
    """Methods to access state for the current ProofOfUnlock app"""

    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    @property
    def global_state(
        self
    ) -> "_GlobalState":
            """Methods to access global_state for the current app"""
            return _GlobalState(self.app_client)

class _GlobalState:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client
        
        # Pre-generated mapping of value types to their struct classes
        self._struct_classes: dict[str, typing.Type[typing.Any]] = {}

    def get_all(self) -> GlobalStateValue:
        """Get all current keyed values from global_state state"""
        result = self.app_client.state.global_state.get_all()
        if not result:
            return typing.cast(GlobalStateValue, {})

        converted = {}
        for key, value in result.items():
            key_info = self.app_client.app_spec.state.keys.global_state.get(key)
            struct_class = self._struct_classes.get(key_info.value_type) if key_info else None
            converted[key] = (
                _init_dataclass(struct_class, value) if struct_class and isinstance(value, dict)
                else value
            )
        return typing.cast(GlobalStateValue, converted)

    @property
    def buyer(self) -> str:
        """Get the current value of the buyer key in global_state state"""
        value = self.app_client.state.global_state.get_value("buyer")
        if isinstance(value, dict) and "address" in self._struct_classes:
            return _init_dataclass(self._struct_classes["address"], value)  # type: ignore
        return typing.cast(str, value)

    @property
    def seller(self) -> str:
        """Get the current value of the seller key in global_state state"""
        value = self.app_client.state.global_state.get_value("seller")
        if isinstance(value, dict) and "address" in self._struct_classes:
            return _init_dataclass(self._struct_classes["address"], value)  # type: ignore
        return typing.cast(str, value)

    @property
    def total_amount(self) -> int:
        """Get the current value of the total_amount key in global_state state"""
        value = self.app_client.state.global_state.get_value("total_amount")
        if isinstance(value, dict) and "uint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["uint64"], value)  # type: ignore
        return typing.cast(int, value)

    @property
    def milestones(self) -> list[tuple[int, bool, bool]]:
        """Get the current value of the milestones key in global_state state"""
        value = self.app_client.state.global_state.get_value("milestones")
        if isinstance(value, dict) and "(uint64,bool,bool)[]" in self._struct_classes:
            return _init_dataclass(self._struct_classes["(uint64,bool,bool)[]"], value)  # type: ignore
        return typing.cast(list[tuple[int, bool, bool]], value)

    @property
    def released(self) -> int:
        """Get the current value of the released key in global_state state"""
        value = self.app_client.state.global_state.get_value("released")
        if isinstance(value, dict) and "uint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["uint64"], value)  # type: ignore
        return typing.cast(int, value)

    @property
    def withdrawn(self) -> int:
        """Get the current value of the withdrawn key in global_state state"""
        value = self.app_client.state.global_state.get_value("withdrawn")
        if isinstance(value, dict) and "uint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["uint64"], value)  # type: ignore
        return typing.cast(int, value)

class ProofOfUnlockClient:
    """Client for interacting with ProofOfUnlock smart contract"""

    @typing.overload
    def __init__(self, app_client: algokit_utils.AppClient) -> None: ...
    
    @typing.overload
    def __init__(
        self,
        *,
        algorand: _AlgoKitAlgorandClient,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None: ...

    def __init__(
        self,
        app_client: algokit_utils.AppClient | None = None,
        *,
        algorand: _AlgoKitAlgorandClient | None = None,
        app_id: int | None = None,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None:
        if app_client:
            self.app_client = app_client
        elif algorand and app_id:
            self.app_client = algokit_utils.AppClient(
                algokit_utils.AppClientParams(
                    algorand=algorand,
                    app_spec=APP_SPEC,
                    app_id=app_id,
                    app_name=app_name,
                    default_sender=default_sender,
                    default_signer=default_signer,
                    approval_source_map=approval_source_map,
                    clear_source_map=clear_source_map,
                )
            )
        else:
            raise ValueError("Either app_client or algorand and app_id must be provided")
    
        self.params = ProofOfUnlockParams(self.app_client)
        self.create_transaction = ProofOfUnlockCreateTransactionParams(self.app_client)
        self.send = ProofOfUnlockSend(self.app_client)
        self.state = ProofOfUnlockState(self.app_client)

    @staticmethod
    def from_creator_and_name(
        creator_address: str,
        app_name: str,
        algorand: _AlgoKitAlgorandClient,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
    ) -> "ProofOfUnlockClient":
        return ProofOfUnlockClient(
            algokit_utils.AppClient.from_creator_and_name(
                creator_address=creator_address,
                app_name=app_name,
                app_spec=APP_SPEC,
                algorand=algorand,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
                ignore_cache=ignore_cache,
                app_lookup_cache=app_lookup_cache,
            )
        )
    
    @staticmethod
    def from_network(
        algorand: _AlgoKitAlgorandClient,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "ProofOfUnlockClient":
        return ProofOfUnlockClient(
            algokit_utils.AppClient.from_network(
                app_spec=APP_SPEC,
                algorand=algorand,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    @property
    def app_id(self) -> int:
        return self.app_client.app_id
    
    @property
    def app_address(self) -> str:
        return self.app_client.app_address
    
    @property
    def app_name(self) -> str:
        return self.app_client.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_client.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_client.algorand

    def clone(
        self,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "ProofOfUnlockClient":
        return ProofOfUnlockClient(
            self.app_client.clone(
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    def new_group(self) -> "ProofOfUnlockComposer":
        return ProofOfUnlockComposer(self)

    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["create_contract(address,address,uint64,asset,uint64[])void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["fund_escrow(axfer)void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["approve_milestone(uint64)void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["release_funds(asset)void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["get_balance(asset)uint64"],
        return_value: algokit_utils.ABIReturn | None
    ) -> int | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None: ...

    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None | int:
        """Decode ABI return value for the given method."""
        if return_value is None:
            return None
    
        arc56_method = self.app_spec.get_arc56_method(method)
        decoded = return_value.get_arc56_value(arc56_method, self.app_spec.structs)
    
        # If method returns a struct, convert the dict to appropriate dataclass
        if (arc56_method and
            arc56_method.returns and
            arc56_method.returns.struct and
            isinstance(decoded, dict)):
            struct_class = globals().get(arc56_method.returns.struct)
            if struct_class:
                return struct_class(**typing.cast(dict, decoded))
        return decoded


@dataclasses.dataclass(frozen=True)
class ProofOfUnlockBareCallCreateParams(algokit_utils.AppClientBareCallCreateParams):
    """Parameters for creating ProofOfUnlock contract with bare calls"""
    on_complete: typing.Literal[OnComplete.NoOpOC] | None = None

    def to_algokit_utils_params(self) -> algokit_utils.AppClientBareCallCreateParams:
        return algokit_utils.AppClientBareCallCreateParams(**self.__dict__)

class ProofOfUnlockFactory(algokit_utils.TypedAppFactoryProtocol[ProofOfUnlockBareCallCreateParams, None, None]):
    """Factory for deploying and managing ProofOfUnlockClient smart contracts"""

    def __init__(
        self,
        algorand: _AlgoKitAlgorandClient,
        *,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        version: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ):
        self.app_factory = algokit_utils.AppFactory(
            params=algokit_utils.AppFactoryParams(
                algorand=algorand,
                app_spec=APP_SPEC,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                version=version,
                compilation_params=compilation_params,
            )
        )
        self.params = ProofOfUnlockFactoryParams(self.app_factory)
        self.create_transaction = ProofOfUnlockFactoryCreateTransaction(self.app_factory)
        self.send = ProofOfUnlockFactorySend(self.app_factory)

    @property
    def app_name(self) -> str:
        return self.app_factory.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_factory.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_factory.algorand

    def deploy(
        self,
        *,
        on_update: algokit_utils.OnUpdate | None = None,
        on_schema_break: algokit_utils.OnSchemaBreak | None = None,
        create_params: ProofOfUnlockBareCallCreateParams | None = None,
        update_params: None = None,
        delete_params: None = None,
        existing_deployments: algokit_utils.ApplicationLookup | None = None,
        ignore_cache: bool = False,
        app_name: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
    ) -> tuple[ProofOfUnlockClient, algokit_utils.AppFactoryDeployResult]:
        """Deploy the application"""
        deploy_response = self.app_factory.deploy(
            on_update=on_update,
            on_schema_break=on_schema_break,
            create_params=create_params.to_algokit_utils_params() if create_params else None,
            update_params=update_params,
            delete_params=delete_params,
            existing_deployments=existing_deployments,
            ignore_cache=ignore_cache,
            app_name=app_name,
            compilation_params=compilation_params,
            send_params=send_params,
        )

        return ProofOfUnlockClient(deploy_response[0]), deploy_response[1]

    def get_app_client_by_creator_and_name(
        self,
        creator_address: str,
        app_name: str,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> ProofOfUnlockClient:
        """Get an app client by creator address and name"""
        return ProofOfUnlockClient(
            self.app_factory.get_app_client_by_creator_and_name(
                creator_address,
                app_name,
                default_sender,
                default_signer,
                ignore_cache,
                app_lookup_cache,
                approval_source_map,
                clear_source_map,
            )
        )

    def get_app_client_by_id(
        self,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> ProofOfUnlockClient:
        """Get an app client by app ID"""
        return ProofOfUnlockClient(
            self.app_factory.get_app_client_by_id(
                app_id,
                app_name,
                default_sender,
                default_signer,
                approval_source_map,
                clear_source_map,
            )
        )


class ProofOfUnlockFactoryParams:
    """Parameters for creating transactions for ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = ProofOfUnlockFactoryCreateParams(app_factory)
        self.update = ProofOfUnlockFactoryUpdateParams(app_factory)
        self.delete = ProofOfUnlockFactoryDeleteParams(app_factory)

class ProofOfUnlockFactoryCreateParams:
    """Parameters for 'create' operations of ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateParams:
        """Creates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            compilation_params=compilation_params)

    def create_contract(
        self,
        args: tuple[str, str, int, int, list[int]] | CreateContractArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the create_contract(address,address,uint64,asset,uint64[])void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "create_contract(address,address,uint64,asset,uint64[])void",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def fund_escrow(
        self,
        args: tuple[algokit_utils.AppMethodCallTransactionArgument] | FundEscrowArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the fund_escrow(axfer)void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "fund_escrow(axfer)void",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def approve_milestone(
        self,
        args: tuple[int] | ApproveMilestoneArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the approve_milestone(uint64)void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "approve_milestone(uint64)void",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def release_funds(
        self,
        args: tuple[int] | ReleaseFundsArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the release_funds(asset)void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "release_funds(asset)void",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def get_balance(
        self,
        args: tuple[int] | GetBalanceArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the get_balance(asset)uint64 ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "get_balance(asset)uint64",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

class ProofOfUnlockFactoryUpdateParams:
    """Parameters for 'update' operations of ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppUpdateParams:
        """Updates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_update(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )

class ProofOfUnlockFactoryDeleteParams:
    """Parameters for 'delete' operations of ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppDeleteParams:
        """Deletes an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_delete(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )


class ProofOfUnlockFactoryCreateTransaction:
    """Create transactions for ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = ProofOfUnlockFactoryCreateTransactionCreate(app_factory)


class ProofOfUnlockFactoryCreateTransactionCreate:
    """Create new instances of ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
    ) -> Transaction:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.create_transaction.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
        )


class ProofOfUnlockFactorySend:
    """Send calls to ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = ProofOfUnlockFactorySendCreate(app_factory)


class ProofOfUnlockFactorySendCreate:
    """Send create calls to ProofOfUnlock contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ) -> tuple[ProofOfUnlockClient, algokit_utils.SendAppCreateTransactionResult]:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        result = self.app_factory.send.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            send_params=send_params,
            compilation_params=compilation_params
        )
        return ProofOfUnlockClient(result[0]), result[1]


class ProofOfUnlockComposer:
    """Composer for creating transaction groups for ProofOfUnlock contract calls"""

    def __init__(self, client: "ProofOfUnlockClient"):
        self.client = client
        self._composer = client.algorand.new_group()
        self._result_mappers: list[typing.Callable[[algokit_utils.ABIReturn | None], object] | None] = []

    def create_contract(
        self,
        args: tuple[str, str, int, int, list[int]] | CreateContractArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "ProofOfUnlockComposer":
        self._composer.add_app_call_method_call(
            self.client.params.create_contract(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "create_contract(address,address,uint64,asset,uint64[])void", v
            )
        )
        return self

    def fund_escrow(
        self,
        args: tuple[algokit_utils.AppMethodCallTransactionArgument] | FundEscrowArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "ProofOfUnlockComposer":
        self._composer.add_app_call_method_call(
            self.client.params.fund_escrow(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "fund_escrow(axfer)void", v
            )
        )
        return self

    def approve_milestone(
        self,
        args: tuple[int] | ApproveMilestoneArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "ProofOfUnlockComposer":
        self._composer.add_app_call_method_call(
            self.client.params.approve_milestone(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "approve_milestone(uint64)void", v
            )
        )
        return self

    def release_funds(
        self,
        args: tuple[int] | ReleaseFundsArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "ProofOfUnlockComposer":
        self._composer.add_app_call_method_call(
            self.client.params.release_funds(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "release_funds(asset)void", v
            )
        )
        return self

    def get_balance(
        self,
        args: tuple[int] | GetBalanceArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "ProofOfUnlockComposer":
        self._composer.add_app_call_method_call(
            self.client.params.get_balance(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "get_balance(asset)uint64", v
            )
        )
        return self

    def clear_state(
        self,
        *,
        args: list[bytes] | None = None,
        params: algokit_utils.CommonAppCallParams | None = None,
    ) -> "ProofOfUnlockComposer":
        params=params or algokit_utils.CommonAppCallParams()
        self._composer.add_app_call(
            self.client.params.clear_state(
                algokit_utils.AppClientBareCallParams(
                    **{
                        **dataclasses.asdict(params),
                        "args": args
                    }
                )
            )
        )
        return self
    
    def add_transaction(
        self, txn: Transaction, signer: TransactionSigner | None = None
    ) -> "ProofOfUnlockComposer":
        self._composer.add_transaction(txn, signer)
        return self
    
    def composer(self) -> algokit_utils.TransactionComposer:
        return self._composer
    
    def simulate(
        self,
        allow_more_logs: bool | None = None,
        allow_empty_signatures: bool | None = None,
        allow_unnamed_resources: bool | None = None,
        extra_opcode_budget: int | None = None,
        exec_trace_config: SimulateTraceConfig | None = None,
        simulation_round: int | None = None,
        skip_signatures: bool | None = None,
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.simulate(
            allow_more_logs=allow_more_logs,
            allow_empty_signatures=allow_empty_signatures,
            allow_unnamed_resources=allow_unnamed_resources,
            extra_opcode_budget=extra_opcode_budget,
            exec_trace_config=exec_trace_config,
            simulation_round=simulation_round,
            skip_signatures=skip_signatures,
        )
    
    def send(
        self,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.send(send_params)
