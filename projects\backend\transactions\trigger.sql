CREATE OR <PERSON><PERSON>LACE FUNCTION update_monthly_expense()
RETURNS TRIGGER AS $$
DECLARE
    existing_total DECIMAL(18, 6);
    existing_count INT;
BEGIN
    -- Check if a MonthlyExpense record exists for the user, year, month, and transaction type
    SELECT total_amount, transaction_count INTO existing_total, existing_count
    FROM transactions_monthlyexpense
    WHERE user_id = NEW.user_id
      AND year = EXTRACT(YEAR FROM NEW.timestamp)
      AND month = EXTRACT(MONTH FROM NEW.timestamp)
      AND transaction_type = NEW.transaction_type
    FOR UPDATE;

    -- If it exists, update the record
    IF FOUND THEN
        UPDATE transactions_monthlyexpense
        SET total_amount = existing_total + NEW.amount_usd,
            transaction_count = existing_count + 1
        WHERE user_id = NEW.user_id
          AND year = EXTRACT(YEAR FROM NEW.timestamp)
          AND month = EXTRACT(MONTH FROM NEW.timestamp)
          AND transaction_type = NEW.transaction_type;
    ELSE
        -- If it does not exist, insert a new record
        INSERT INTO transactions_monthlyexpense (user_id, year, month, total_amount, transaction_type, transaction_count)
        VALUES (NEW.user_id, EXTRACT(YEAR FROM NEW.timestamp), EXTRACT(MONTH FROM NEW.timestamp), NEW.amount_usd, NEW.transaction_type, 1);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create or replace the trigger to fire on both INSERT and UPDATE
CREATE OR REPLACE TRIGGER update_monthly_expense_trigger
AFTER INSERT OR UPDATE ON transactions_transaction
FOR EACH ROW
EXECUTE FUNCTION update_monthly_expense();