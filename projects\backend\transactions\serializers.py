from rest_framework import serializers
from .models import Transaction, MonthlyExpense
from django.contrib.auth import get_user_model

User = get_user_model()


class TransactionSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source="user.name", read_only=True)
    timestamp = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    class Meta:
        model = Transaction
        fields = [
            "transaction_id",
            "algorand_tx_id",
            "username",
            "from_address",
            "to_address",
            "amount_usd",
            "amount_stablecoin_ktt",
            "amount_stablecoin_kct",
            "transaction_type",
            "status",
            "timestamp",
            "gas_fee",
        ]
        read_only_fields = ["transaction_id", "timestamp", "username"]

    def create(self, validated_data):
        # Automatically set the user from the request
        user = self.context["request"].user
        validated_data["user"] = user
        return super().create(validated_data)


class MonthlyExpenseSerializer(serializers.ModelSerializer):
    month_name = serializers.SerializerMethodField()

    class Meta:
        model = MonthlyExpense
        fields = [
            "id",
            "year",
            "month",
            "month_name",
            "total_amount",
            "transaction_type",
            "transaction_count",
        ]
        read_only_fields = ["id"]

    def get_month_name(self, obj):
        month_names = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
        ]
        return month_names[obj.month - 1]


class YearlyExpenseSerializer(serializers.Serializer):
    year = serializers.IntegerField()
    monthly_data = serializers.ListField(
        child=serializers.DecimalField(max_digits=18, decimal_places=6)
    )
    labels = serializers.ListField(child=serializers.CharField())


class MonthlyExpenseSummarySerializer(serializers.ModelSerializer):
    month_year = serializers.SerializerMethodField()

    class Meta:
        model = MonthlyExpense
        fields = ["month_year", "total_amount"]

    def get_month_year(self, obj):
        month_names = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
        ]
        return f"{month_names[obj.month - 1]} {obj.year}"
