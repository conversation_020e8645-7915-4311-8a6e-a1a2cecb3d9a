import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import AuthLayout from '../components/AuthLayout'
import FormInput from '../components/FormInput'
import { useToast } from '../components/ui/use-toast'
import { login } from '../services/authService'
import { useAuthStore } from '../stores/authStore'

const Login = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const { toast } = useToast()
  const navigate = useNavigate()

  // Use auth store
  const { login: loginUser, loading, setLoading, setError } = useAuthStore()

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid'
    }

    if (!password) {
      newErrors.password = 'Password is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setLoading(true)

    try {
      console.log(email, password)
      const loginResponse = await login(email, password)

      // Use the auth store to handle login
      loginUser(loginResponse)

      toast({
        title: 'Success!',
        description: 'You have successfully logged in.',
      })

      // Check if user has account_address (wallet connected)
      if (!loginResponse.user.account_address) {
        navigate('/onboarding')
      } else {
        navigate('/dashboard')
      }
    } catch (error) {
      console.error('Login error:', error)
      setError('An error occurred during login. Please check your credentials.')
      toast({
        title: 'Error!',
        description: 'An error occurred during login. Please check your credentials.',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthLayout title="Welcome Back To Agritram" subtitle="Enter your credentials to access your account">
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput label="Email" type="email" value={email} onChange={setEmail} error={errors.email} placeholder="Enter your email" />
        <FormInput
          label="Password"
          type="password"
          value={password}
          onChange={setPassword}
          error={errors.password}
          placeholder="Enter your password"
        />

        <div className="flex items-center justify-between">
          <Link to="/forgot-password" className="text-sm text-link-text hover:text-link-text/80 font-bold">
            Forgot password?
          </Link>
        </div>

        <button
          type="submit"
          disabled={loading}
          className={`w-full py-3 px-4 bg-button-bg text-button-text rounded-lg font-medium border border-border-dotted border-dotted ${
            loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-button-bg-hover'
          }`}
        >
          {loading ? 'Signing in...' : 'Sign In'}
        </button>

        <p className="text-center text-primary-text">
          <span className="opacity-60">Don't have an account? </span>
          <Link to="/register" className="text-link-text  hover:text-link-text/80 font-bold">
            Register now
          </Link>
        </p>
      </form>
    </AuthLayout>
  )
}

export default Login
