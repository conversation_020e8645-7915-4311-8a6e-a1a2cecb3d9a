import { InventoryItem } from '@/utils/types'
import { Loader2, Shopping<PERSON>art, Wheat } from 'lucide-react'
import { useEffect, useState } from 'react'

import BuyDialog from '@/components/traderInventory/BuyDialog'
import apiClient from '@/services/apiClient'

const TraderInventory = () => {
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const handleBuyClick = (item: InventoryItem) => {
    setSelectedItem(item)
    setIsDialogOpen(true)
  }

  useEffect(() => {
    // Define the type for the API response
    type ApiInventoryItem = {
      id: number
      crop_name?: string
      trader: {
        id: number
        name: string
        account_address: string
        email?: string
        role?: string
        opt_in?: boolean
      }
      description?: string
      unit?: string
      ready_to_sell_quantity: string | number
      total_quantity_in_storage: string | number
      sold_quantity: string | number
      total_quantity_batches: number
      category?: string
    }
    const fetchInventory = async () => {
      setLoading(true)
      setError(null)
      try {
        const res = await apiClient.get('/inventory/entire-inventory/')
        const data: ApiInventoryItem[] = res.data

        const mapped: InventoryItem[] = data.map((item) => ({
          id: item.id,
          trader: item.trader,
          crop_name: item.crop_name || 'Cocoa Beans',
          description: item.description || 'Premium quality organic cocoa beans',
          unit: item.unit || 'tonnes',
          ready_to_sell_quantity: Number(item.ready_to_sell_quantity),
          total_quantity_in_storage: Number(item.total_quantity_in_storage),
          sold_quantity: Number(item.sold_quantity),
          total_quantity_batches: item.total_quantity_batches,
          category: item.category || 'Beans',
          icon: <Wheat className="w-5 h-5" />, // fallback icon
        }))
        setInventory(mapped)
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message)
        } else {
          setError('An unknown error occurred')
        }
      } finally {
        setLoading(false)
      }
    }
    fetchInventory()
  }, [])

  return (
    <div className="min-h-screen bg-main-bg py-8 px-4 sm:px-6 lg:px-8">
      <div className="bg-card-bg rounded-xl shadow-md overflow-hidden border border-card-border">
        <div className="p-6 border-b border-card-border bg-accent-light/5 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-primary-text">Trader Inventory</h2>
        </div>
        <div className="overflow-x-auto">
          {loading && (
            <div className="p-8 flex items-center justify-center">
              <Loader2 className="h-8 w-8 text-accent animate-spin" />
            </div>
          )}
          {error && (
            <div className="p-4 text-button-danger bg-button-danger/10 rounded-lg mx-4 my-2 flex items-center gap-2">
              <span className="text-sm">{error}</span>
            </div>
          )}
          <table className="w-full">
            <thead>
              <tr className="border-b border-card-border">
                <th className="py-4 px-6 text-left text-sm font-medium text-primary-text">Trader</th>
                <th className="py-4 px-6 text-left text-sm font-medium text-primary-text">Product</th>
                <th className="py-4 px-6 text-left text-sm font-medium text-primary-text">Category</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-primary-text">Total Batches</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-primary-text">In Storage</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-primary-text">Ready to Sell</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-primary-text">Sold</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-primary-text">Actions</th>
              </tr>
            </thead>
            <tbody>
              {inventory.map((item) => (
                <tr key={item.id} className="border-b border-card-border hover:bg-accent-light/5 transition-colors duration-200">
                  <td className="py-4 px-6">
                    <p className="text-sm text-primary-text">{item.trader.name}</p>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-accent-light/20 text-accent">{item.icon}</div>
                      <div>
                        <p className="font-medium text-primary-text">{item.crop_name}</p>
                        <p className="text-xs text-secondary-text">{item.description}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-accent-light/20 text-accent-dark">{item.category}</span>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-primary-text">{item.total_quantity_batches}</p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-primary-text">
                      {item.total_quantity_in_storage} {item.unit}
                    </p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-status-active-text">
                      {item.ready_to_sell_quantity} {item.unit}
                    </p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-status-completed-text">
                      {item.sold_quantity} {item.unit}
                    </p>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center justify-center gap-3">
                      <button
                        className={`px-3 py-2 rounded-lg flex items-center gap-2 font-medium text-sm transition-all duration-200 ${
                          item.ready_to_sell_quantity > 0
                            ? 'bg-button-bg text-button-text hover:bg-button-bg-hover'
                            : 'bg-card-bg text-secondary-text border border-card-border cursor-not-allowed'
                        }`}
                        onClick={() => item.ready_to_sell_quantity > 0 && handleBuyClick(item)}
                        disabled={item.ready_to_sell_quantity === 0}
                      >
                        <ShoppingCart className="w-4 h-4" />
                        <span>Buy</span>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Purchase Dialog */}
      <BuyDialog isDialogOpen={isDialogOpen} setIsDialogOpen={setIsDialogOpen} selectedItem={selectedItem} />
    </div>
  )
}

export default TraderInventory
