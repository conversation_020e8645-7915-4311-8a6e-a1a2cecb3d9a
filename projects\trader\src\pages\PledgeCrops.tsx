import { RenderCardView } from '@/components/pledgedCrops/renderCardView'
import { RenderListView } from '@/components/pledgedCrops/renderListView'

import apiClient from '@/services/apiClient'
import {
  BadgeCheck,
  BarChart3,
  ChevronDown,
  ChevronUp,
  DollarSign,
  Filter,
  LayoutGrid,
  List,
  MoveDownIcon,
  MoveUpIcon,
  Search,
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { Crop } from '../utils/types'

interface FilterOptions {
  status?: string
  grade?: string
  minValue?: number
  maxValue?: number
}

type ViewMode = 'card' | 'list'

export function PledgedCrops() {
  const [crops, setCrops] = useState<Crop[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<FilterOptions>({})
  const [showFilters, setShowFilters] = useState(false)
  const [sortBy, setSortBy] = useState<'name' | 'value' | 'date'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [viewMode, setViewMode] = useState<ViewMode>(() => {
    const saved = localStorage.getItem('cropViewMode')
    return saved === 'list' || saved === 'card' ? saved : 'card'
  })

  useEffect(() => {
    localStorage.setItem('cropViewMode', viewMode)
  }, [viewMode])

  useEffect(() => {
    apiClient
      .get('/crops/crops/')
      .then((res) => {
        console.log(res.data)
        const data: Crop[] = res.data

        const cropsWithDate = data.map((crop: Crop) => ({
          ...crop,
          pledgeDate: new Date(crop.created_at),
        }))
        setCrops(cropsWithDate)
      })
      .catch((err) => {
        console.error('Failed to fetch crops:', err)
      })
  }, [])

  const filteredCrops = crops
    .filter((crop) => {
      if (filters.grade && crop.crop_grade !== filters.grade) {
        return false
      }
      return true
    })
    .sort((a, b) => {
      const aDate = new Date(a.created_at)
      const bDate = new Date(b.created_at)
      return sortOrder === 'asc' ? aDate.getTime() - bDate.getTime() : bDate.getTime() - aDate.getTime()
    })

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className="min-h-screen bg-main-bg py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <h1 className="text-3xl font-bold text-primary-text">Pledged Crops</h1>

          <div className="flex items-center gap-4">
            <div className="flex items-center bg-card-bg rounded-lg p-1 shadow-md hover:shadow-lg transition-all">
              <button
                onClick={() => setViewMode('card')}
                className={`p-2.5 rounded ${
                  viewMode === 'card' ? 'text-button-text bg-button-bg hover:bg-button-bg-hover' : 'text-primary-text hover:bg-card-hover'
                } transition-all flex items-center gap-1.5`}
                title="Card View"
                aria-pressed={viewMode === 'card'}
              >
                <LayoutGrid className="h-5 w-5" />
                <span className="text-sm font-medium hidden sm:inline">Cards</span>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2.5 rounded ${
                  viewMode === 'list' ? 'text-button-text bg-button-bg hover:bg-button-bg-hover' : 'text-primary-text hover:bg-card-hover'
                } transition-all flex items-center gap-1.5`}
                title="List View"
                aria-pressed={viewMode === 'list'}
              >
                <List className="h-5 w-5" />
                <span className="text-sm font-medium hidden sm:inline">List</span>
              </button>
            </div>

            <div className="relative">
              <input
                type="text"
                placeholder="Search crops..."
                className="bg-card-bg text-primary-text pl-10 pr-4 py-2.5 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent border border-card-border hover:border-accent transition-all shadow-md w-64"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-accent" />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary-text hover:text-accent transition-colors"
                  aria-label="Clear search"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              )}
            </div>

            <div className="flex items-center bg-card-bg rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all">
              <div className="relative group">
                <div
                  className="flex items-center px-4 py-2.5 cursor-pointer"
                  onClick={() => {
                    const selectElement = document.getElementById('sort-select') as HTMLSelectElement
                    if (selectElement) {
                      selectElement.focus()
                      selectElement.click()
                    }
                  }}
                >
                  <span className="text-accent mr-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M3 6h18"></path>
                      <path d="M7 12h10"></path>
                      <path d="M11 18h6"></path>
                    </svg>
                  </span>
                  <select
                    id="sort-select"
                    className="bg-transparent text-primary-text font-medium focus:outline-none cursor-pointer hover:text-accent transition-colors appearance-none pr-8 w-full"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'name' | 'value' | 'date')}
                    style={{ WebkitAppearance: 'none', MozAppearance: 'none' }}
                  >
                    <option value="date" className="bg-card-bg text-primary-text py-3 px-2">
                      Sort by Date
                    </option>
                    <option value="name" className="bg-card-bg text-primary-text py-3 px-2">
                      Sort by Name
                    </option>
                    <option value="value" className="bg-card-bg text-primary-text py-3 px-2">
                      Sort by Value
                    </option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-accent pointer-events-none" />
                </div>
              </div>
              <div className="w-px h-8 bg-card-border self-center"></div>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-4 py-2.5 hover:bg-accent-light hover:text-accent-dark transition-all flex items-center gap-1.5 text-primary-text font-medium"
                title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
                aria-label={`Change sort order to ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
              >
                {sortOrder === 'asc' ? <MoveUpIcon className="h-5 w-8 text-accent" /> : <MoveDownIcon className="h-5 w-8 text-accent" />}
              </button>
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2.5 bg-card-bg text-primary-text rounded-lg hover:bg-accent-light hover:text-accent-dark transition-all shadow-md hover:shadow-lg font-medium"
              aria-expanded={showFilters}
              aria-controls="filter-panel"
            >
              <Filter className="h-5 w-5 text-accent" />
              <span>Filters</span>
              {showFilters ? <ChevronUp className="h-4 w-4 ml-1" /> : <ChevronDown className="h-4 w-4 ml-1" />}
            </button>
          </div>
        </div>

        {showFilters && (
          <div id="filter-panel" className="bg-card-bg p-6 rounded-lg mb-6 shadow-lg animate-fadeIn">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label className="text-sm font-medium text-primary-text mb-2 flex items-center">
                  <BadgeCheck className="h-4 w-4 mr-2 text-accent" />
                  Status
                </label>
                <div className="relative group">
                  <div
                    className="flex items-center w-full bg-card-bg border border-card-border rounded-lg px-4 py-2.5 text-primary-text transition-all hover:border-accent cursor-pointer"
                    onClick={() => {
                      const selectElement = document.getElementById('status-select') as HTMLSelectElement
                      if (selectElement) {
                        selectElement.focus()
                        selectElement.click()
                      }
                    }}
                  >
                    <span className="flex-grow truncate">
                      {filters.status ? filters.status.charAt(0).toUpperCase() + filters.status.slice(1) : 'All Statuses'}
                    </span>
                    <select
                      id="status-select"
                      className="absolute inset-0 opacity-0 cursor-pointer w-full"
                      value={filters.status || ''}
                      onChange={(e) => setFilters({ ...filters, status: e.target.value || undefined })}
                    >
                      <option value="">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="pending">Pending</option>
                      <option value="completed">Completed</option>
                      <option value="defaulted">Defaulted</option>
                    </select>
                    <ChevronDown className="h-4 w-4 text-accent ml-2 flex-shrink-0" />
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-primary-text mb-2 flex items-center">
                  <BarChart3 className="h-4 w-4 mr-2 text-accent" />
                  Quality Grade
                </label>
                <div className="relative group">
                  <div
                    className="flex items-center w-full bg-card-bg border border-card-border rounded-lg px-4 py-2.5 text-primary-text transition-all hover:border-accent cursor-pointer"
                    onClick={() => {
                      const selectElement = document.getElementById('grade-select') as HTMLSelectElement
                      if (selectElement) {
                        selectElement.focus()
                        selectElement.click()
                      }
                    }}
                  >
                    <span className="flex-grow truncate">{filters.grade ? `Grade ${filters.grade}` : 'All Grades'}</span>
                    <select
                      id="grade-select"
                      className="absolute inset-0 opacity-0 cursor-pointer w-full"
                      value={filters.grade || ''}
                      onChange={(e) => setFilters({ ...filters, grade: e.target.value || undefined })}
                    >
                      <option value="">All Grades</option>
                      <option value="A">Grade A</option>
                      <option value="B">Grade B</option>
                      <option value="C">Grade C</option>
                    </select>
                    <ChevronDown className="h-4 w-4 text-accent ml-2 flex-shrink-0" />
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-primary-text mb-2 flex items-center">
                  <DollarSign className="h-4 w-4 mr-2 text-accent" />
                  Min Value (USD)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    className="w-full bg-card-bg border border-card-border rounded-lg px-4 py-2.5 text-primary-text focus:ring-2 focus:ring-accent focus:border-transparent transition-all hover:border-accent"
                    value={filters.minValue || ''}
                    onChange={(e) => setFilters({ ...filters, minValue: parseFloat(e.target.value) || undefined })}
                    placeholder="Enter minimum value"
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-primary-text mb-2 flex items-center">
                  <DollarSign className="h-4 w-4 mr-2 text-accent" />
                  Max Value (USD)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    className="w-full bg-card-bg border border-card-border rounded-lg px-4 py-2.5 text-primary-text focus:ring-2 focus:ring-accent focus:border-transparent transition-all hover:border-accent"
                    value={filters.maxValue || ''}
                    onChange={(e) => setFilters({ ...filters, maxValue: parseFloat(e.target.value) || undefined })}
                    placeholder="Enter maximum value"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => setFilters({})}
                className="px-4 py-2 bg-button-danger text-button-text rounded-lg hover:bg-button-danger-hover transition-all font-medium flex items-center gap-2 shadow-md"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-accent"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
                Clear All Filters
              </button>
            </div>
          </div>
        )}

        {viewMode === 'list'
          ? RenderListView({
              filteredCrops,
              formatDate,
            })
          : RenderCardView({
              filteredCrops,
              formatDate,
            })}

        {filteredCrops.length === 0 && (
          <div className="text-center py-12">
            <p className="text-secondary-text text-lg">No crops found matching your criteria</p>
          </div>
        )}
      </div>
    </div>
  )
}
