# flake8: noqa
# fmt: off
# mypy: ignore-errors
# This file was automatically generated by algokit-client-generator.
# DO NOT MODIFY IT BY HAND.
# requires: algokit-utils@^3.0.0

# common
import dataclasses
import typing
# core algosdk
import algosdk
from algosdk.transaction import OnComplete
from algosdk.atomic_transaction_composer import TransactionSigner
from algosdk.source_map import SourceMap
from algosdk.transaction import Transaction
from algosdk.v2client.models import SimulateTraceConfig
# utils
import algokit_utils
from algokit_utils import AlgorandClient as _AlgoKitAlgorandClient

_APP_SPEC_JSON = r"""{"arcs": [22, 28], "bareActions": {"call": [], "create": ["NoOp"]}, "methods": [{"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)", "desc": "Detailed information about the crop to register.", "name": "info", "struct": "ProductInfo"}], "name": "register_crop", "returns": {"type": "uint64", "desc": "The asset ID of the newly created ASA representing the crop."}, "desc": "Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.\nCreates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.", "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "product_id"}, {"type": "account", "name": "framer_address"}], "name": "opt_in_asa", "returns": {"type": "void"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "product_id"}, {"type": "account", "name": "framer_address"}, {"type": "uint64", "name": "quantity"}], "name": "transfer_asa", "returns": {"type": "void"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "product_id"}], "name": "get_product_info", "returns": {"type": "(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)", "struct": "ProductInfo"}, "events": [], "readonly": true, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [], "name": "get_product_count", "returns": {"type": "uint64"}, "desc": "Returns the total number of registered products in the supply chain.", "events": [], "readonly": true, "recommendations": {}}, {"actions": {"call": ["DeleteApplication"], "create": []}, "args": [], "name": "delete_application", "returns": {"type": "void"}, "desc": "Deletes the application if the transaction sender is the creator.\nThe application can only be deleted by its creator.", "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["UpdateApplication"], "create": []}, "args": [], "name": "update_application", "returns": {"type": "void"}, "desc": "Allows the application to be updated only by its creator.", "events": [], "readonly": false, "recommendations": {}}], "name": "SupplyChain", "state": {"keys": {"box": {}, "global": {"product_count": {"key": "cHJvZHVjdF9jb3VudA==", "keyType": "AVMString", "valueType": "AVMUint64"}}, "local": {}}, "maps": {"box": {}, "global": {}, "local": {}}, "schema": {"global": {"bytes": 0, "ints": 1}, "local": {"bytes": 0, "ints": 0}}}, "structs": {"Coordinates": [{"name": "lat", "type": "string"}, {"name": "long", "type": "string"}], "GrowthPeriod": [{"name": "startDate", "type": "string"}, {"name": "harvestDate", "type": "string"}], "Location": [{"name": "address", "type": "string"}, {"name": "coordinates", "type": "Coordinates"}], "ProductInfo": [{"name": "farmer_address", "type": "address"}, {"name": "cost", "type": "uint64"}, {"name": "crop_greade", "type": "string"}, {"name": "quantity", "type": "uint64"}, {"name": "location", "type": "Location"}, {"name": "growth_period", "type": "GrowthPeriod"}, {"name": "soil_type", "type": "string"}, {"name": "irrigation_type", "type": "string"}, {"name": "fertilizers_used", "type": "string[]"}, {"name": "certification", "type": "string"}]}, "byteCode": {"approval": "CiADAQAEJgINcHJvZHVjdF9jb3VudAQVH3x1MRhAAAMoI2cxG0EAvYIHBIE1IPUEUSV6RwQMTMUUBL9s32QESgwWlAQzs0meBLU+JZM2GgCOBwBxAFsAQgAuAB0ADwACI0MxGSQSRDEYRIgBDSJDMRmBBRJEMRhEiAD4IkMxGRREMRhEiADnFilMULAiQzEZFEQxGEQ2GgEXiADJKUxQsCJDMRkURDEYRDYaARc2GgIXwBw2GgOIAJIiQzEZFEQxGEQ2GgEXNhoCF8AciABkIkMxGRREMRhENhoBiAASFilMULAiQzEZQP92MRgURCJDigEBsTIAi/+BKlsjKGVEFjIKRwOyLLIrsiqyKbImgARDUk9QsiUjsiOyIoEDshCyAbO0PEkWi/+/IyhlRCIIKExniYoCALEyACOyEov+shGL/7IUJLIQsgGziYoDALEyAIv/F7ISi/2yEYv+shQkshCyAbOJigEBi/8WvkiJIyhlRIkxADIJEkSJMQAyCRJEiQ==", "clear": "CoEBQw=="}, "compilerInfo": {"compiler": "puya", "compilerVersion": {"major": 4, "minor": 7, "patch": 0}}, "events": [], "networks": {}, "source": {"approval": "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", "clear": "I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"}, "sourceInfo": {"approval": {"pcOffsetMethod": "none", "sourceInfo": [{"pc": [116], "errorMessage": "OnCompletion is not DeleteApplication"}, {"pc": [128, 145, 165, 190, 212], "errorMessage": "OnCompletion is not NoOp"}, {"pc": [102], "errorMessage": "OnCompletion is not UpdateApplication"}, {"pc": [237], "errorMessage": "can only call when creating"}, {"pc": [105, 119, 131, 148, 168, 193, 215], "errorMessage": "can only call when not creating"}, {"pc": [254, 300, 369], "errorMessage": "check self.product_count exists"}]}, "clear": {"pcOffsetMethod": "none", "sourceInfo": []}}, "templateVariables": {}}"""
APP_SPEC = algokit_utils.Arc56Contract.from_json(_APP_SPEC_JSON)

def _parse_abi_args(args: object | None = None) -> list[object] | None:
    """Helper to parse ABI args into the format expected by underlying client"""
    if args is None:
        return None

    def convert_dataclass(value: object) -> object:
        if dataclasses.is_dataclass(value):
            return tuple(convert_dataclass(getattr(value, field.name)) for field in dataclasses.fields(value))
        elif isinstance(value, (list, tuple)):
            return type(value)(convert_dataclass(item) for item in value)
        return value

    match args:
        case tuple():
            method_args = list(args)
        case _ if dataclasses.is_dataclass(args):
            method_args = [getattr(args, field.name) for field in dataclasses.fields(args)]
        case _:
            raise ValueError("Invalid 'args' type. Expected 'tuple' or 'TypedDict' for respective typed arguments.")

    return [
        convert_dataclass(arg) if not isinstance(arg, algokit_utils.AppMethodCallTransactionArgument) else arg
        for arg in method_args
    ] if method_args else None

def _init_dataclass(cls: type, data: dict) -> object:
    """
    Recursively instantiate a dataclass of type `cls` from `data`.

    For each field on the dataclass, if the field type is also a dataclass
    and the corresponding data is a dict, instantiate that field recursively.
    """
    field_values = {}
    for field in dataclasses.fields(cls):
        field_value = data.get(field.name)
        # Check if the field expects another dataclass and the value is a dict.
        if dataclasses.is_dataclass(field.type) and isinstance(field_value, dict):
            field_values[field.name] = _init_dataclass(typing.cast(type, field.type), field_value)
        else:
            field_values[field.name] = field_value
    return cls(**field_values)

@dataclasses.dataclass(frozen=True)
class Coordinates:
    """Struct for Coordinates"""
    lat: str
    long: str

@dataclasses.dataclass(frozen=True)
class GrowthPeriod:
    """Struct for GrowthPeriod"""
    startDate: str
    harvestDate: str

@dataclasses.dataclass(frozen=True)
class Location:
    """Struct for Location"""
    address: str
    coordinates: Coordinates

@dataclasses.dataclass(frozen=True)
class ProductInfo:
    """Struct for ProductInfo"""
    farmer_address: str
    cost: int
    crop_greade: str
    quantity: int
    location: Location
    growth_period: GrowthPeriod
    soil_type: str
    irrigation_type: str
    fertilizers_used: list[str]
    certification: str


@dataclasses.dataclass(frozen=True, kw_only=True)
class RegisterCropArgs:
    """Dataclass for register_crop arguments"""
    info: ProductInfo

    @property
    def abi_method_signature(self) -> str:
        return "register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64"

@dataclasses.dataclass(frozen=True, kw_only=True)
class OptInAsaArgs:
    """Dataclass for opt_in_asa arguments"""
    product_id: int
    framer_address: str | bytes

    @property
    def abi_method_signature(self) -> str:
        return "opt_in_asa(uint64,account)void"

@dataclasses.dataclass(frozen=True, kw_only=True)
class TransferAsaArgs:
    """Dataclass for transfer_asa arguments"""
    product_id: int
    framer_address: str | bytes
    quantity: int

    @property
    def abi_method_signature(self) -> str:
        return "transfer_asa(uint64,account,uint64)void"

@dataclasses.dataclass(frozen=True, kw_only=True)
class GetProductInfoArgs:
    """Dataclass for get_product_info arguments"""
    product_id: int

    @property
    def abi_method_signature(self) -> str:
        return "get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)"


class _SupplyChainUpdate:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def update_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppUpdateMethodCallParams:
    
        params = params or algokit_utils.CommonAppCallParams()
        compilation_params = compilation_params or algokit_utils.AppClientCompilationParams()
        return self.app_client.params.update(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "update_application()void",
        }))


class _SupplyChainDelete:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def delete_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppDeleteMethodCallParams:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.delete(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "delete_application()void",
        }))


class SupplyChainParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    @property
    def update(self) -> "_SupplyChainUpdate":
        return _SupplyChainUpdate(self.app_client)

    @property
    def delete(self) -> "_SupplyChainDelete":
        return _SupplyChainDelete(self.app_client)

    def register_crop(
        self,
        args: tuple[ProductInfo] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64",
            "args": method_args,
        }))

    def opt_in_asa(
        self,
        args: tuple[int, str | bytes] | OptInAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "opt_in_asa(uint64,account)void",
            "args": method_args,
        }))

    def transfer_asa(
        self,
        args: tuple[int, str | bytes, int] | TransferAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "transfer_asa(uint64,account,uint64)void",
            "args": method_args,
        }))

    def get_product_info(
        self,
        args: tuple[int] | GetProductInfoArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)",
            "args": method_args,
        }))

    def get_product_count(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_product_count()uint64",
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> algokit_utils.AppCallParams:
        return self.app_client.params.bare.clear_state(
            params,
            
        )


class _SupplyChainUpdateTransaction:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def update_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.BuiltTransactions:
    
        params = params or algokit_utils.CommonAppCallParams()
        compilation_params = compilation_params or algokit_utils.AppClientCompilationParams()
        return self.app_client.create_transaction.update(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "update_application()void",
        }))


class _SupplyChainDeleteTransaction:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def delete_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.delete(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "delete_application()void",
        }))


class SupplyChainCreateTransactionParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    @property
    def update(self) -> "_SupplyChainUpdateTransaction":
        return _SupplyChainUpdateTransaction(self.app_client)

    @property
    def delete(self) -> "_SupplyChainDeleteTransaction":
        return _SupplyChainDeleteTransaction(self.app_client)

    def register_crop(
        self,
        args: tuple[ProductInfo] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64",
            "args": method_args,
        }))

    def opt_in_asa(
        self,
        args: tuple[int, str | bytes] | OptInAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "opt_in_asa(uint64,account)void",
            "args": method_args,
        }))

    def transfer_asa(
        self,
        args: tuple[int, str | bytes, int] | TransferAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "transfer_asa(uint64,account,uint64)void",
            "args": method_args,
        }))

    def get_product_info(
        self,
        args: tuple[int] | GetProductInfoArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)",
            "args": method_args,
        }))

    def get_product_count(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_product_count()uint64",
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> Transaction:
        return self.app_client.create_transaction.bare.clear_state(
            params,
            
        )


class _SupplyChainUpdateSend:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def update_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
    
        params = params or algokit_utils.CommonAppCallParams()
        compilation_params = compilation_params or algokit_utils.AppClientCompilationParams()
        response = self.app_client.send.update(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "update_application()void",
        }), send_params=send_params, compilation_params=compilation_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppUpdateTransactionResult[None], parsed_response)


class _SupplyChainDeleteSend:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def delete_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
    
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.delete(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "delete_application()void",
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[None], parsed_response)


class SupplyChainSend:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    @property
    def update(self) -> "_SupplyChainUpdateSend":
        return _SupplyChainUpdateSend(self.app_client)

    @property
    def delete(self) -> "_SupplyChainDeleteSend":
        return _SupplyChainDeleteSend(self.app_client)

    def register_crop(
        self,
        args: tuple[ProductInfo] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[int]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[int], parsed_response)

    def opt_in_asa(
        self,
        args: tuple[int, str | bytes] | OptInAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "opt_in_asa(uint64,account)void",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[None], parsed_response)

    def transfer_asa(
        self,
        args: tuple[int, str | bytes, int] | TransferAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[None]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "transfer_asa(uint64,account,uint64)void",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[None], parsed_response)

    def get_product_info(
        self,
        args: tuple[int] | GetProductInfoArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[ProductInfo]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = dataclasses.replace(response, abi_return=_init_dataclass(ProductInfo, typing.cast(dict, response.abi_return))) # type: ignore
        return typing.cast(algokit_utils.SendAppTransactionResult[ProductInfo], parsed_response)

    def get_product_count(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[int]:
    
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_product_count()uint64",
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[int], parsed_response)

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[algokit_utils.ABIReturn]:
        return self.app_client.send.bare.clear_state(
            params,
            send_params=send_params,
        )


class GlobalStateValue(typing.TypedDict):
    """Shape of global_state state key values"""
    product_count: int

class SupplyChainState:
    """Methods to access state for the current SupplyChain app"""

    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    @property
    def global_state(
        self
    ) -> "_GlobalState":
            """Methods to access global_state for the current app"""
            return _GlobalState(self.app_client)

class _GlobalState:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client
        
        # Pre-generated mapping of value types to their struct classes
        self._struct_classes: dict[str, typing.Type[typing.Any]] = {}

    def get_all(self) -> GlobalStateValue:
        """Get all current keyed values from global_state state"""
        result = self.app_client.state.global_state.get_all()
        if not result:
            return typing.cast(GlobalStateValue, {})

        converted = {}
        for key, value in result.items():
            key_info = self.app_client.app_spec.state.keys.global_state.get(key)
            struct_class = self._struct_classes.get(key_info.value_type) if key_info else None
            converted[key] = (
                _init_dataclass(struct_class, value) if struct_class and isinstance(value, dict)
                else value
            )
        return typing.cast(GlobalStateValue, converted)

    @property
    def product_count(self) -> int:
        """Get the current value of the product_count key in global_state state"""
        value = self.app_client.state.global_state.get_value("product_count")
        if isinstance(value, dict) and "AVMUint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["AVMUint64"], value)  # type: ignore
        return typing.cast(int, value)

class SupplyChainClient:
    """Client for interacting with SupplyChain smart contract"""

    @typing.overload
    def __init__(self, app_client: algokit_utils.AppClient) -> None: ...
    
    @typing.overload
    def __init__(
        self,
        *,
        algorand: _AlgoKitAlgorandClient,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None: ...

    def __init__(
        self,
        app_client: algokit_utils.AppClient | None = None,
        *,
        algorand: _AlgoKitAlgorandClient | None = None,
        app_id: int | None = None,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None:
        if app_client:
            self.app_client = app_client
        elif algorand and app_id:
            self.app_client = algokit_utils.AppClient(
                algokit_utils.AppClientParams(
                    algorand=algorand,
                    app_spec=APP_SPEC,
                    app_id=app_id,
                    app_name=app_name,
                    default_sender=default_sender,
                    default_signer=default_signer,
                    approval_source_map=approval_source_map,
                    clear_source_map=clear_source_map,
                )
            )
        else:
            raise ValueError("Either app_client or algorand and app_id must be provided")
    
        self.params = SupplyChainParams(self.app_client)
        self.create_transaction = SupplyChainCreateTransactionParams(self.app_client)
        self.send = SupplyChainSend(self.app_client)
        self.state = SupplyChainState(self.app_client)

    @staticmethod
    def from_creator_and_name(
        creator_address: str,
        app_name: str,
        algorand: _AlgoKitAlgorandClient,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
    ) -> "SupplyChainClient":
        return SupplyChainClient(
            algokit_utils.AppClient.from_creator_and_name(
                creator_address=creator_address,
                app_name=app_name,
                app_spec=APP_SPEC,
                algorand=algorand,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
                ignore_cache=ignore_cache,
                app_lookup_cache=app_lookup_cache,
            )
        )
    
    @staticmethod
    def from_network(
        algorand: _AlgoKitAlgorandClient,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "SupplyChainClient":
        return SupplyChainClient(
            algokit_utils.AppClient.from_network(
                app_spec=APP_SPEC,
                algorand=algorand,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    @property
    def app_id(self) -> int:
        return self.app_client.app_id
    
    @property
    def app_address(self) -> str:
        return self.app_client.app_address
    
    @property
    def app_name(self) -> str:
        return self.app_client.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_client.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_client.algorand

    def clone(
        self,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "SupplyChainClient":
        return SupplyChainClient(
            self.app_client.clone(
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    def new_group(self) -> "SupplyChainComposer":
        return SupplyChainComposer(self)

    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64"],
        return_value: algokit_utils.ABIReturn | None
    ) -> int | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["opt_in_asa(uint64,account)void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["transfer_asa(uint64,account,uint64)void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)"],
        return_value: algokit_utils.ABIReturn | None
    ) -> ProductInfo | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["get_product_count()uint64"],
        return_value: algokit_utils.ABIReturn | None
    ) -> int | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["update_application()void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["delete_application()void"],
        return_value: algokit_utils.ABIReturn | None
    ) -> None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None: ...

    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None | ProductInfo | int:
        """Decode ABI return value for the given method."""
        if return_value is None:
            return None
    
        arc56_method = self.app_spec.get_arc56_method(method)
        decoded = return_value.get_arc56_value(arc56_method, self.app_spec.structs)
    
        # If method returns a struct, convert the dict to appropriate dataclass
        if (arc56_method and
            arc56_method.returns and
            arc56_method.returns.struct and
            isinstance(decoded, dict)):
            struct_class = globals().get(arc56_method.returns.struct)
            if struct_class:
                return struct_class(**typing.cast(dict, decoded))
        return decoded


@dataclasses.dataclass(frozen=True)
class SupplyChainBareCallCreateParams(algokit_utils.AppClientBareCallCreateParams):
    """Parameters for creating SupplyChain contract with bare calls"""
    on_complete: typing.Literal[OnComplete.NoOpOC] | None = None

    def to_algokit_utils_params(self) -> algokit_utils.AppClientBareCallCreateParams:
        return algokit_utils.AppClientBareCallCreateParams(**self.__dict__)

@dataclasses.dataclass(frozen=True)
class SupplyChainMethodCallUpdateParams(
    algokit_utils.BaseAppClientMethodCallParams[
        typing.Any,
        str | None,
    ]
):
    """Parameters for calling SupplyChain contract using ABI"""
    on_complete: typing.Literal[OnComplete.UpdateApplicationOC] | None = None
    method: str | None = None

    def to_algokit_utils_params(self) -> algokit_utils.AppClientMethodCallParams:
        method_args = _parse_abi_args(self.args)
        return algokit_utils.AppClientMethodCallParams(
            **{
                **self.__dict__,
                "method": self.method or getattr(self.args, "abi_method_signature", None),
                "args": method_args,
            }
        )

@dataclasses.dataclass(frozen=True)
class SupplyChainMethodCallDeleteParams(
    algokit_utils.BaseAppClientMethodCallParams[
        typing.Any,
        str | None,
    ]
):
    """Parameters for calling SupplyChain contract using ABI"""
    on_complete: typing.Literal[OnComplete.DeleteApplicationOC] | None = None
    method: str | None = None

    def to_algokit_utils_params(self) -> algokit_utils.AppClientMethodCallParams:
        method_args = _parse_abi_args(self.args)
        return algokit_utils.AppClientMethodCallParams(
            **{
                **self.__dict__,
                "method": self.method or getattr(self.args, "abi_method_signature", None),
                "args": method_args,
            }
        )

class SupplyChainFactory(algokit_utils.TypedAppFactoryProtocol[SupplyChainBareCallCreateParams, SupplyChainMethodCallUpdateParams, SupplyChainMethodCallDeleteParams]):
    """Factory for deploying and managing SupplyChainClient smart contracts"""

    def __init__(
        self,
        algorand: _AlgoKitAlgorandClient,
        *,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        version: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ):
        self.app_factory = algokit_utils.AppFactory(
            params=algokit_utils.AppFactoryParams(
                algorand=algorand,
                app_spec=APP_SPEC,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                version=version,
                compilation_params=compilation_params,
            )
        )
        self.params = SupplyChainFactoryParams(self.app_factory)
        self.create_transaction = SupplyChainFactoryCreateTransaction(self.app_factory)
        self.send = SupplyChainFactorySend(self.app_factory)

    @property
    def app_name(self) -> str:
        return self.app_factory.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_factory.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_factory.algorand

    def deploy(
        self,
        *,
        on_update: algokit_utils.OnUpdate | None = None,
        on_schema_break: algokit_utils.OnSchemaBreak | None = None,
        create_params: SupplyChainBareCallCreateParams | None = None,
        update_params: SupplyChainMethodCallUpdateParams | None = None,
        delete_params: SupplyChainMethodCallDeleteParams | None = None,
        existing_deployments: algokit_utils.ApplicationLookup | None = None,
        ignore_cache: bool = False,
        app_name: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
    ) -> tuple[SupplyChainClient, algokit_utils.AppFactoryDeployResult]:
        """Deploy the application"""
        deploy_response = self.app_factory.deploy(
            on_update=on_update,
            on_schema_break=on_schema_break,
            create_params=create_params.to_algokit_utils_params() if create_params else None,
            update_params=update_params.to_algokit_utils_params() if update_params else None,
            delete_params=delete_params.to_algokit_utils_params() if delete_params else None,
            existing_deployments=existing_deployments,
            ignore_cache=ignore_cache,
            app_name=app_name,
            compilation_params=compilation_params,
            send_params=send_params,
        )

        return SupplyChainClient(deploy_response[0]), deploy_response[1]

    def get_app_client_by_creator_and_name(
        self,
        creator_address: str,
        app_name: str,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> SupplyChainClient:
        """Get an app client by creator address and name"""
        return SupplyChainClient(
            self.app_factory.get_app_client_by_creator_and_name(
                creator_address,
                app_name,
                default_sender,
                default_signer,
                ignore_cache,
                app_lookup_cache,
                approval_source_map,
                clear_source_map,
            )
        )

    def get_app_client_by_id(
        self,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> SupplyChainClient:
        """Get an app client by app ID"""
        return SupplyChainClient(
            self.app_factory.get_app_client_by_id(
                app_id,
                app_name,
                default_sender,
                default_signer,
                approval_source_map,
                clear_source_map,
            )
        )


class SupplyChainFactoryParams:
    """Parameters for creating transactions for SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = SupplyChainFactoryCreateParams(app_factory)
        self.update = SupplyChainFactoryUpdateParams(app_factory)
        self.delete = SupplyChainFactoryDeleteParams(app_factory)

class SupplyChainFactoryCreateParams:
    """Parameters for 'create' operations of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateParams:
        """Creates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            compilation_params=compilation_params)

    def register_crop(
        self,
        args: tuple[ProductInfo] | RegisterCropArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64 ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def opt_in_asa(
        self,
        args: tuple[int, str | bytes] | OptInAsaArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the opt_in_asa(uint64,account)void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "opt_in_asa(uint64,account)void",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def transfer_asa(
        self,
        args: tuple[int, str | bytes, int] | TransferAsaArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the transfer_asa(uint64,account,uint64)void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "transfer_asa(uint64,account,uint64)void",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def get_product_info(
        self,
        args: tuple[int] | GetProductInfoArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string) ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def get_product_count(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the get_product_count()uint64 ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "get_product_count()uint64",
                "args": None,
                }
            ),
            compilation_params=compilation_params
        )

    def update_application(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the update_application()void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "update_application()void",
                "args": None,
                }
            ),
            compilation_params=compilation_params
        )

    def delete_application(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the delete_application()void ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "delete_application()void",
                "args": None,
                }
            ),
            compilation_params=compilation_params
        )

class SupplyChainFactoryUpdateParams:
    """Parameters for 'update' operations of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppUpdateParams:
        """Updates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_update(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )

class SupplyChainFactoryDeleteParams:
    """Parameters for 'delete' operations of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppDeleteParams:
        """Deletes an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_delete(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )


class SupplyChainFactoryCreateTransaction:
    """Create transactions for SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = SupplyChainFactoryCreateTransactionCreate(app_factory)


class SupplyChainFactoryCreateTransactionCreate:
    """Create new instances of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
    ) -> Transaction:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.create_transaction.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
        )


class SupplyChainFactorySend:
    """Send calls to SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = SupplyChainFactorySendCreate(app_factory)


class SupplyChainFactorySendCreate:
    """Send create calls to SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ) -> tuple[SupplyChainClient, algokit_utils.SendAppCreateTransactionResult]:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        result = self.app_factory.send.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            send_params=send_params,
            compilation_params=compilation_params
        )
        return SupplyChainClient(result[0]), result[1]


class _SupplyChainUpdateComposer:
    def __init__(self, composer: "SupplyChainComposer"):
        self.composer = composer
    def update_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> "SupplyChainComposer":
        self.composer._composer.add_app_update_method_call(
            self.composer.client.params.update.update_application(
                
                params=params,
                compilation_params=compilation_params
            )
        )
        self.composer._result_mappers.append(
            lambda v: self.composer.client.decode_return_value(
                "update_application()void", v
            )
        )
        return self.composer


class _SupplyChainDeleteComposer:
    def __init__(self, composer: "SupplyChainComposer"):
        self.composer = composer
    def delete_application(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "SupplyChainComposer":
        self.composer._composer.add_app_delete_method_call(
            self.composer.client.params.delete.delete_application(
                
                params=params,
                
            )
        )
        self.composer._result_mappers.append(
            lambda v: self.composer.client.decode_return_value(
                "delete_application()void", v
            )
        )
        return self.composer


class SupplyChainComposer:
    """Composer for creating transaction groups for SupplyChain contract calls"""

    def __init__(self, client: "SupplyChainClient"):
        self.client = client
        self._composer = client.algorand.new_group()
        self._result_mappers: list[typing.Callable[[algokit_utils.ABIReturn | None], object] | None] = []

    @property
    def update(self) -> "_SupplyChainUpdateComposer":
        return _SupplyChainUpdateComposer(self)

    @property
    def delete(self) -> "_SupplyChainDeleteComposer":
        return _SupplyChainDeleteComposer(self)

    def register_crop(
        self,
        args: tuple[ProductInfo] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_app_call_method_call(
            self.client.params.register_crop(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64", v
            )
        )
        return self

    def opt_in_asa(
        self,
        args: tuple[int, str | bytes] | OptInAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_app_call_method_call(
            self.client.params.opt_in_asa(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "opt_in_asa(uint64,account)void", v
            )
        )
        return self

    def transfer_asa(
        self,
        args: tuple[int, str | bytes, int] | TransferAsaArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_app_call_method_call(
            self.client.params.transfer_asa(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "transfer_asa(uint64,account,uint64)void", v
            )
        )
        return self

    def get_product_info(
        self,
        args: tuple[int] | GetProductInfoArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_app_call_method_call(
            self.client.params.get_product_info(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)", v
            )
        )
        return self

    def get_product_count(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_app_call_method_call(
            self.client.params.get_product_count(
                
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "get_product_count()uint64", v
            )
        )
        return self

    def clear_state(
        self,
        *,
        args: list[bytes] | None = None,
        params: algokit_utils.CommonAppCallParams | None = None,
    ) -> "SupplyChainComposer":
        params=params or algokit_utils.CommonAppCallParams()
        self._composer.add_app_call(
            self.client.params.clear_state(
                algokit_utils.AppClientBareCallParams(
                    **{
                        **dataclasses.asdict(params),
                        "args": args
                    }
                )
            )
        )
        return self
    
    def add_transaction(
        self, txn: Transaction, signer: TransactionSigner | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_transaction(txn, signer)
        return self
    
    def composer(self) -> algokit_utils.TransactionComposer:
        return self._composer
    
    def simulate(
        self,
        allow_more_logs: bool | None = None,
        allow_empty_signatures: bool | None = None,
        allow_unnamed_resources: bool | None = None,
        extra_opcode_budget: int | None = None,
        exec_trace_config: SimulateTraceConfig | None = None,
        simulation_round: int | None = None,
        skip_signatures: bool | None = None,
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.simulate(
            allow_more_logs=allow_more_logs,
            allow_empty_signatures=allow_empty_signatures,
            allow_unnamed_resources=allow_unnamed_resources,
            extra_opcode_budget=extra_opcode_budget,
            exec_trace_config=exec_trace_config,
            simulation_round=simulation_round,
            skip_signatures=skip_signatures,
        )
    
    def send(
        self,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.send(send_params)
