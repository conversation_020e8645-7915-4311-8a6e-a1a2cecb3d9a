import { motion } from 'framer-motion';
import { ChevronRight, Wheat } from 'lucide-react';
import { Transaction } from '../../stores/transactionStore';
import MilestoneProgress from './MilestoneProgress';

interface TransactionCardProps {
  transaction: Transaction;
  onClick: () => void;
}

const TransactionCard = ({ transaction, onClick }: TransactionCardProps) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-primary-100 text-primary-700">
            Active
          </span>
        );
      case 'completed':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-success-100 text-success-700">
            Completed
          </span>
        );
      case 'pending':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-secondary-100 text-secondary-700">
            Pending
          </span>
        );
      case 'disputed':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-error-100 text-error-700">
            Disputed
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-neutral-100 text-neutral-700">
            {status}
          </span>
        );
    }
  };
  
  // Calculate progress percentage
  const completedMilestones = transaction.milestones.filter(
    (m) => ['completed', 'verified', 'released'].includes(m.status)
  ).length;
  const progressPercentage = Math.round(
    (completedMilestones / transaction.milestones.length) * 100
  );
  
  return (
    <motion.div
      whileHover={{ y: -4 }}
      className="bg-white rounded-xl shadow-sm overflow-hidden cursor-pointer"
      onClick={onClick}
    >
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-lg bg-primary-50 text-primary-600">
              <Wheat className="w-5 h-5" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-neutral-900">
                {transaction.productName}
              </h3>
              <p className="text-sm text-neutral-500">
                {transaction.quantity} units
              </p>
            </div>
          </div>
          {getStatusBadge(transaction.status)}
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm font-medium text-neutral-500">Total Amount</p>
            <p className="text-lg font-semibold text-neutral-900">
              ${transaction.totalAmount.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-neutral-500">Released</p>
            <p className="text-lg font-semibold text-neutral-900">
              ${transaction.releaseAmount.toLocaleString()}
            </p>
          </div>
        </div>
        
        <MilestoneProgress milestones={transaction.milestones} />
        
        <div className="flex justify-between items-center mt-4 pt-4 border-t border-neutral-200">
          <div>
            <p className="text-sm font-medium text-neutral-900">Progress</p>
            <p className="text-xs text-neutral-500">
              {completedMilestones} of {transaction.milestones.length} milestones
            </p>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-primary-700">
              {progressPercentage}%
            </span>
            <ChevronRight className="w-5 h-5 text-neutral-400" />
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TransactionCard;