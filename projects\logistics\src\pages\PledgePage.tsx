import { VITE_ALGORAND_DECIMAL } from '@/utils/variable'
import { useWallet } from '@txnlab/use-wallet-react'
import { BrowserQRCodeReader } from '@zxing/browser'
import clsx from 'clsx'
import { ArrowLeft, Calendar, Copy, Loader2, MapPin, QrCode } from 'lucide-react'
import React, { useState } from 'react'
import toast from 'react-hot-toast'
import { useSupplyChainClient } from '../utils/algo'

interface PledgeFormData {
  grade: string
  customCrop: string
  quantity: number
  address: string
  price: number
  unit: 'kg' | 'tons'
  location: {
    address: string
    coordinates: {
      lat: string
      lng: string
    }
  }
  growthPeriod: {
    startDate: string
    harvestDate: string
  }
  soilType: string
  fertilizers: string[]
  irrigation: string
  certifications: string
  termsAccepted: boolean
}
interface TransferFormData {
  amount: string
  address: string
}

const GRADES = ['Grade A', 'Grade B', 'Grade C']
const SOIL_TYPES = ['Clay', 'Sandy', 'Silty', 'Peaty', 'Chalky', 'Loamy']
const FERTILIZER_OPTIONS = ['Nitrogen', 'Phosphorus', 'Potassium', 'Organic', 'Biofertilizer']
const IRRIGATION_METHODS = ['Drip', 'Sprinkler', 'Flood', 'Center Pivot', 'Manual']

/**
 * Renders the crop pledge form page, allowing users to submit detailed crop, farmer, and growth information for blockchain registration.
 *
 * The form supports QR code scanning for farmer address input, real-time validation, total price calculation, and displays estimated rewards based on the pledged quantity. Upon submission, the crop data is registered on the blockchain, and user feedback is provided for success or failure.
 *
 * @returns The pledge form page React component.
 */
export function PledgePage() {
  const [isLoading, setIsLoading] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [showError, setShowError] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [errors, setErrors] = useState<Partial<TransferFormData>>({})
  const { activeAddress } = useWallet()
  const supplyChainClient = useSupplyChainClient()
  const [formData, setFormData] = useState<PledgeFormData>({
    grade: '',
    customCrop: '',
    address: '',
    price: 0,
    quantity: 0,
    unit: 'kg',
    location: {
      address: '',
      coordinates: { lat: '', lng: '' },
    },
    growthPeriod: {
      startDate: '',
      harvestDate: '',
    },
    soilType: '',
    fertilizers: [],
    irrigation: '',
    certifications: '',
    termsAccepted: false,
  })

  const startQRScanner = async () => {
    setIsScanning(true)
    const codeReader = new BrowserQRCodeReader()

    try {
      const videoInputDevices = await BrowserQRCodeReader.listVideoInputDevices()
      if (videoInputDevices.length > 0) {
        const result = await codeReader.decodeOnceFromVideoDevice(undefined, 'qr-video')
        if (result) {
          setFormData((prev) => ({ ...prev, address: result.getText() }))
        }
      }
    } catch (error) {
      toast.error('Failed to access camera')
    } finally {
      setIsScanning(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Add address validation
    if (!formData.address) {
      setErrors({ address: 'Address is required' })
      setIsLoading(false)
      return
    }
    setErrors({})

    try {
      const productInfo = {
        farmerAddress: activeAddress as string,
        cost: BigInt((formData.price * VITE_ALGORAND_DECIMAL).toFixed(0)),
        cropGreade: formData.grade,
        quantity: BigInt((formData.quantity * (formData.unit === 'tons' ? 6 : 3)).toFixed(0)),
        location: {
          address: formData.location.address,
          coordinates: {
            lat: formData.location.coordinates.lat,
            long: formData.location.coordinates.lng,
          },
        },
        growthPeriod: {
          startDate: formData.growthPeriod.startDate,
          harvestDate: formData.growthPeriod.harvestDate,
        },
        soilType: formData.soilType,
        irrigationType: formData.irrigation,
        fertilizersUsed: formData.fertilizers,
        certification: formData.certifications,
      }

      console.log('Registering crop with info:', productInfo)

      const result = await supplyChainClient.send.registerCrop({
        args: [productInfo],
        note: 'Registering new crop',
      })
      console.log('Registration result:', result)
      setShowSuccess(true)
      setShowError(false)

      // Show success message
      toast.success('Crop registered successfully!')
    } catch (error) {
      console.error('Error registering crop:', error)
      setShowError(true)
      setShowSuccess(false)
      toast.error('Failed to register crop: ' + (error instanceof Error ? error.message : String(error)))
    } finally {
      setIsLoading(false)
    }
  }

  const calculateRewards = () => {
    // Mock reward calculation based on quantity and growth period
    const baseRate = 10 // tokens per ton
    const quantity = formData.quantity * (formData.unit === 'tons' ? 1 : 0.001)
    return Math.round(quantity * baseRate)
  }

  return (
    <div className="min-h-screen bg-[#212121] py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button onClick={() => window.history.back()} className="mr-4 text-[#BDBDBD] hover:text-white transition-colors">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-3xl font-bold text-white">Pledge Your Crops</h1>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Crop Details */}
          <div className="bg-[#303030] p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-white mb-4">Crop Details</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Crop Grade */}
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Crop Grade *</label>
                <select
                  className="w-full bg-[#212121] border border-[#424242] rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                  value={formData.grade}
                  onChange={(e) => setFormData({ ...formData, grade: e.target.value })}
                  required
                >
                  <option value="">Select crop grade</option>
                  {GRADES.map((crop) => (
                    <option key={crop} value={crop}>
                      {crop}
                    </option>
                  ))}
                </select>
              </div>

              {/* Price */}
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Price per Unit (KTT) *</label>
                <input
                  type="number"
                  min="0"
                  max="999999.99"
                  step="0.01"
                  className="w-full bg-[#212121] border border-[#424242] rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                  value={formData.price || ''}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value)
                    if (!isNaN(value) && value >= 0 && value <= 999999.99) {
                      setFormData({ ...formData, price: value })
                    }
                  }}
                  required
                  placeholder="0.00"
                />
              </div>

              {/* Quantity */}
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Quantity *</label>
                <div className="flex">
                  <input
                    type="number"
                    min="0"
                    max={formData.unit === 'tons' ? 999999.99 : 999999999.99}
                    step="0.01"
                    className="flex-1 bg-[#212121] border border-[#424242] rounded-l-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    value={formData.quantity || ''}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value)
                      const maxValue = formData.unit === 'tons' ? 999999.99 : 999999999.99
                      if (!isNaN(value) && value >= 0 && value <= maxValue) {
                        setFormData({
                          ...formData,
                          quantity: value,
                        })
                      }
                    }}
                    required
                    placeholder="0.00"
                  />
                  <select
                    className="w-24 bg-[#212121] border border-[#424242] rounded-r-lg px-2 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    value={formData.unit}
                    onChange={(e) => {
                      const newUnit = e.target.value as 'kg' | 'tons'
                      const maxValue = newUnit === 'tons' ? 999999.99 : 999999999.99
                      setFormData({
                        ...formData,
                        unit: newUnit,
                        // Adjust quantity if it exceeds the new unit's max value
                        quantity: Math.min(formData.quantity, maxValue),
                      })
                    }}
                  >
                    <option value="kg">kg</option>
                    <option value="tons">tons</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Total Value Display */}
            <div className="mt-4 p-4 bg-[#212121] rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-[#BDBDBD]">Total Price (KTT):</span>
                <span className="text-xl font-bold text-[#00E676]">
                  {((formData.price || 0) * (formData.quantity || 0)).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </span>
              </div>
            </div>
          </div>

          {/* Farmer Details */}
          <div className="bg-[#303030] p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-white mb-4">Farmer Details</h2>

            <div>
              <label className="block text-[#BDBDBD] text-sm mb-2">Farmer Address</label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.address}
                  onChange={(e) => setFormData((prev) => ({ ...prev, address: e.target.value }))}
                  className={clsx(
                    'w-full bg-[#212121] p-3 rounded-lg pr-24',
                    'focus:outline-none focus:ring-2 focus:ring-[#00E676]',
                    errors.address && 'ring-2 ring-red-500',
                  )}
                  placeholder="0x..."
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                  <button
                    type="button"
                    onClick={() => startQRScanner()}
                    className="text-[#00E676] hover:text-[#00E676]/80"
                    title="Scan QR Code"
                    disabled={isScanning}
                  >
                    <QrCode size={20} />
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      navigator.clipboard.writeText(formData.address)
                      toast.success('Address copied!')
                    }}
                    className="text-[#00E676] hover:text-[#00E676]/80"
                    title="Copy Address"
                  >
                    <Copy size={20} />
                  </button>
                </div>
              </div>
              {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
            </div>
          </div>

          {/* Location */}
          <div className="bg-[#303030] p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-white mb-4">Growth Location</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Address *</label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full bg-[#212121] border border-[#424242] rounded-lg pl-10 pr-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    value={formData.location.address}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        location: {
                          ...formData.location,
                          address: e.target.value,
                        },
                      })
                    }
                    required
                  />
                  <MapPin className="absolute left-3 top-2.5 h-5 w-5 text-[#BDBDBD]" />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Latitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    className="w-full bg-[#212121] border border-[#424242] rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    value={formData.location.coordinates.lat || ''}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        location: {
                          ...formData.location,
                          coordinates: {
                            ...formData.location.coordinates,
                            lat: e.target.value,
                          },
                        },
                      })
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Longitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    className="w-full bg-[#212121] border border-[#424242] rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    value={formData.location.coordinates.lng || ''}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        location: {
                          ...formData.location,
                          coordinates: {
                            ...formData.location.coordinates,
                            lng: e.target.value,
                          },
                        },
                      })
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Growth Period */}
          <div className="bg-[#303030] p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-white mb-4">Growth Period</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Start Date *</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full bg-[#212121] border border-[#424242] rounded-lg pl-10 pr-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    value={formData.growthPeriod.startDate}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        growthPeriod: {
                          ...formData.growthPeriod,
                          startDate: e.target.value,
                        },
                      })
                    }
                    required
                  />
                  <Calendar className="absolute left-3 top-2.5 h-5 w-5 text-[#BDBDBD]" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Expected Harvest Date *</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full bg-[#212121] border border-[#424242] rounded-lg pl-10 pr-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    value={formData.growthPeriod.harvestDate}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        growthPeriod: {
                          ...formData.growthPeriod,
                          harvestDate: e.target.value,
                        },
                      })
                    }
                    required
                  />
                  <Calendar className="absolute left-3 top-2.5 h-5 w-5 text-[#BDBDBD]" />
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-[#303030] p-6 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-white mb-4">Additional Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Soil Type</label>
                <select
                  className="w-full bg-[#212121] border border-[#424242] rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                  value={formData.soilType}
                  onChange={(e) => setFormData({ ...formData, soilType: e.target.value })}
                >
                  <option value="">Select soil type</option>
                  {SOIL_TYPES.map((soil) => (
                    <option key={soil} value={soil}>
                      {soil}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Irrigation Method</label>
                <select
                  className="w-full bg-[#212121] border border-[#424242] rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                  value={formData.irrigation}
                  onChange={(e) => setFormData({ ...formData, irrigation: e.target.value })}
                >
                  <option value="">Select irrigation method</option>
                  {IRRIGATION_METHODS.map((method) => (
                    <option key={method} value={method}>
                      {method}
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Fertilizers Used</label>
                <div className="flex flex-wrap gap-2">
                  {FERTILIZER_OPTIONS.map((fertilizer) => (
                    <label key={fertilizer} className="inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-[#00E676] bg-[#212121] border-[#424242] rounded focus:ring-[#00E676]"
                        checked={formData.fertilizers.includes(fertilizer)}
                        onChange={(e) => {
                          const newFertilizers = e.target.checked
                            ? [...formData.fertilizers, fertilizer]
                            : formData.fertilizers.filter((f) => f !== fertilizer)
                          setFormData({
                            ...formData,
                            fertilizers: newFertilizers,
                          })
                        }}
                      />
                      <span className="ml-2 text-sm text-white">{fertilizer}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Certifications</label>
                <textarea
                  className="w-full bg-[#212121] border border-[#424242] rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                  rows={3}
                  value={formData.certifications}
                  onChange={(e) => setFormData({ ...formData, certifications: e.target.value })}
                  placeholder="Enter any certification details..."
                />
              </div>
            </div>
          </div>

          {/* Estimated Rewards */}
          <div className="bg-[#1B5E20] p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">Estimated Rewards</h2>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-[#BDBDBD]">Base Rate:</span>
                <span className="text-white">10 tokens/ton</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-[#BDBDBD]">Quantity:</span>
                <span className="text-white">
                  {formData.quantity} {formData.unit}
                </span>
              </div>
              <div className="border-t border-[#2E7D32] my-2 pt-2">
                <div className="flex justify-between items-center">
                  <span className="text-[#BDBDBD]">Estimated Total:</span>
                  <span className="text-2xl font-bold text-[#00E676]">{calculateRewards()} tokens</span>
                </div>
              </div>
            </div>
          </div>

          {/* Terms & Conditions */}
          <div className="bg-[#303030] p-6 rounded-lg">
            <label className="flex items-start space-x-3">
              <input
                type="checkbox"
                className="mt-1 form-checkbox h-4 w-4 text-[#00E676] bg-[#212121] border-[#424242] rounded focus:ring-[#00E676]"
                checked={formData.termsAccepted}
                onChange={(e) => setFormData({ ...formData, termsAccepted: e.target.checked })}
                required
              />
              <span className="text-sm text-[#BDBDBD]">
                I accept the{' '}
                <a href="#" className="text-[#00E676] hover:underline">
                  terms and conditions
                </a>{' '}
                for pledging my crops and understand the reward calculation process.
              </span>
            </label>
          </div>

          {/* Success/Error Messages */}
          {showSuccess && (
            <div className="bg-[#1B5E20] text-white p-4 rounded-lg">
              Pledge submitted successfully! You will receive a confirmation email shortly.
            </div>
          )}
          {showError && (
            <div className="bg-red-600 text-white p-4 rounded-lg">An error occurred while submitting your pledge. Please try again.</div>
          )}

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-[#1B5E20] text-white px-6 py-3 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Submitting...</span>
                </>
              ) : (
                <span>Submit Pledge</span>
              )}
            </button>
            <button
              type="button"
              onClick={() => window.history.back()}
              className="flex-1 sm:flex-none bg-[#424242] text-white px-6 py-3 rounded-lg hover:bg-[#616161] transition-colors duration-300"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
