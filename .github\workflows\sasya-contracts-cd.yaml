name: Release sasya-contracts

on:
  workflow_call:

jobs:
  deploy-testnet:
    runs-on: "ubuntu-latest"
    
    environment: contract-testnet
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Install poetry
        run: pipx install poetry

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          cache: "poetry"

      - name: Install algokit
        run: pipx install algokit

      - name: Bootstrap dependencies
        run: algokit project bootstrap all --project-name 'sasya-contracts'

      - name: Configure git
        shell: bash
        run: |
          # set git user and email as test invoke git
          git config --global user.email "<EMAIL>" && git config --global user.name "github-actions"

      - name: Deploy to testnet
        run: algokit project deploy testnet --project-name 'sasya-contracts'
        env:
          # This is the account that becomes the creator of the contract
          DEPLOYER_MNEMONIC: ${{ secrets.DEPLOYER_MNEMONIC }}
          # The dispenser account is used to ensure the deployer account is funded
          DISPENSER_MNEMONIC: ${{ secrets.DISPENSER_MNEMONIC }}
