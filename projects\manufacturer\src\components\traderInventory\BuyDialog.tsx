import { InventoryItem } from '@/utils/types'
import { useEffect, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import apiClient from '@/services/apiClient'
import { algorand, proofOfUnlockFactory } from '@/utils/algo'
import { VITE_ALGORAND_ASSET_ID_KTT } from '@/utils/variable'
import { useWallet } from '@txnlab/use-wallet-react'
import algosdk from 'algosdk'
import { useNavigate } from 'react-router-dom'

interface Props {
  isDialogOpen: boolean
  setIsDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
  selectedItem: InventoryItem | null
}

interface BatchCost {
  batchId: string
  costPerTon: number
}

interface Batch {
  crop_id: number
  crop_grade: string
  quantity: number
  unit: string
}

export default function BuyDialog({ isDialogOpen, setIsDialogOpen, selectedItem }: Props) {
  const navigate = useNavigate()

  const [batches, setBatches] = useState<Batch[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [selectedBatches, setSelectedBatches] = useState<string[]>([])
  const [batchCosts, setBatchCosts] = useState<BatchCost[]>([])
  const [globalCost, setGlobalCost] = useState<string>('')
  const { activeAddress, transactionSigner: signer } = useWallet()

  useEffect(() => {
    if (isDialogOpen && selectedItem) {
      setLoading(true)
      setError(null)
      apiClient
        .get(`/inventory/trader-inventory-crop-status/${selectedItem.trader.id}/`)
        .then((res) => {
          setBatches(res.data)
        })
        .catch((err) => {
          console.log(err)
          setError('Failed to load batches')
        })
        .finally(() => setLoading(false))
    } else {
      setBatches([])
      setBatchCosts([])
      setGlobalCost('')
      setSelectedBatches([])
    }
  }, [isDialogOpen, selectedItem])

  const handlePurchase = async () => {
    try {
      if (!activeAddress) {
        setError('Wallet not connected.')
        return
      }
      if (!selectedItem) {
        setError('No item selected.')
        return
      }
      const factory = await proofOfUnlockFactory(selectedBatches.join('-'), activeAddress, signer)
      const appAddressString = algosdk.encodeAddress(Uint8Array.from(Object.values(factory.appClient.appAddress.publicKey)))
      await algorand.send.payment({
        sender: activeAddress,
        receiver: factory.appClient.appAddress,
        amount: (2).algo(),
        signer: signer,
      })
      const txId = await factory.appClient.send.createContract({
        args: {
          buyer: activeAddress,
          seller: selectedItem.trader.account_address,
          totalAmount: BigInt(Math.round(Number(calculateTotalCost().toFixed(2)) * 100)),
          asset: BigInt(VITE_ALGORAND_ASSET_ID_KTT),
          milestonePercentages: [BigInt(25), BigInt(25), BigInt(50)],
        },
      })
      apiClient.post('/transaction/transfer-ktt/', {
        amount: calculateTotalCost().toFixed(2),
        reciver: appAddressString,
      })
      await factory.appClient.send.approveMilestone({
        args: {
          milestoneIndex: BigInt(0),
        },
      })

      await factory.appClient.send.releaseFunds({
        args: {
          asset: BigInt(VITE_ALGORAND_ASSET_ID_KTT),
        },
      })

      console.log('App address', appAddressString)
      console.log('Tx id', txId)
      const apiResponse = await apiClient.post('/proof-of-unlock/proof-of-unlock/', {
        tx_id: txId.txIds[0],
        contract_address: appAddressString,
        buyer: activeAddress,
        seller: selectedItem.trader.account_address,
        total_amount: calculateTotalCost().toFixed(2),
        release_amount: (calculateTotalCost() * 0.5).toFixed(2),
        crops: batches
          .filter((batch) => selectedBatches.includes(batch.crop_id.toString()))
          .map((batch) => ({
            crop_id: batch.crop_id.toString(),
            quantity: batch.quantity.toString(),
            unit: batch.unit,
          })),
        milestones: [
          {
            name: 'Advance Payment',
            description: 'Initial contract signing and deposit',
            amount: (calculateTotalCost() * 0.5).toFixed(2),
            status: 'released',
            completed_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          },
          {
            name: 'Shipment',
            description: 'Shipment of cocoa beans',
            amount: (calculateTotalCost() * 0.25).toFixed(2),
            status: 'pending',
            completed_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          },
          {
            name: 'Final Payment',
            description: 'Final payment for the cocoa beans',
            amount: (calculateTotalCost() * 0.25).toFixed(2),
            status: 'pending',
          },
        ],
      })

      console.log(apiResponse)
      if (apiResponse.status >= 200 && apiResponse.status < 300) {
        navigate('/proof-of-unlock')
      } else {
        setError('Failed to create proof of unlock.')
      }

      // Close the dialog
      setIsDialogOpen(false)
      setSelectedBatches([])
      setBatchCosts([])
      setGlobalCost('')
    } catch (err: unknown) {
      console.log('Error', err)
      if (
        err &&
        typeof err === 'object' &&
        'response' in err &&
        err.response &&
        typeof err.response === 'object' &&
        'data' in err.response
      ) {
        setError(JSON.stringify(err.response.data))
        console.error('API error:', err.response.data)
      } else if (err instanceof Error) {
        setError(err.message)
      } else {
        setError('An error occurred during purchase.')
      }
    }
  }

  const handleBatchToggle = (batchId: string) => {
    setSelectedBatches((prev) => {
      // If already selected, remove it
      if (prev.includes(batchId)) {
        setBatchCosts((costs) => costs.filter((cost) => cost.batchId !== batchId))
        return prev.filter((id) => id !== batchId)
      }
      // Otherwise add it with the current global cost
      if (globalCost) {
        handleCostChange(batchId, globalCost)
      }
      return [...prev, batchId]
    })
  }

  const handleCostChange = (batchId: string, cost: string) => {
    const numericCost = parseFloat(cost) || 0
    setBatchCosts((prev) => {
      const existingCost = prev.find((c) => c.batchId === batchId)
      if (existingCost) {
        return prev.map((c) => (c.batchId === batchId ? { ...c, costPerTon: numericCost } : c))
      }
      return [...prev, { batchId, costPerTon: numericCost }]
    })
  }

  const handleGlobalCostChange = (cost: string) => {
    setGlobalCost(cost)
    selectedBatches.forEach((batchId) => handleCostChange(batchId, cost))
  }

  const isBatchSelected = (batchId: string) => {
    return selectedBatches.includes(batchId)
  }

  const calculateTotalCost = () => {
    return batchCosts.reduce((total, { batchId, costPerTon }) => {
      const batch = batches.find((b) => b.crop_id.toString() === batchId)
      return total + (batch ? batch.quantity * costPerTon : 0)
    }, 0)
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogContent className="bg-card-bg text-primary-text border border-card-border">
        <DialogHeader>
          <DialogTitle className="text-primary-text">Select Batches to Purchase</DialogTitle>
          <DialogDescription className="text-secondary-text">
            {selectedItem && `Choose batches of ${selectedItem.crop_name} to purchase. You can select multiple batches.`}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-primary-text mb-1">Available Batches</h3>
            <p className="text-sm text-secondary-text mb-4">Select the batches you wish to purchase</p>

            {loading ? (
              <div className="text-center text-secondary-text py-4">Loading batches...</div>
            ) : (
              <>
                {error && <div className="text-center text-button-danger bg-button-danger/10 p-3 rounded-lg">{error}</div>}
                <div className="grid gap-3">
                  {batches.map((batch) => (
                    <Card
                      key={batch.crop_id}
                      className={`transition-all duration-200 hover:shadow-md ${
                        isBatchSelected(batch.crop_id.toString())
                          ? 'bg-accent-light/10 border-accent'
                          : 'bg-card-bg border-card-border hover:border-accent/50'
                      }`}
                      onClick={() => handleBatchToggle(batch.crop_id.toString())}
                    >
                      <div className="p-4 cursor-pointer">
                        <div className="flex items-center">
                          <div className="mr-3">
                            <Checkbox
                              id={`batch-${batch.crop_id}`}
                              className="h-5 w-5 border-2 border-card-border data-[state=checked]:bg-accent data-[state=checked]:text-button-text"
                              checked={isBatchSelected(batch.crop_id.toString())}
                              onCheckedChange={() => handleBatchToggle(batch.crop_id.toString())}
                            />
                          </div>

                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                              <div>
                                <Label
                                  htmlFor={`batch-${batch.crop_id}`}
                                  className="text-base font-medium cursor-pointer text-primary-text"
                                >
                                  {batch.crop_id}
                                </Label>
                                <p className="text-xs text-secondary-text mt-1">Grade: {batch.crop_grade}</p>
                              </div>

                              <div className="mt-2 sm:mt-0 flex flex-col items-end gap-2">
                                <span className="inline-flex items-center px-3 py-1 rounded-full bg-accent text-button-text text-sm font-medium">
                                  {batch.quantity} {batch.unit}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </>
            )}

            {selectedBatches.length > 0 && (
              <div className="mt-4 p-4 bg-accent-light/10 rounded-md border border-accent/20">
                <div className="flex items-center justify-between mb-3">
                  <p className="text-sm text-primary-text">
                    <span className="font-medium">{selectedBatches.length}</span> batch{selectedBatches.length !== 1 ? 'es' : ''} selected
                  </p>
                  <div className="flex items-center gap-3">
                    <Label htmlFor="cost-per-ton" className="text-sm text-secondary-text whitespace-nowrap">
                      Cost per ton:
                    </Label>
                    <Input
                      id="cost-per-ton"
                      type="number"
                      min="0"
                      step="0.01"
                      value={globalCost}
                      onChange={(e) => handleGlobalCostChange(e.target.value)}
                      className="w-32 h-9 text-sm text-primary-text bg-card-bg border-card-border focus:border-accent focus:ring-accent/20"
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-primary-text font-medium">Total Cost: KTT {calculateTotalCost().toFixed(2)}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsDialogOpen(false)}
            className="bg-card-bg border-card-border text-primary-text hover:bg-button-danger hover:text-button-text hover:border-button-danger transition-colors"
          >
            Cancel
          </Button>
          <Button
            onClick={handlePurchase}
            disabled={selectedBatches.length === 0}
            className="bg-accent text-button-text hover:bg-accent-hover border border-accent/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Purchase
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
