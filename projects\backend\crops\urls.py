from django.urls import path
from .views import create_crop_transfer, get_crop, get_all_crops, get_crop_transaction_history

urlpatterns = [
    path('crop-transfers/', create_crop_transfer, name='create-crop-transfer'),
    path('crops/<int:crop_id>/', get_crop, name='get-crop'),
    path('crops/', get_all_crops, name='get-all-crops'),
    path('crops/<int:crop_id>/transactions/', get_crop_transaction_history, name='get-crop-transaction-history'),
    # path('crop-transfers/<str:transaction_id>/', update_crop_transfer, name='update-crop-transfer'),
    # path('crop-transfers/<str:transaction_id>/delete/', delete_crop_transfer, name='delete-crop-transfer'),
]
