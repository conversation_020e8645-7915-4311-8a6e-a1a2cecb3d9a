# ======================
# LocalNet configuration
# uncomment below to use
# ======================

VITE_ENVIRONMENT=local

# Algod
VITE_ALGOD_TOKEN=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
VITE_ALGOD_SERVER=http://localhost
VITE_ALGOD_PORT=4001
VITE_ALGOD_NETWORK=""

# Indexer
VITE_INDEXER_TOKEN=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
VITE_INDEXER_SERVER=http://localhost
VITE_INDEXER_PORT=8980

# KMD
# Please note:
# 1. This is only needed for LocalNet since
# by default KMD provider is ignored on other networks.
# 2. AlgoKit LocalNet starts with a single wallet called 'unencrypted-default-wallet',
# with heaps of tokens available for testing.
VITE_KMD_TOKEN=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
VITE_KMD_SERVER=http://localhost
VITE_KMD_PORT=4002
VITE_KMD_WALLET="unencrypted-default-wallet"
VITE_KMD_PASSWORD=""

# # ======================
# # TestNet configuration:
# # uncomment below to use
# # ======================

# VITE_ENVIRONMENT=local

# # Algod
# VITE_ALGOD_TOKEN=""
# VITE_ALGOD_SERVER="https://testnet-api.algonode.cloud"
# VITE_ALGOD_PORT=""
# VITE_ALGOD_NETWORK="testnet"

# # Indexer
# VITE_INDEXER_TOKEN=""
# VITE_INDEXER_SERVER="https://testnet-idx.algonode.cloud"
# VITE_INDEXER_PORT=""


# # ======================
# # MainNet configuration:
# # uncomment below to use
# # ======================

# VITE_ENVIRONMENT=production

# # Algod
# VITE_ALGOD_TOKEN=""
# VITE_ALGOD_SERVER="https://mainnet-api.algonode.cloud"
# VITE_ALGOD_PORT=""
# VITE_ALGOD_NETWORK="mainnet"

# # Indexer
# VITE_INDEXER_TOKEN=""
# VITE_INDEXER_SERVER="https://mainnet-idx.algonode.cloud"
# VITE_INDEXER_PORT=""

