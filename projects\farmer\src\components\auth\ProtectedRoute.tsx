import { ReactNode } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuthLoading, useIsAuthenticated, useUser } from '../../stores/authStore'

interface ProtectedRouteProps {
  children: ReactNode
  redirectPath?: string
  requireAuth?: boolean
  requireOnboarding?: boolean // New prop to check if onboarding is required
}

const ProtectedRoute = ({ children, redirectPath = '/login', requireAuth = true, requireOnboarding = true }: ProtectedRouteProps) => {
  const isAuthenticated = useIsAuthenticated()
  const loading = useAuthLoading()
  const user = useUser()

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  // If authentication is required and user is not authenticated, redirect to login
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectPath} replace />
  }

  // If user is authenticated but onboarding is not completed and onboarding is required
  if (isAuthenticated && user && requireOnboarding && !user.onboarding_completed) {
    return <Navigate to="/onboarding" replace />
  }

  // If user has completed onboarding but trying to access onboarding page, redirect to dashboard
  if (isAuthenticated && user && !requireOnboarding && user.onboarding_completed) {
    return <Navigate to="/dashboard" replace />
  }

  // If authentication is not required but user is authenticated,
  // redirect to appropriate page based on onboarding status
  if (!requireAuth && isAuthenticated && user) {
    if (!user.onboarding_completed) {
      return <Navigate to="/onboarding" replace />
    } else {
      return <Navigate to="/dashboard" replace />
    }
  }

  return <>{children}</>
}

export default ProtectedRoute
