# Trader Global State Management with Zustand

This directory contains the Zustand stores for global state management in the trader application.

## Auth Store (`authStore.ts`)

The auth store manages trader authentication state and provides a complete solution for handling user login, logout, and authentication status with onboarding flow control.

### Features

- **Persistent Storage**: Trader data is automatically saved to localStorage and restored on app reload
- **Type Safety**: Full TypeScript support with proper interfaces
- **Selective Hooks**: Individual hooks for specific parts of the state
- **Error Handling**: Built-in error state management
- **Loading States**: Loading indicators for async operations
- **Onboarding Flow**: Automatic redirection based on `account_address` status for traders

### User Data Structure

Based on the login API response:

```typescript
interface User {
  id: number
  name: string
  email: string
  role: string
  account_address: string
  opt_in: boolean
}

interface LoginResponse {
  token: string
  message: string
  user: User
}
```

### Protected Routes & Onboarding Flow

The `ProtectedRoute` component supports a two-tier protection system for traders:

1. **Authentication Required**: Trader must be logged in
2. **Onboarding Required**: Trader must have completed wallet connection (`account_address` must not be empty/null)

```typescript
// Public route (redirects authenticated traders based on onboarding status)
<ProtectedRoute requireAuth={false}>
  <Login />
</ProtectedRoute>

// Onboarding route (requires auth but not wallet connection)
<ProtectedRoute requireOnboarding={false}>
  <OnboardingFlow />
</ProtectedRoute>

// Fully protected route (requires auth AND wallet connection)
<ProtectedRoute requireOnboarding={true}>
  <Dashboard />
</ProtectedRoute>
```

#### Trader Onboarding Flow Logic:

- **After Login**: If `user.account_address` is empty/null → redirect to `/onboarding`
- **From Onboarding**: After wallet connection → redirect to `/dashboard`
- **Protected Routes**: If `account_address` is empty/null → redirect to `/onboarding`
- **Onboarding Route**: If `account_address` exists → redirect to `/dashboard` (prevent access)
- **Public Routes**: If authenticated and onboarded → redirect to `/dashboard`

### Usage Examples

#### 1. Trader Login Process

```typescript
import { useAuthStore } from '../stores/authStore'
import { login } from '../services/authService'

const TraderLoginComponent = () => {
  const { login: loginUser, loading, setLoading, setError } = useAuthStore()

  const handleLogin = async (email: string, password: string) => {
    setLoading(true)
    try {
      const loginResponse = await login(email, password)
      loginUser(loginResponse) // This saves trader data and token
      // Automatic redirect based on account_address status
    } catch (error) {
      setError('Login failed')
    } finally {
      setLoading(false)
    }
  }
}
```

#### 2. Trader Onboarding Status Checks

```typescript
import { useIsOnboardingCompleted, useRequiresOnboarding } from '../stores/authStore'

const TraderOnboardingStatus = () => {
  const isOnboardingCompleted = useIsOnboardingCompleted()
  const requiresOnboarding = useRequiresOnboarding()

  if (requiresOnboarding) {
    return <div>Please complete trading wallet connection</div>
  }

  return <div>Trading setup completed: {isOnboardingCompleted ? 'Yes' : 'No'}</div>
}
```

#### 3. Handling Trader Onboarding Completion

```typescript
import { useAuthStore } from '../stores/authStore'
import { useNavigate } from 'react-router-dom'

const TraderOnboardingFlow = () => {
  const { updateUser } = useAuthStore()
  const navigate = useNavigate()

  const handleTradingWalletConnected = (walletAddress: string) => {
    // Update trader with wallet address
    updateUser({ account_address: walletAddress })
    
    // Navigate to trading dashboard after onboarding
    navigate('/dashboard')
  }
}
```

### Store Actions

- `login(loginData)` - Handle complete login response
- `logout()` - Clear all trader data
- `updateUser(userData)` - Partially update trader data
- `setLoading(loading)` - Set loading state
- `setError(error)` - Set error message
- `clearError()` - Clear error message
- `checkAuth()` - Check authentication status on app initialization

### Selector Hooks

- `useUser()` - Get trader object
- `useToken()` - Get authentication token
- `useIsAuthenticated()` - Get authentication status
- `useAuthLoading()` - Get loading state
- `useAuthError()` - Get error message
- `useIsOnboardingCompleted()` - Check if trader has completed onboarding
- `useRequiresOnboarding()` - Check if trader needs to complete onboarding

### Trader-Specific Routes

All trader routes are protected with the onboarding flow:

- `/dashboard` - Trading dashboard (requires onboarding)
- `/inventory` - Inventory management (requires onboarding)
- `/shipments` - Shipment tracking (requires onboarding)
- `/transactions` - Transaction history (requires onboarding)
- `/pledge` - Crop pledging (requires onboarding)
- `/pledged-crops` - Pledged crops management (requires onboarding)

### Installation

Make sure Zustand is installed:

```bash
npm install zustand
```

### Integration with API

The auth store works seamlessly with the updated `authService.ts` which returns the complete login response structure matching your API, specifically supporting the trader role.
