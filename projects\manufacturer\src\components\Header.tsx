interface HeaderProps {
  totalBalance: number
  onLogout?: () => void
}
export const Header = ({ totalBalance, onLogout = () => {} }: HeaderProps) => {
  // const isMobile = useIsMobile()

  // const NavLinks = () => (
  //   <>
  //     <a href="#dashboard" className="text-primary-text hover:text-link-text transition-colors duration-300">
  //       Dashboard
  //     </a>
  //     <a href="#transactions" className="text-primary-text hover:text-link-text transition-colors duration-300">
  //       Transactions
  //     </a>
  //     <a href="#news" className="text-primary-text hover:text-link-text transition-colors duration-300">
  //       News
  //     </a>
  //   </>
  // )

  return (
    <header className="w-full bg-alt-bg p-4 md:p-6 shadow-md">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-secondary-text font-bold text-xl md:text-2xl">Manufacturer Dashboard</h1>
          {/* {!isMobile && (
            <nav className="hidden md:flex gap-6 ml-8">
              <NavLinks />
            </nav>
          )} */}
        </div>

        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-primary-text opacity-60 text-sm">Total Balance</p>
            <p className="text-primary-text font-bold">{`$ ${totalBalance.toFixed(2)}`}</p>
          </div>

          <button
            onClick={onLogout}
            className="px-4 py-2 bg-button-danger hover:bg-button-danger-hover text-white rounded-md transition-colors duration-300"
          >
            Logout
          </button>

          {/* {isMobile && (
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent>
                <nav className="flex flex-col gap-4 mt-8">
                  <NavLinks />
                </nav>
              </SheetContent>
            </Sheet>
          )} */}
        </div>
      </div>
    </header>
  )
}
