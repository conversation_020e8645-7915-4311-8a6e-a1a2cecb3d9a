import { CheckCircle2, Clock, Wallet } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Milestone } from '../../stores/transactionStore'

interface MilestoneProgressProps {
  milestones: Milestone[]
}

const MilestoneProgress = ({ milestones }: MilestoneProgressProps) => {
  // Sort milestones by ID to ensure correct order
  const sortedMilestones = [...milestones].sort((a, b) => a.id - b.id)
  const [isVisible, setIsVisible] = useState(false)

  // Simple animation effect on mount
  useEffect(() => {
    setIsVisible(true)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'released':
        return <CheckCircle2 className="w-5 h-5 text-status-completed-text" />
      case 'verified':
      case 'completed':
        return <Wallet className="w-5 h-5 text-status-active-text" />
      default:
        return <Clock className="w-5 h-5 text-secondary-text" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-status-completed-bg'
      case 'verified':
        return 'bg-status-active-bg'
      case 'released':
        return 'bg-accent'
      default:
        return 'bg-accent-light/5'
    }
  }

  const getStatusBorderColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-status-completed-text'
      case 'verified':
        return 'border-status-active-text'
      case 'released':
        return 'border-accent'
      default:
        return 'border-card-border'
    }
  }

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-status-completed-text'
      case 'verified':
        return 'text-status-active-text'
      case 'released':
        return 'text-accent'
      default:
        return 'text-secondary-text'
    }
  }

  return (
    <div className="py-8">
      <div className="relative flex items-center justify-between">
        {/* Background progress line */}
        <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-1 bg-accent-light/5"></div>

        {/* Milestones */}
        {sortedMilestones.map((milestone, index) => (
          <div key={milestone.id} className="relative flex flex-col items-center z-10 w-full">
            {/* Circle with icon */}
            <div
              className={`flex items-center justify-center w-10 h-10 rounded-full ${getStatusBorderColor(
                milestone.status,
              )} transition-all duration-300 ease-in-out ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-80'} hover:scale-110`}
              style={{ transitionDelay: `${index * 100}ms` }}
            >
              {getStatusIcon(milestone.status)}
            </div>

            {/* Progress fill line */}
            {index < sortedMilestones.length - 1 && (
              <div
                className="absolute left-1/2 top-1/2 transform -translate-y-1/2 h-1"
                style={{ width: 'calc(100% - 10px)', left: '10px' }}
              >
                <div
                  className={`h-full transition-all duration-500 ease-in-out ${getStatusColor(milestone.status)}`}
                  style={{
                    width: isVisible && milestone.status !== 'pending' ? '100%' : '0%',
                    transitionDelay: `${index * 100}ms`,
                  }}
                />
              </div>
            )}

            {/* Text labels */}
            <div className="mt-4 text-center w-full max-w-[90px] mx-auto">
              <p className={`text-xs font-medium truncate ${getStatusTextColor(milestone.status)}`}>{milestone.name}</p>
              <p className="text-xs text-secondary-text mt-1">${milestone.amount}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default MilestoneProgress
