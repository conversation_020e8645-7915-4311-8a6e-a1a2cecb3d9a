# flake8: noqa
# fmt: off
# mypy: ignore-errors
# This file was automatically generated by algokit-client-generator.
# DO NOT MODIFY IT BY HAND.
# requires: algokit-utils@^3.0.0

# common
import dataclasses
import typing
# core algosdk
import algosdk
from algosdk.transaction import OnComplete
from algosdk.atomic_transaction_composer import TransactionSigner
from algosdk.source_map import SourceMap
from algosdk.transaction import Transaction
from algosdk.v2client.models import SimulateTraceConfig
# utils
import algokit_utils
from algokit_utils import AlgorandClient as _AlgoKitAlgorandClient

_APP_SPEC_JSON = r"""{"arcs": [22, 28], "bareActions": {"call": [], "create": ["NoOp"]}, "methods": [{"actions": {"call": ["NoOp"], "create": []}, "args": [], "name": "create", "returns": {"type": "byte[]"}, "desc": "Create the Agri USD Coin ASA.", "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [], "name": "get_asset_id", "returns": {"type": "uint64"}, "events": [], "readonly": true, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [], "name": "get_minted_tokens", "returns": {"type": "uint64"}, "events": [], "readonly": true, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [], "name": "get_burnt_tokens", "returns": {"type": "uint64"}, "events": [], "readonly": true, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "amount"}, {"type": "account", "name": "receiver"}], "name": "mint_tokens", "returns": {"type": "byte[]"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "amount"}, {"type": "account", "name": "address"}], "name": "burn_tokens", "returns": {"type": "byte[]"}, "events": [], "readonly": false, "recommendations": {}}, {"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "amount"}, {"type": "account", "name": "receiver"}, {"type": "account", "name": "account"}], "name": "transfer_tokens", "returns": {"type": "byte[]"}, "events": [], "readonly": false, "recommendations": {}}], "name": "AgriUSDCoin", "state": {"keys": {"box": {}, "global": {"minted_tokens": {"key": "bWludGVkX3Rva2Vucw==", "keyType": "AVMString", "valueType": "AVMUint64"}, "burnt_tokens": {"key": "YnVybnRfdG9rZW5z", "keyType": "AVMString", "valueType": "AVMUint64"}, "asset": {"key": "YXNzZXQ=", "keyType": "AVMString", "valueType": "AVMUint64"}}, "local": {}}, "maps": {"box": {}, "global": {}, "local": {}}, "schema": {"global": {"bytes": 0, "ints": 3}, "local": {"bytes": 0, "ints": 0}}}, "structs": {}, "byteCode": {"approval": "CiADAAEEJgQFYXNzZXQEFR98dQ1taW50ZWRfdG9rZW5zDGJ1cm50X3Rva2VuczEYQAAJKiJnKyJnKCJnMRtBAO6CBwSCE63mBFuiKoQEUo+dywSamI+VBHrKjfoELZoN5wTDcvl5NhoAjgcAngCNAHwAawBKACkAAiJDMRkURDEYRDYaATYaAhfAHDYaAxfAHIgBZ0kVFlcGAkxQKUxQsCNDMRkURDEYRDYaATYaAhfAHIgBFkkVFlcGAkxQKUxQsCNDMRkURDEYRDYaATYaAhfAHIgAykkVFlcGAkxQKUxQsCNDMRkURDEYRIgArRYpTFCwI0MxGRREMRhEiACXFilMULAjQzEZFEQxGESIAIEWKUxQsCNDMRkURDEYRIgAGUkVFlcGAkxQKUxQsCNDMRlA/0UxGBREI0MxADIJEkQiKGVEFESxMgAyCkcDsiyyK7IqsimACEFHUkktVVNEsiWADUFncmkgVVNEIENvaW6yJoECsiOB////////////AbIigQOyELIBs7QXKLQ8Z4kiKGVEiSIqZUSJIitlRImKAgGxMgAiKGVEi/4XSbISTLIRi/+yFCSyEEyyAbO0FyIqZURPAggqTGeJigIBsTIAMgoiKGVEi/4Xi/+yE0myEkyyEUyyFCSyEEyyAbO0FyIrZURPAggrTGeJigMBsTIAIihlRIv9F4v/shNJshJMshGL/rIUJLIQTLIBs7QXIiplRE8CCCpMZ4k=", "clear": "CoEBQw=="}, "compilerInfo": {"compiler": "puya", "compilerVersion": {"major": 4, "minor": 7, "patch": 0}}, "events": [], "networks": {}, "source": {"approval": "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", "clear": "I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"}, "sourceInfo": {"approval": {"pcOffsetMethod": "none", "sourceInfo": [{"pc": [325], "errorMessage": "ASA already created"}, {"pc": [126, 165, 198, 231, 248, 265, 282], "errorMessage": "OnCompletion is not NoOp"}, {"pc": [319], "errorMessage": "Only the creator can create the ASA"}, {"pc": [311], "errorMessage": "can only call when creating"}, {"pc": [129, 168, 201, 234, 251, 268, 285], "errorMessage": "can only call when not creating"}, {"pc": [323, 404, 425, 470, 516], "errorMessage": "check self.asset exists"}, {"pc": [414, 499], "errorMessage": "check self.burnt_tokens exists"}, {"pc": [409, 451, 546], "errorMessage": "check self.minted_tokens exists"}]}, "clear": {"pcOffsetMethod": "none", "sourceInfo": []}}, "templateVariables": {}}"""
APP_SPEC = algokit_utils.Arc56Contract.from_json(_APP_SPEC_JSON)

def _parse_abi_args(args: object | None = None) -> list[object] | None:
    """Helper to parse ABI args into the format expected by underlying client"""
    if args is None:
        return None

    def convert_dataclass(value: object) -> object:
        if dataclasses.is_dataclass(value):
            return tuple(convert_dataclass(getattr(value, field.name)) for field in dataclasses.fields(value))
        elif isinstance(value, (list, tuple)):
            return type(value)(convert_dataclass(item) for item in value)
        return value

    match args:
        case tuple():
            method_args = list(args)
        case _ if dataclasses.is_dataclass(args):
            method_args = [getattr(args, field.name) for field in dataclasses.fields(args)]
        case _:
            raise ValueError("Invalid 'args' type. Expected 'tuple' or 'TypedDict' for respective typed arguments.")

    return [
        convert_dataclass(arg) if not isinstance(arg, algokit_utils.AppMethodCallTransactionArgument) else arg
        for arg in method_args
    ] if method_args else None

def _init_dataclass(cls: type, data: dict) -> object:
    """
    Recursively instantiate a dataclass of type `cls` from `data`.

    For each field on the dataclass, if the field type is also a dataclass
    and the corresponding data is a dict, instantiate that field recursively.
    """
    field_values = {}
    for field in dataclasses.fields(cls):
        field_value = data.get(field.name)
        # Check if the field expects another dataclass and the value is a dict.
        if dataclasses.is_dataclass(field.type) and isinstance(field_value, dict):
            field_values[field.name] = _init_dataclass(typing.cast(type, field.type), field_value)
        else:
            field_values[field.name] = field_value
    return cls(**field_values)

@dataclasses.dataclass(frozen=True, kw_only=True)
class MintTokensArgs:
    """Dataclass for mint_tokens arguments"""
    amount: int
    receiver: str | bytes

    @property
    def abi_method_signature(self) -> str:
        return "mint_tokens(uint64,account)byte[]"

@dataclasses.dataclass(frozen=True, kw_only=True)
class BurnTokensArgs:
    """Dataclass for burn_tokens arguments"""
    amount: int
    address: str | bytes

    @property
    def abi_method_signature(self) -> str:
        return "burn_tokens(uint64,account)byte[]"

@dataclasses.dataclass(frozen=True, kw_only=True)
class TransferTokensArgs:
    """Dataclass for transfer_tokens arguments"""
    amount: int
    receiver: str | bytes
    account: str | bytes

    @property
    def abi_method_signature(self) -> str:
        return "transfer_tokens(uint64,account,account)byte[]"


class AgriUsdCoinParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def create(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "create()byte[]",
        }))

    def get_asset_id(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_asset_id()uint64",
        }))

    def get_minted_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_minted_tokens()uint64",
        }))

    def get_burnt_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_burnt_tokens()uint64",
        }))

    def mint_tokens(
        self,
        args: tuple[int, str | bytes] | MintTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "mint_tokens(uint64,account)byte[]",
            "args": method_args,
        }))

    def burn_tokens(
        self,
        args: tuple[int, str | bytes] | BurnTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "burn_tokens(uint64,account)byte[]",
            "args": method_args,
        }))

    def transfer_tokens(
        self,
        args: tuple[int, str | bytes, str | bytes] | TransferTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "transfer_tokens(uint64,account,account)byte[]",
            "args": method_args,
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> algokit_utils.AppCallParams:
        return self.app_client.params.bare.clear_state(
            params,
            
        )


class AgriUsdCoinCreateTransactionParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def create(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "create()byte[]",
        }))

    def get_asset_id(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_asset_id()uint64",
        }))

    def get_minted_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_minted_tokens()uint64",
        }))

    def get_burnt_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
    
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_burnt_tokens()uint64",
        }))

    def mint_tokens(
        self,
        args: tuple[int, str | bytes] | MintTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "mint_tokens(uint64,account)byte[]",
            "args": method_args,
        }))

    def burn_tokens(
        self,
        args: tuple[int, str | bytes] | BurnTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "burn_tokens(uint64,account)byte[]",
            "args": method_args,
        }))

    def transfer_tokens(
        self,
        args: tuple[int, str | bytes, str | bytes] | TransferTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "transfer_tokens(uint64,account,account)byte[]",
            "args": method_args,
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> Transaction:
        return self.app_client.create_transaction.bare.clear_state(
            params,
            
        )


class AgriUsdCoinSend:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def create(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[bytes]:
    
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "create()byte[]",
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[bytes], parsed_response)

    def get_asset_id(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[int]:
    
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_asset_id()uint64",
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[int], parsed_response)

    def get_minted_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[int]:
    
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_minted_tokens()uint64",
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[int], parsed_response)

    def get_burnt_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[int]:
    
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "get_burnt_tokens()uint64",
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[int], parsed_response)

    def mint_tokens(
        self,
        args: tuple[int, str | bytes] | MintTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[bytes]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "mint_tokens(uint64,account)byte[]",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[bytes], parsed_response)

    def burn_tokens(
        self,
        args: tuple[int, str | bytes] | BurnTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[bytes]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "burn_tokens(uint64,account)byte[]",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[bytes], parsed_response)

    def transfer_tokens(
        self,
        args: tuple[int, str | bytes, str | bytes] | TransferTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[bytes]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "transfer_tokens(uint64,account,account)byte[]",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[bytes], parsed_response)

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[algokit_utils.ABIReturn]:
        return self.app_client.send.bare.clear_state(
            params,
            send_params=send_params,
        )


class GlobalStateValue(typing.TypedDict):
    """Shape of global_state state key values"""
    minted_tokens: int
    burnt_tokens: int
    asset: int

class AgriUsdCoinState:
    """Methods to access state for the current AgriUSDCoin app"""

    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    @property
    def global_state(
        self
    ) -> "_GlobalState":
            """Methods to access global_state for the current app"""
            return _GlobalState(self.app_client)

class _GlobalState:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client
        
        # Pre-generated mapping of value types to their struct classes
        self._struct_classes: dict[str, typing.Type[typing.Any]] = {}

    def get_all(self) -> GlobalStateValue:
        """Get all current keyed values from global_state state"""
        result = self.app_client.state.global_state.get_all()
        if not result:
            return typing.cast(GlobalStateValue, {})

        converted = {}
        for key, value in result.items():
            key_info = self.app_client.app_spec.state.keys.global_state.get(key)
            struct_class = self._struct_classes.get(key_info.value_type) if key_info else None
            converted[key] = (
                _init_dataclass(struct_class, value) if struct_class and isinstance(value, dict)
                else value
            )
        return typing.cast(GlobalStateValue, converted)

    @property
    def minted_tokens(self) -> int:
        """Get the current value of the minted_tokens key in global_state state"""
        value = self.app_client.state.global_state.get_value("minted_tokens")
        if isinstance(value, dict) and "AVMUint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["AVMUint64"], value)  # type: ignore
        return typing.cast(int, value)

    @property
    def burnt_tokens(self) -> int:
        """Get the current value of the burnt_tokens key in global_state state"""
        value = self.app_client.state.global_state.get_value("burnt_tokens")
        if isinstance(value, dict) and "AVMUint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["AVMUint64"], value)  # type: ignore
        return typing.cast(int, value)

    @property
    def asset(self) -> int:
        """Get the current value of the asset key in global_state state"""
        value = self.app_client.state.global_state.get_value("asset")
        if isinstance(value, dict) and "AVMUint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["AVMUint64"], value)  # type: ignore
        return typing.cast(int, value)

class AgriUsdCoinClient:
    """Client for interacting with AgriUSDCoin smart contract"""

    @typing.overload
    def __init__(self, app_client: algokit_utils.AppClient) -> None: ...
    
    @typing.overload
    def __init__(
        self,
        *,
        algorand: _AlgoKitAlgorandClient,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None: ...

    def __init__(
        self,
        app_client: algokit_utils.AppClient | None = None,
        *,
        algorand: _AlgoKitAlgorandClient | None = None,
        app_id: int | None = None,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None:
        if app_client:
            self.app_client = app_client
        elif algorand and app_id:
            self.app_client = algokit_utils.AppClient(
                algokit_utils.AppClientParams(
                    algorand=algorand,
                    app_spec=APP_SPEC,
                    app_id=app_id,
                    app_name=app_name,
                    default_sender=default_sender,
                    default_signer=default_signer,
                    approval_source_map=approval_source_map,
                    clear_source_map=clear_source_map,
                )
            )
        else:
            raise ValueError("Either app_client or algorand and app_id must be provided")
    
        self.params = AgriUsdCoinParams(self.app_client)
        self.create_transaction = AgriUsdCoinCreateTransactionParams(self.app_client)
        self.send = AgriUsdCoinSend(self.app_client)
        self.state = AgriUsdCoinState(self.app_client)

    @staticmethod
    def from_creator_and_name(
        creator_address: str,
        app_name: str,
        algorand: _AlgoKitAlgorandClient,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
    ) -> "AgriUsdCoinClient":
        return AgriUsdCoinClient(
            algokit_utils.AppClient.from_creator_and_name(
                creator_address=creator_address,
                app_name=app_name,
                app_spec=APP_SPEC,
                algorand=algorand,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
                ignore_cache=ignore_cache,
                app_lookup_cache=app_lookup_cache,
            )
        )
    
    @staticmethod
    def from_network(
        algorand: _AlgoKitAlgorandClient,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "AgriUsdCoinClient":
        return AgriUsdCoinClient(
            algokit_utils.AppClient.from_network(
                app_spec=APP_SPEC,
                algorand=algorand,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    @property
    def app_id(self) -> int:
        return self.app_client.app_id
    
    @property
    def app_address(self) -> str:
        return self.app_client.app_address
    
    @property
    def app_name(self) -> str:
        return self.app_client.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_client.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_client.algorand

    def clone(
        self,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "AgriUsdCoinClient":
        return AgriUsdCoinClient(
            self.app_client.clone(
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    def new_group(self) -> "AgriUsdCoinComposer":
        return AgriUsdCoinComposer(self)

    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["create()byte[]"],
        return_value: algokit_utils.ABIReturn | None
    ) -> bytes | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["get_asset_id()uint64"],
        return_value: algokit_utils.ABIReturn | None
    ) -> int | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["get_minted_tokens()uint64"],
        return_value: algokit_utils.ABIReturn | None
    ) -> int | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["get_burnt_tokens()uint64"],
        return_value: algokit_utils.ABIReturn | None
    ) -> int | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["mint_tokens(uint64,account)byte[]"],
        return_value: algokit_utils.ABIReturn | None
    ) -> bytes | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["burn_tokens(uint64,account)byte[]"],
        return_value: algokit_utils.ABIReturn | None
    ) -> bytes | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["transfer_tokens(uint64,account,account)byte[]"],
        return_value: algokit_utils.ABIReturn | None
    ) -> bytes | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None: ...

    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None | bytes | int:
        """Decode ABI return value for the given method."""
        if return_value is None:
            return None
    
        arc56_method = self.app_spec.get_arc56_method(method)
        decoded = return_value.get_arc56_value(arc56_method, self.app_spec.structs)
    
        # If method returns a struct, convert the dict to appropriate dataclass
        if (arc56_method and
            arc56_method.returns and
            arc56_method.returns.struct and
            isinstance(decoded, dict)):
            struct_class = globals().get(arc56_method.returns.struct)
            if struct_class:
                return struct_class(**typing.cast(dict, decoded))
        return decoded


@dataclasses.dataclass(frozen=True)
class AgriUsdCoinBareCallCreateParams(algokit_utils.AppClientBareCallCreateParams):
    """Parameters for creating AgriUsdCoin contract with bare calls"""
    on_complete: typing.Literal[OnComplete.NoOpOC] | None = None

    def to_algokit_utils_params(self) -> algokit_utils.AppClientBareCallCreateParams:
        return algokit_utils.AppClientBareCallCreateParams(**self.__dict__)

class AgriUsdCoinFactory(algokit_utils.TypedAppFactoryProtocol[AgriUsdCoinBareCallCreateParams, None, None]):
    """Factory for deploying and managing AgriUsdCoinClient smart contracts"""

    def __init__(
        self,
        algorand: _AlgoKitAlgorandClient,
        *,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        version: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ):
        self.app_factory = algokit_utils.AppFactory(
            params=algokit_utils.AppFactoryParams(
                algorand=algorand,
                app_spec=APP_SPEC,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                version=version,
                compilation_params=compilation_params,
            )
        )
        self.params = AgriUsdCoinFactoryParams(self.app_factory)
        self.create_transaction = AgriUsdCoinFactoryCreateTransaction(self.app_factory)
        self.send = AgriUsdCoinFactorySend(self.app_factory)

    @property
    def app_name(self) -> str:
        return self.app_factory.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_factory.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_factory.algorand

    def deploy(
        self,
        *,
        on_update: algokit_utils.OnUpdate | None = None,
        on_schema_break: algokit_utils.OnSchemaBreak | None = None,
        create_params: AgriUsdCoinBareCallCreateParams | None = None,
        update_params: None = None,
        delete_params: None = None,
        existing_deployments: algokit_utils.ApplicationLookup | None = None,
        ignore_cache: bool = False,
        app_name: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
    ) -> tuple[AgriUsdCoinClient, algokit_utils.AppFactoryDeployResult]:
        """Deploy the application"""
        deploy_response = self.app_factory.deploy(
            on_update=on_update,
            on_schema_break=on_schema_break,
            create_params=create_params.to_algokit_utils_params() if create_params else None,
            update_params=update_params,
            delete_params=delete_params,
            existing_deployments=existing_deployments,
            ignore_cache=ignore_cache,
            app_name=app_name,
            compilation_params=compilation_params,
            send_params=send_params,
        )

        return AgriUsdCoinClient(deploy_response[0]), deploy_response[1]

    def get_app_client_by_creator_and_name(
        self,
        creator_address: str,
        app_name: str,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> AgriUsdCoinClient:
        """Get an app client by creator address and name"""
        return AgriUsdCoinClient(
            self.app_factory.get_app_client_by_creator_and_name(
                creator_address,
                app_name,
                default_sender,
                default_signer,
                ignore_cache,
                app_lookup_cache,
                approval_source_map,
                clear_source_map,
            )
        )

    def get_app_client_by_id(
        self,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> AgriUsdCoinClient:
        """Get an app client by app ID"""
        return AgriUsdCoinClient(
            self.app_factory.get_app_client_by_id(
                app_id,
                app_name,
                default_sender,
                default_signer,
                approval_source_map,
                clear_source_map,
            )
        )


class AgriUsdCoinFactoryParams:
    """Parameters for creating transactions for AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = AgriUsdCoinFactoryCreateParams(app_factory)
        self.update = AgriUsdCoinFactoryUpdateParams(app_factory)
        self.delete = AgriUsdCoinFactoryDeleteParams(app_factory)

class AgriUsdCoinFactoryCreateParams:
    """Parameters for 'create' operations of AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateParams:
        """Creates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            compilation_params=compilation_params)

    def create(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the create()byte[] ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "create()byte[]",
                "args": None,
                }
            ),
            compilation_params=compilation_params
        )

    def get_asset_id(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the get_asset_id()uint64 ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "get_asset_id()uint64",
                "args": None,
                }
            ),
            compilation_params=compilation_params
        )

    def get_minted_tokens(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the get_minted_tokens()uint64 ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "get_minted_tokens()uint64",
                "args": None,
                }
            ),
            compilation_params=compilation_params
        )

    def get_burnt_tokens(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the get_burnt_tokens()uint64 ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "get_burnt_tokens()uint64",
                "args": None,
                }
            ),
            compilation_params=compilation_params
        )

    def mint_tokens(
        self,
        args: tuple[int, str | bytes] | MintTokensArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the mint_tokens(uint64,account)byte[] ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "mint_tokens(uint64,account)byte[]",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def burn_tokens(
        self,
        args: tuple[int, str | bytes] | BurnTokensArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the burn_tokens(uint64,account)byte[] ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "burn_tokens(uint64,account)byte[]",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

    def transfer_tokens(
        self,
        args: tuple[int, str | bytes, str | bytes] | TransferTokensArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the transfer_tokens(uint64,account,account)byte[] ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "transfer_tokens(uint64,account,account)byte[]",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

class AgriUsdCoinFactoryUpdateParams:
    """Parameters for 'update' operations of AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppUpdateParams:
        """Updates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_update(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )

class AgriUsdCoinFactoryDeleteParams:
    """Parameters for 'delete' operations of AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppDeleteParams:
        """Deletes an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_delete(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )


class AgriUsdCoinFactoryCreateTransaction:
    """Create transactions for AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = AgriUsdCoinFactoryCreateTransactionCreate(app_factory)


class AgriUsdCoinFactoryCreateTransactionCreate:
    """Create new instances of AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
    ) -> Transaction:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.create_transaction.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
        )


class AgriUsdCoinFactorySend:
    """Send calls to AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = AgriUsdCoinFactorySendCreate(app_factory)


class AgriUsdCoinFactorySendCreate:
    """Send create calls to AgriUsdCoin contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ) -> tuple[AgriUsdCoinClient, algokit_utils.SendAppCreateTransactionResult]:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        result = self.app_factory.send.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            send_params=send_params,
            compilation_params=compilation_params
        )
        return AgriUsdCoinClient(result[0]), result[1]


class AgriUsdCoinComposer:
    """Composer for creating transaction groups for AgriUsdCoin contract calls"""

    def __init__(self, client: "AgriUsdCoinClient"):
        self.client = client
        self._composer = client.algorand.new_group()
        self._result_mappers: list[typing.Callable[[algokit_utils.ABIReturn | None], object] | None] = []

    def create(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_app_call_method_call(
            self.client.params.create(
                
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "create()byte[]", v
            )
        )
        return self

    def get_asset_id(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_app_call_method_call(
            self.client.params.get_asset_id(
                
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "get_asset_id()uint64", v
            )
        )
        return self

    def get_minted_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_app_call_method_call(
            self.client.params.get_minted_tokens(
                
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "get_minted_tokens()uint64", v
            )
        )
        return self

    def get_burnt_tokens(
        self,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_app_call_method_call(
            self.client.params.get_burnt_tokens(
                
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "get_burnt_tokens()uint64", v
            )
        )
        return self

    def mint_tokens(
        self,
        args: tuple[int, str | bytes] | MintTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_app_call_method_call(
            self.client.params.mint_tokens(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "mint_tokens(uint64,account)byte[]", v
            )
        )
        return self

    def burn_tokens(
        self,
        args: tuple[int, str | bytes] | BurnTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_app_call_method_call(
            self.client.params.burn_tokens(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "burn_tokens(uint64,account)byte[]", v
            )
        )
        return self

    def transfer_tokens(
        self,
        args: tuple[int, str | bytes, str | bytes] | TransferTokensArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_app_call_method_call(
            self.client.params.transfer_tokens(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "transfer_tokens(uint64,account,account)byte[]", v
            )
        )
        return self

    def clear_state(
        self,
        *,
        args: list[bytes] | None = None,
        params: algokit_utils.CommonAppCallParams | None = None,
    ) -> "AgriUsdCoinComposer":
        params=params or algokit_utils.CommonAppCallParams()
        self._composer.add_app_call(
            self.client.params.clear_state(
                algokit_utils.AppClientBareCallParams(
                    **{
                        **dataclasses.asdict(params),
                        "args": args
                    }
                )
            )
        )
        return self
    
    def add_transaction(
        self, txn: Transaction, signer: TransactionSigner | None = None
    ) -> "AgriUsdCoinComposer":
        self._composer.add_transaction(txn, signer)
        return self
    
    def composer(self) -> algokit_utils.TransactionComposer:
        return self._composer
    
    def simulate(
        self,
        allow_more_logs: bool | None = None,
        allow_empty_signatures: bool | None = None,
        allow_unnamed_resources: bool | None = None,
        extra_opcode_budget: int | None = None,
        exec_trace_config: SimulateTraceConfig | None = None,
        simulation_round: int | None = None,
        skip_signatures: bool | None = None,
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.simulate(
            allow_more_logs=allow_more_logs,
            allow_empty_signatures=allow_empty_signatures,
            allow_unnamed_resources=allow_unnamed_resources,
            extra_opcode_budget=extra_opcode_budget,
            exec_trace_config=exec_trace_config,
            simulation_round=simulation_round,
            skip_signatures=skip_signatures,
        )
    
    def send(
        self,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.send(send_params)
