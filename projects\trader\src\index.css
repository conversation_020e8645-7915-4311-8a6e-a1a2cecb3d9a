@tailwind base;
@tailwind components;
@tailwind utilities;

/* Updated modal styles to use new color scheme */
.modal-content {
  @apply bg-alt-bg text-primary-text border border-border-primary;
}

/* Updated scrollbar styles to use new color scheme */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-main-bg;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-button-bg rounded;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-button-bg opacity-80;
}
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
