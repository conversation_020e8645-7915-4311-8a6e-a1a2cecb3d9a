/* eslint-disable */
/**
 * This file was automatically generated by @algorandfoundation/algokit-client-generator.
 * DO NOT MODIFY IT BY HAND.
 * requires: @algorandfoundation/algokit-utils: ^7
 */
import { type AlgorandClient } from '@algorandfoundation/algokit-utils/types/algorand-client'
import { ABIReturn, AppReturn, SendAppTransactionResult } from '@algorandfoundation/algokit-utils/types/app'
import { Arc56Contract, getArc56ReturnValue, getABIStructFromABITuple } from '@algorandfoundation/algokit-utils/types/app-arc56'
import {
  AppClient as _AppClient,
  AppClientMethodCallParams,
  AppClientParams,
  AppClientBareCallParams,
  CallOnComplete,
  AppClientCompilationParams,
  ResolveAppClientByCreatorAndName,
  ResolveAppClientByNetwork,
  CloneAppClientParams,
} from '@algorandfoundation/algokit-utils/types/app-client'
import { AppFactory as _AppFactory, AppFactoryAppClientParams, AppFactoryResolveAppClientByCreatorAndNameParams, AppFactoryDeployParams, AppFactoryParams, CreateSchema } from '@algorandfoundation/algokit-utils/types/app-factory'
import { TransactionComposer, AppCallMethodCall, AppMethodCallTransactionArgument, SimulateOptions, RawSimulateOptions, SkipSignaturesSimulateOptions } from '@algorandfoundation/algokit-utils/types/composer'
import { SendParams, SendSingleTransactionResult, SendAtomicTransactionComposerResults } from '@algorandfoundation/algokit-utils/types/transaction'
import { Address, encodeAddress, modelsv2, OnApplicationComplete, Transaction, TransactionSigner } from 'algosdk'
import SimulateResponse = modelsv2.SimulateResponse

export const APP_SPEC: Arc56Contract = {"name":"SupplyChain","structs":{"Coordinates":[{"name":"lat","type":"string"},{"name":"long","type":"string"}],"GrowthPeriod":[{"name":"startDate","type":"string"},{"name":"harvestDate","type":"string"}],"Location":[{"name":"address","type":"string"},{"name":"coordinates","type":"Coordinates"}],"ProductInfo":[{"name":"farmerAddress","type":"address"},{"name":"cost","type":"uint64"},{"name":"cropGreade","type":"string"},{"name":"quantity","type":"uint64"},{"name":"location","type":"Location"},{"name":"growthPeriod","type":"GrowthPeriod"},{"name":"soilType","type":"string"},{"name":"irrigationType","type":"string"},{"name":"fertilizersUsed","type":"string[]"},{"name":"certification","type":"string"}]},"methods":[{"name":"register_crop","args":[{"type":"(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)","struct":"ProductInfo","name":"info","desc":"Detailed information about the crop to register."}],"returns":{"type":"uint64","desc":"The asset ID of the newly created ASA representing the crop."},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"desc":"Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.\nCreates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.","events":[],"recommendations":{}},{"name":"opt_in_asa","args":[{"type":"uint64","name":"product_id"},{"type":"account","name":"framer_address"}],"returns":{"type":"void"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"transfer_asa","args":[{"type":"uint64","name":"product_id"},{"type":"account","name":"framer_address"},{"type":"uint64","name":"quantity"}],"returns":{"type":"void"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"get_product_info","args":[{"type":"uint64","name":"product_id"}],"returns":{"type":"(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)","struct":"ProductInfo"},"actions":{"create":[],"call":["NoOp"]},"readonly":true,"events":[],"recommendations":{}},{"name":"get_product_count","args":[],"returns":{"type":"uint64"},"actions":{"create":[],"call":["NoOp"]},"readonly":true,"desc":"Returns the total number of registered products in the supply chain.","events":[],"recommendations":{}},{"name":"delete_application","args":[],"returns":{"type":"void"},"actions":{"create":[],"call":["DeleteApplication"]},"readonly":false,"desc":"Deletes the application if the transaction sender is the creator.\nThe application can only be deleted by its creator.","events":[],"recommendations":{}},{"name":"update_application","args":[],"returns":{"type":"void"},"actions":{"create":[],"call":["UpdateApplication"]},"readonly":false,"desc":"Allows the application to be updated only by its creator.","events":[],"recommendations":{}}],"arcs":[22,28],"networks":{},"state":{"schema":{"global":{"ints":1,"bytes":0},"local":{"ints":0,"bytes":0}},"keys":{"global":{"product_count":{"keyType":"AVMString","valueType":"AVMUint64","key":"cHJvZHVjdF9jb3VudA=="}},"local":{},"box":{}},"maps":{"global":{},"local":{},"box":{}}},"bareActions":{"create":["NoOp"],"call":[]},"sourceInfo":{"approval":{"sourceInfo":[{"pc":[116],"errorMessage":"OnCompletion is not DeleteApplication"},{"pc":[128,145,165,190,212],"errorMessage":"OnCompletion is not NoOp"},{"pc":[102],"errorMessage":"OnCompletion is not UpdateApplication"},{"pc":[237],"errorMessage":"can only call when creating"},{"pc":[105,119,131,148,168,193,215],"errorMessage":"can only call when not creating"},{"pc":[254,300,369],"errorMessage":"check self.product_count exists"}],"pcOffsetMethod":"none"},"clear":{"sourceInfo":[],"pcOffsetMethod":"none"}},"source":{"approval":"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","clear":"I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"},"byteCode":{"approval":"CiADAQAEJgINcHJvZHVjdF9jb3VudAQVH3x1MRhAAAMoI2cxG0EAvYIHBIE1IPUEUSV6RwQMTMUUBL9s32QESgwWlAQzs0meBLU+JZM2GgCOBwBxAFsAQgAuAB0ADwACI0MxGSQSRDEYRIgBDSJDMRmBBRJEMRhEiAD4IkMxGRREMRhEiADnFilMULAiQzEZFEQxGEQ2GgEXiADJKUxQsCJDMRkURDEYRDYaARc2GgIXwBw2GgOIAJIiQzEZFEQxGEQ2GgEXNhoCF8AciABkIkMxGRREMRhENhoBiAASFilMULAiQzEZQP92MRgURCJDigEBsTIAi/+BKlsjKGVEFjIKRwOyLLIrsiqyKbImgARDUk9QsiUjsiOyIoEDshCyAbO0PEkWi/+/IyhlRCIIKExniYoCALEyACOyEov+shGL/7IUJLIQsgGziYoDALEyAIv/F7ISi/2yEYv+shQkshCyAbOJigEBi/8WvkiJIyhlRIkxADIJEkSJMQAyCRJEiQ==","clear":"CoEBQw=="},"compilerInfo":{"compiler":"puya","compilerVersion":{"major":4,"minor":7,"patch":0}},"events":[],"templateVariables":{}} as unknown as Arc56Contract

/**
 * A state record containing binary data
 */
export interface BinaryState {
  /**
   * Gets the state value as a Uint8Array
   */
  asByteArray(): Uint8Array | undefined
  /**
   * Gets the state value as a string
   */
  asString(): string | undefined
}

class BinaryStateValue implements BinaryState {
  constructor(private value: Uint8Array | undefined) {}

  asByteArray(): Uint8Array | undefined {
    return this.value
  }

  asString(): string | undefined {
    return this.value !== undefined ? Buffer.from(this.value).toString('utf-8') : undefined
  }
}

/**
 * Expands types for IntelliSense so they are more human readable
 * See https://stackoverflow.com/a/69288824
 */
export type Expand<T> = T extends (...args: infer A) => infer R
  ? (...args: Expand<A>) => Expand<R>
  : T extends infer O
    ? { [K in keyof O]: O[K] }
    : never


// Type definitions for ARC-56 structs

export type Coordinates = {
  lat: string,
  long: string
}


/**
 * Converts the ABI tuple representation of a Coordinates to the struct representation
 */
export function CoordinatesFromTuple(abiTuple: [string, string]) {
  return getABIStructFromABITuple(abiTuple, APP_SPEC.structs.Coordinates, APP_SPEC.structs) as Coordinates
}

export type GrowthPeriod = {
  startDate: string,
  harvestDate: string
}


/**
 * Converts the ABI tuple representation of a GrowthPeriod to the struct representation
 */
export function GrowthPeriodFromTuple(abiTuple: [string, string]) {
  return getABIStructFromABITuple(abiTuple, APP_SPEC.structs.GrowthPeriod, APP_SPEC.structs) as GrowthPeriod
}

export type Location = {
  address: string,
  coordinates: Coordinates
}


/**
 * Converts the ABI tuple representation of a Location to the struct representation
 */
export function LocationFromTuple(abiTuple: [string, [string, string]]) {
  return getABIStructFromABITuple(abiTuple, APP_SPEC.structs.Location, APP_SPEC.structs) as Location
}

export type ProductInfo = {
  farmerAddress: string,
  cost: bigint,
  cropGreade: string,
  quantity: bigint,
  location: Location,
  growthPeriod: GrowthPeriod,
  soilType: string,
  irrigationType: string,
  fertilizersUsed: string[],
  certification: string
}


/**
 * Converts the ABI tuple representation of a ProductInfo to the struct representation
 */
export function ProductInfoFromTuple(abiTuple: [string, bigint, string, bigint, [string, [string, string]], [string, string], string, string, string[], string]) {
  return getABIStructFromABITuple(abiTuple, APP_SPEC.structs.ProductInfo, APP_SPEC.structs) as ProductInfo
}

/**
 * The argument types for the SupplyChain contract
 */
export type SupplyChainArgs = {
  /**
   * The object representation of the arguments for each method
   */
  obj: {
    'register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64': {
      /**
       * Detailed information about the crop to register.
       */
      info: ProductInfo
    }
    'opt_in_asa(uint64,account)void': {
      productId: bigint | number
      framerAddress: Uint8Array | string
    }
    'transfer_asa(uint64,account,uint64)void': {
      productId: bigint | number
      framerAddress: Uint8Array | string
      quantity: bigint | number
    }
    'get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)': {
      productId: bigint | number
    }
    'get_product_count()uint64': Record<string, never>
    'delete_application()void': Record<string, never>
    'update_application()void': Record<string, never>
  }
  /**
   * The tuple representation of the arguments for each method
   */
  tuple: {
    'register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64': [info: ProductInfo]
    'opt_in_asa(uint64,account)void': [productId: bigint | number, framerAddress: Uint8Array | string]
    'transfer_asa(uint64,account,uint64)void': [productId: bigint | number, framerAddress: Uint8Array | string, quantity: bigint | number]
    'get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)': [productId: bigint | number]
    'get_product_count()uint64': []
    'delete_application()void': []
    'update_application()void': []
  }
}

/**
 * The return type for each method
 */
export type SupplyChainReturns = {
  'register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64': bigint
  'opt_in_asa(uint64,account)void': void
  'transfer_asa(uint64,account,uint64)void': void
  'get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)': ProductInfo
  'get_product_count()uint64': bigint
  'delete_application()void': void
  'update_application()void': void
}

/**
 * Defines the types of available calls and state of the SupplyChain smart contract.
 */
export type SupplyChainTypes = {
  /**
   * Maps method signatures / names to their argument and return types.
   */
  methods:
    & Record<'register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64' | 'register_crop', {
      argsObj: SupplyChainArgs['obj']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']
      argsTuple: SupplyChainArgs['tuple']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']
      /**
       * The asset ID of the newly created ASA representing the crop.
       */
      returns: SupplyChainReturns['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']
    }>
    & Record<'opt_in_asa(uint64,account)void' | 'opt_in_asa', {
      argsObj: SupplyChainArgs['obj']['opt_in_asa(uint64,account)void']
      argsTuple: SupplyChainArgs['tuple']['opt_in_asa(uint64,account)void']
      returns: SupplyChainReturns['opt_in_asa(uint64,account)void']
    }>
    & Record<'transfer_asa(uint64,account,uint64)void' | 'transfer_asa', {
      argsObj: SupplyChainArgs['obj']['transfer_asa(uint64,account,uint64)void']
      argsTuple: SupplyChainArgs['tuple']['transfer_asa(uint64,account,uint64)void']
      returns: SupplyChainReturns['transfer_asa(uint64,account,uint64)void']
    }>
    & Record<'get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)' | 'get_product_info', {
      argsObj: SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']
      argsTuple: SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']
      returns: SupplyChainReturns['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']
    }>
    & Record<'get_product_count()uint64' | 'get_product_count', {
      argsObj: SupplyChainArgs['obj']['get_product_count()uint64']
      argsTuple: SupplyChainArgs['tuple']['get_product_count()uint64']
      returns: SupplyChainReturns['get_product_count()uint64']
    }>
    & Record<'delete_application()void' | 'delete_application', {
      argsObj: SupplyChainArgs['obj']['delete_application()void']
      argsTuple: SupplyChainArgs['tuple']['delete_application()void']
      returns: SupplyChainReturns['delete_application()void']
    }>
    & Record<'update_application()void' | 'update_application', {
      argsObj: SupplyChainArgs['obj']['update_application()void']
      argsTuple: SupplyChainArgs['tuple']['update_application()void']
      returns: SupplyChainReturns['update_application()void']
    }>
  /**
   * Defines the shape of the state of the application.
   */
  state: {
    global: {
      keys: {
        productCount: bigint
      }
      maps: {}
    }
  }
}

/**
 * Defines the possible abi call signatures.
 */
export type SupplyChainSignatures = keyof SupplyChainTypes['methods']
/**
 * Defines the possible abi call signatures for methods that return a non-void value.
 */
export type SupplyChainNonVoidMethodSignatures = keyof SupplyChainTypes['methods'] extends infer T ? T extends keyof SupplyChainTypes['methods'] ? MethodReturn<T> extends void ? never : T  : never : never
/**
 * Defines an object containing all relevant parameters for a single call to the contract.
 */
export type CallParams<TArgs> = Expand<
  Omit<AppClientMethodCallParams, 'method' | 'args' | 'onComplete'> &
    {
      /** The args for the ABI method call, either as an ordered array or an object */
      args: Expand<TArgs>
    }
>
/**
 * Maps a method signature from the SupplyChain smart contract to the method's arguments in either tuple or struct form
 */
export type MethodArgs<TSignature extends SupplyChainSignatures> = SupplyChainTypes['methods'][TSignature]['argsObj' | 'argsTuple']
/**
 * Maps a method signature from the SupplyChain smart contract to the method's return type
 */
export type MethodReturn<TSignature extends SupplyChainSignatures> = SupplyChainTypes['methods'][TSignature]['returns']

/**
 * Defines the shape of the keyed global state of the application.
 */
export type GlobalKeysState = SupplyChainTypes['state']['global']['keys']


/**
 * Defines supported create method params for this smart contract
 */
export type SupplyChainCreateCallParams =
  | Expand<AppClientBareCallParams & {method?: never} & {onComplete?: OnApplicationComplete.NoOpOC} & CreateSchema>
/**
 * Defines supported update method params for this smart contract
 */
export type SupplyChainUpdateCallParams =
  | Expand<CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & {method: 'update_application'}>
  | Expand<CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & {method: 'update_application()void'}>
/**
 * Defines supported delete method params for this smart contract
 */
export type SupplyChainDeleteCallParams =
  | Expand<CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']> & {method: 'delete_application'}>
  | Expand<CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']> & {method: 'delete_application()void'}>
/**
 * Defines arguments required for the deploy method.
 */
export type SupplyChainDeployParams = Expand<Omit<AppFactoryDeployParams, 'createParams' | 'updateParams' | 'deleteParams'> & {
  /**
   * Create transaction parameters to use if a create needs to be issued as part of deployment; use `method` to define ABI call (if available) or leave out for a bare call (if available)
   */
  createParams?: SupplyChainCreateCallParams
  /**
   * Update transaction parameters to use if a create needs to be issued as part of deployment; use `method` to define ABI call (if available) or leave out for a bare call (if available)
   */
  updateParams?: SupplyChainUpdateCallParams
  /**
   * Delete transaction parameters to use if a create needs to be issued as part of deployment; use `method` to define ABI call (if available) or leave out for a bare call (if available)
   */
  deleteParams?: SupplyChainDeleteCallParams
}>


/**
 * Exposes methods for constructing `AppClient` params objects for ABI calls to the SupplyChain smart contract
 */
export abstract class SupplyChainParamsFactory {
  /**
   * Gets available update ABI call param factories
   */
  static get update() {
    return {
      _resolveByMethod<TParams extends SupplyChainUpdateCallParams & {method: string}>(params: TParams) {
        switch(params.method) {
          case 'update_application':
          case 'update_application()void':
            return SupplyChainParamsFactory.update.updateApplication(params)
        }
        throw new Error(`Unknown ' + verb + ' method`)
      },

      /**
       * Constructs update ABI call params for the SupplyChain smart contract using the update_application()void ABI method
       *
       * @param params Parameters for the call
       * @returns An `AppClientMethodCallParams` object for the call
       */
      updateApplication(params: CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & AppClientCompilationParams): AppClientMethodCallParams & AppClientCompilationParams {
        return {
          ...params,
          method: 'update_application()void' as const,
          args: Array.isArray(params.args) ? params.args : [],
        }
      },
    }
  }

  /**
   * Gets available delete ABI call param factories
   */
  static get delete() {
    return {
      _resolveByMethod<TParams extends SupplyChainDeleteCallParams & {method: string}>(params: TParams) {
        switch(params.method) {
          case 'delete_application':
          case 'delete_application()void':
            return SupplyChainParamsFactory.delete.deleteApplication(params)
        }
        throw new Error(`Unknown ' + verb + ' method`)
      },

      /**
       * Constructs delete ABI call params for the SupplyChain smart contract using the delete_application()void ABI method
       *
       * @param params Parameters for the call
       * @returns An `AppClientMethodCallParams` object for the call
       */
      deleteApplication(params: CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']>): AppClientMethodCallParams {
        return {
          ...params,
          method: 'delete_application()void' as const,
          args: Array.isArray(params.args) ? params.args : [],
        }
      },
    }
  }

  /**
   * Constructs a no op call for the register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64 ABI method
   *
  * Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.
  Creates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.

   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static registerCrop(params: CallParams<SupplyChainArgs['obj']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'] | SupplyChainArgs['tuple']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.info],
    }
  }
  /**
   * Constructs a no op call for the opt_in_asa(uint64,account)void ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static optInAsa(params: CallParams<SupplyChainArgs['obj']['opt_in_asa(uint64,account)void'] | SupplyChainArgs['tuple']['opt_in_asa(uint64,account)void']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'opt_in_asa(uint64,account)void' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.productId, params.args.framerAddress],
    }
  }
  /**
   * Constructs a no op call for the transfer_asa(uint64,account,uint64)void ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static transferAsa(params: CallParams<SupplyChainArgs['obj']['transfer_asa(uint64,account,uint64)void'] | SupplyChainArgs['tuple']['transfer_asa(uint64,account,uint64)void']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'transfer_asa(uint64,account,uint64)void' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.productId, params.args.framerAddress, params.args.quantity],
    }
  }
  /**
   * Constructs a no op call for the get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string) ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static getProductInfo(params: CallParams<SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.productId],
    }
  }
  /**
   * Constructs a no op call for the get_product_count()uint64 ABI method
   *
   * Returns the total number of registered products in the supply chain.
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static getProductCount(params: CallParams<SupplyChainArgs['obj']['get_product_count()uint64'] | SupplyChainArgs['tuple']['get_product_count()uint64']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'get_product_count()uint64' as const,
      args: Array.isArray(params.args) ? params.args : [],
    }
  }
}

/**
 * A factory to create and deploy one or more instance of the SupplyChain smart contract and to create one or more app clients to interact with those (or other) app instances
 */
export class SupplyChainFactory {
  /**
   * The underlying `AppFactory` for when you want to have more flexibility
   */
  public readonly appFactory: _AppFactory

  /**
   * Creates a new instance of `SupplyChainFactory`
   *
   * @param params The parameters to initialise the app factory with
   */
  constructor(params: Omit<AppFactoryParams, 'appSpec'>) {
    this.appFactory = new _AppFactory({
      ...params,
      appSpec: APP_SPEC,
    })
  }
  
  /** The name of the app (from the ARC-32 / ARC-56 app spec or override). */
  public get appName() {
    return this.appFactory.appName
  }
  
  /** The ARC-56 app spec being used */
  get appSpec() {
    return APP_SPEC
  }
  
  /** A reference to the underlying `AlgorandClient` this app factory is using. */
  public get algorand(): AlgorandClient {
    return this.appFactory.algorand
  }
  
  /**
   * Returns a new `AppClient` client for an app instance of the given ID.
   *
   * Automatically populates appName, defaultSender and source maps from the factory
   * if not specified in the params.
   * @param params The parameters to create the app client
   * @returns The `AppClient`
   */
  public getAppClientById(params: AppFactoryAppClientParams) {
    return new SupplyChainClient(this.appFactory.getAppClientById(params))
  }
  
  /**
   * Returns a new `AppClient` client, resolving the app by creator address and name
   * using AlgoKit app deployment semantics (i.e. looking for the app creation transaction note).
   *
   * Automatically populates appName, defaultSender and source maps from the factory
   * if not specified in the params.
   * @param params The parameters to create the app client
   * @returns The `AppClient`
   */
  public async getAppClientByCreatorAndName(
    params: AppFactoryResolveAppClientByCreatorAndNameParams,
  ) {
    return new SupplyChainClient(await this.appFactory.getAppClientByCreatorAndName(params))
  }

  /**
   * Idempotently deploys the SupplyChain smart contract.
   *
   * @param params The arguments for the contract calls and any additional parameters for the call
   * @returns The deployment result
   */
  public async deploy(params: SupplyChainDeployParams = {}) {
    const result = await this.appFactory.deploy({
      ...params,
      updateParams: params.updateParams?.method ? SupplyChainParamsFactory.update._resolveByMethod(params.updateParams) : params.updateParams ? params.updateParams as (SupplyChainUpdateCallParams & { args: Uint8Array[] }) : undefined,
      deleteParams: params.deleteParams?.method ? SupplyChainParamsFactory.delete._resolveByMethod(params.deleteParams) : params.deleteParams ? params.deleteParams as (SupplyChainDeleteCallParams & { args: Uint8Array[] }) : undefined,
    })
    return { result: result.result, appClient: new SupplyChainClient(result.appClient) }
  }

  /**
   * Get parameters to create transactions (create and deploy related calls) for the current app. A good mental model for this is that these parameters represent a deferred transaction creation.
   */
  readonly params = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the SupplyChain smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The params for a create call
       */
      bare: (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        return this.appFactory.params.bare.create(params)
      },
    },

    /**
     * Gets available deployUpdate methods
     */
    deployUpdate: {
      /**
       * Updates an existing instance of the SupplyChain smart contract using the update_application()void ABI method.
       *
       * Allows the application to be updated only by its creator.
       *
       * @param params The params for the smart contract call
       * @returns The deployUpdate params
       */
      updateApplication: (params: CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & AppClientCompilationParams = {args: []}) => {
        return this.appFactory.params.deployUpdate(SupplyChainParamsFactory.update.updateApplication(params))
      },
    },

    /**
     * Gets available deployDelete methods
     */
    deployDelete: {
      /**
       * Deletes an existing instance of the SupplyChain smart contract using the delete_application()void ABI method.
       *
      * Deletes the application if the transaction sender is the creator.
      The application can only be deleted by its creator.

       *
       * @param params The params for the smart contract call
       * @returns The deployDelete params
       */
      deleteApplication: (params: CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']> = {args: []}) => {
        return this.appFactory.params.deployDelete(SupplyChainParamsFactory.delete.deleteApplication(params))
      },
    },

  }

  /**
   * Create transactions for the current app
   */
  readonly createTransaction = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the SupplyChain smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The transaction for a create call
       */
      bare: (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        return this.appFactory.createTransaction.bare.create(params)
      },
    },

  }

  /**
   * Send calls to the current app
   */
  readonly send = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the SupplyChain smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The create result
       */
      bare: async (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        const result = await this.appFactory.send.bare.create(params)
        return { result: result.result, appClient: new SupplyChainClient(result.appClient) }
      },
    },

  }

}
/**
 * A client to make calls to the SupplyChain smart contract
 */
export class SupplyChainClient {
  /**
   * The underlying `AppClient` for when you want to have more flexibility
   */
  public readonly appClient: _AppClient

  /**
   * Creates a new instance of `SupplyChainClient`
   *
   * @param appClient An `AppClient` instance which has been created with the SupplyChain app spec
   */
  constructor(appClient: _AppClient)
  /**
   * Creates a new instance of `SupplyChainClient`
   *
   * @param params The parameters to initialise the app client with
   */
  constructor(params: Omit<AppClientParams, 'appSpec'>)
  constructor(appClientOrParams: _AppClient | Omit<AppClientParams, 'appSpec'>) {
    this.appClient = appClientOrParams instanceof _AppClient ? appClientOrParams : new _AppClient({
      ...appClientOrParams,
      appSpec: APP_SPEC,
    })
  }
  
  /**
   * Checks for decode errors on the given return value and maps the return value to the return type for the given method
   * @returns The typed return value or undefined if there was no value
   */
  decodeReturnValue<TSignature extends SupplyChainNonVoidMethodSignatures>(method: TSignature, returnValue: ABIReturn | undefined) {
    return returnValue !== undefined ? getArc56ReturnValue<MethodReturn<TSignature>>(returnValue, this.appClient.getABIMethod(method), APP_SPEC.structs) : undefined
  }
  
  /**
   * Returns a new `SupplyChainClient` client, resolving the app by creator address and name
   * using AlgoKit app deployment semantics (i.e. looking for the app creation transaction note).
   * @param params The parameters to create the app client
   */
  public static async fromCreatorAndName(params: Omit<ResolveAppClientByCreatorAndName, 'appSpec'>): Promise<SupplyChainClient> {
    return new SupplyChainClient(await _AppClient.fromCreatorAndName({...params, appSpec: APP_SPEC}))
  }
  
  /**
   * Returns an `SupplyChainClient` instance for the current network based on
   * pre-determined network-specific app IDs specified in the ARC-56 app spec.
   *
   * If no IDs are in the app spec or the network isn't recognised, an error is thrown.
   * @param params The parameters to create the app client
   */
  static async fromNetwork(
    params: Omit<ResolveAppClientByNetwork, 'appSpec'>
  ): Promise<SupplyChainClient> {
    return new SupplyChainClient(await _AppClient.fromNetwork({...params, appSpec: APP_SPEC}))
  }
  
  /** The ID of the app instance this client is linked to. */
  public get appId() {
    return this.appClient.appId
  }
  
  /** The app address of the app instance this client is linked to. */
  public get appAddress() {
    return this.appClient.appAddress
  }
  
  /** The name of the app. */
  public get appName() {
    return this.appClient.appName
  }
  
  /** The ARC-56 app spec being used */
  public get appSpec() {
    return this.appClient.appSpec
  }
  
  /** A reference to the underlying `AlgorandClient` this app client is using. */
  public get algorand(): AlgorandClient {
    return this.appClient.algorand
  }

  /**
   * Get parameters to create transactions for the current app. A good mental model for this is that these parameters represent a deferred transaction creation.
   */
  readonly params = {
    /**
     * Gets available update methods
     */
    update: {
      /**
       * Updates an existing instance of the SupplyChain smart contract using the `update_application()void` ABI method.
       *
       * Allows the application to be updated only by its creator.
       *
       * @param params The params for the smart contract call
       * @returns The update params
       */
      updateApplication: (params: CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & AppClientCompilationParams = {args: []}) => {
        return this.appClient.params.update(SupplyChainParamsFactory.update.updateApplication(params))
      },

    },

    /**
     * Gets available delete methods
     */
    delete: {
      /**
       * Deletes an existing instance of the SupplyChain smart contract using the `delete_application()void` ABI method.
       *
      * Deletes the application if the transaction sender is the creator.
      The application can only be deleted by its creator.

       *
       * @param params The params for the smart contract call
       * @returns The delete params
       */
      deleteApplication: (params: CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']> = {args: []}) => {
        return this.appClient.params.delete(SupplyChainParamsFactory.delete.deleteApplication(params))
      },

    },

    /**
     * Makes a clear_state call to an existing instance of the SupplyChain smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams>) => {
      return this.appClient.params.bare.clearState(params)
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64` ABI method.
     *
    * Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.
    Creates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.

     *
     * @param params The params for the smart contract call
     * @returns The call params: The asset ID of the newly created ASA representing the crop.
     */
    registerCrop: (params: CallParams<SupplyChainArgs['obj']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'] | SupplyChainArgs['tuple']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(SupplyChainParamsFactory.registerCrop(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `opt_in_asa(uint64,account)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    optInAsa: (params: CallParams<SupplyChainArgs['obj']['opt_in_asa(uint64,account)void'] | SupplyChainArgs['tuple']['opt_in_asa(uint64,account)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(SupplyChainParamsFactory.optInAsa(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `transfer_asa(uint64,account,uint64)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    transferAsa: (params: CallParams<SupplyChainArgs['obj']['transfer_asa(uint64,account,uint64)void'] | SupplyChainArgs['tuple']['transfer_asa(uint64,account,uint64)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(SupplyChainParamsFactory.transferAsa(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    getProductInfo: (params: CallParams<SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(SupplyChainParamsFactory.getProductInfo(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `get_product_count()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * Returns the total number of registered products in the supply chain.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    getProductCount: (params: CallParams<SupplyChainArgs['obj']['get_product_count()uint64'] | SupplyChainArgs['tuple']['get_product_count()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.params.call(SupplyChainParamsFactory.getProductCount(params))
    },

  }

  /**
   * Create transactions for the current app
   */
  readonly createTransaction = {
    /**
     * Gets available update methods
     */
    update: {
      /**
       * Updates an existing instance of the SupplyChain smart contract using the `update_application()void` ABI method.
       *
       * Allows the application to be updated only by its creator.
       *
       * @param params The params for the smart contract call
       * @returns The update transaction
       */
      updateApplication: (params: CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & AppClientCompilationParams = {args: []}) => {
        return this.appClient.createTransaction.update(SupplyChainParamsFactory.update.updateApplication(params))
      },

    },

    /**
     * Gets available delete methods
     */
    delete: {
      /**
       * Deletes an existing instance of the SupplyChain smart contract using the `delete_application()void` ABI method.
       *
      * Deletes the application if the transaction sender is the creator.
      The application can only be deleted by its creator.

       *
       * @param params The params for the smart contract call
       * @returns The delete transaction
       */
      deleteApplication: (params: CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']> = {args: []}) => {
        return this.appClient.createTransaction.delete(SupplyChainParamsFactory.delete.deleteApplication(params))
      },

    },

    /**
     * Makes a clear_state call to an existing instance of the SupplyChain smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams>) => {
      return this.appClient.createTransaction.bare.clearState(params)
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64` ABI method.
     *
    * Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.
    Creates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.

     *
     * @param params The params for the smart contract call
     * @returns The call transaction: The asset ID of the newly created ASA representing the crop.
     */
    registerCrop: (params: CallParams<SupplyChainArgs['obj']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'] | SupplyChainArgs['tuple']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(SupplyChainParamsFactory.registerCrop(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `opt_in_asa(uint64,account)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    optInAsa: (params: CallParams<SupplyChainArgs['obj']['opt_in_asa(uint64,account)void'] | SupplyChainArgs['tuple']['opt_in_asa(uint64,account)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(SupplyChainParamsFactory.optInAsa(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `transfer_asa(uint64,account,uint64)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    transferAsa: (params: CallParams<SupplyChainArgs['obj']['transfer_asa(uint64,account,uint64)void'] | SupplyChainArgs['tuple']['transfer_asa(uint64,account,uint64)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(SupplyChainParamsFactory.transferAsa(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    getProductInfo: (params: CallParams<SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(SupplyChainParamsFactory.getProductInfo(params))
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `get_product_count()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * Returns the total number of registered products in the supply chain.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    getProductCount: (params: CallParams<SupplyChainArgs['obj']['get_product_count()uint64'] | SupplyChainArgs['tuple']['get_product_count()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.createTransaction.call(SupplyChainParamsFactory.getProductCount(params))
    },

  }

  /**
   * Send calls to the current app
   */
  readonly send = {
    /**
     * Gets available update methods
     */
    update: {
      /**
       * Updates an existing instance of the SupplyChain smart contract using the `update_application()void` ABI method.
       *
       * Allows the application to be updated only by its creator.
       *
       * @param params The params for the smart contract call
       * @returns The update result
       */
      updateApplication: async (params: CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & AppClientCompilationParams & SendParams = {args: []}) => {
        const result = await this.appClient.send.update(SupplyChainParamsFactory.update.updateApplication(params))
        return {...result, return: result.return as unknown as (undefined | SupplyChainReturns['update_application()void'])}
      },

    },

    /**
     * Gets available delete methods
     */
    delete: {
      /**
       * Deletes an existing instance of the SupplyChain smart contract using the `delete_application()void` ABI method.
       *
      * Deletes the application if the transaction sender is the creator.
      The application can only be deleted by its creator.

       *
       * @param params The params for the smart contract call
       * @returns The delete result
       */
      deleteApplication: async (params: CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']> & SendParams = {args: []}) => {
        const result = await this.appClient.send.delete(SupplyChainParamsFactory.delete.deleteApplication(params))
        return {...result, return: result.return as unknown as (undefined | SupplyChainReturns['delete_application()void'])}
      },

    },

    /**
     * Makes a clear_state call to an existing instance of the SupplyChain smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams & SendParams>) => {
      return this.appClient.send.bare.clearState(params)
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64` ABI method.
     *
    * Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.
    Creates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.

     *
     * @param params The params for the smart contract call
     * @returns The call result: The asset ID of the newly created ASA representing the crop.
     */
    registerCrop: async (params: CallParams<SupplyChainArgs['obj']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'] | SupplyChainArgs['tuple']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(SupplyChainParamsFactory.registerCrop(params))
      return {...result, return: result.return as unknown as (undefined | SupplyChainReturns['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'])}
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `opt_in_asa(uint64,account)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    optInAsa: async (params: CallParams<SupplyChainArgs['obj']['opt_in_asa(uint64,account)void'] | SupplyChainArgs['tuple']['opt_in_asa(uint64,account)void']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(SupplyChainParamsFactory.optInAsa(params))
      return {...result, return: result.return as unknown as (undefined | SupplyChainReturns['opt_in_asa(uint64,account)void'])}
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `transfer_asa(uint64,account,uint64)void` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    transferAsa: async (params: CallParams<SupplyChainArgs['obj']['transfer_asa(uint64,account,uint64)void'] | SupplyChainArgs['tuple']['transfer_asa(uint64,account,uint64)void']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(SupplyChainParamsFactory.transferAsa(params))
      return {...result, return: result.return as unknown as (undefined | SupplyChainReturns['transfer_asa(uint64,account,uint64)void'])}
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    getProductInfo: async (params: CallParams<SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(SupplyChainParamsFactory.getProductInfo(params))
      return {...result, return: result.return as unknown as (undefined | SupplyChainReturns['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'])}
    },

    /**
     * Makes a call to the SupplyChain smart contract using the `get_product_count()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * Returns the total number of registered products in the supply chain.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    getProductCount: async (params: CallParams<SupplyChainArgs['obj']['get_product_count()uint64'] | SupplyChainArgs['tuple']['get_product_count()uint64']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      const result = await this.appClient.send.call(SupplyChainParamsFactory.getProductCount(params))
      return {...result, return: result.return as unknown as (undefined | SupplyChainReturns['get_product_count()uint64'])}
    },

  }

  /**
   * Clone this app client with different params
   *
   * @param params The params to use for the the cloned app client. Omit a param to keep the original value. Set a param to override the original value. Setting to undefined will clear the original value.
   * @returns A new app client with the altered params
   */
  public clone(params: CloneAppClientParams) {
    return new SupplyChainClient(this.appClient.clone(params))
  }

  /**
   * Makes a readonly (simulated) call to the SupplyChain smart contract using the `get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)` ABI method.
   * 
   * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
   *
   * @param params The params for the smart contract call
   * @returns The call result
   */
  async getProductInfo(params: CallParams<SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']>) {
    const result = await this.appClient.send.call(SupplyChainParamsFactory.getProductInfo(params))
    return result.return as unknown as SupplyChainReturns['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']
  }

  /**
   * Makes a readonly (simulated) call to the SupplyChain smart contract using the `get_product_count()uint64` ABI method.
   * 
   * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
   *
   * Returns the total number of registered products in the supply chain.
   *
   * @param params The params for the smart contract call
   * @returns The call result
   */
  async getProductCount(params: CallParams<SupplyChainArgs['obj']['get_product_count()uint64'] | SupplyChainArgs['tuple']['get_product_count()uint64']> = {args: []}) {
    const result = await this.appClient.send.call(SupplyChainParamsFactory.getProductCount(params))
    return result.return as unknown as SupplyChainReturns['get_product_count()uint64']
  }

  /**
   * Methods to access state for the current SupplyChain app
   */
  state = {
    /**
     * Methods to access global state for the current SupplyChain app
     */
    global: {
      /**
       * Get all current keyed values from global state
       */
      getAll: async (): Promise<Partial<Expand<GlobalKeysState>>> => {
        const result = await this.appClient.state.global.getAll()
        return {
          productCount: result.product_count,
        }
      },
      /**
       * Get the current value of the product_count key in global state
       */
      productCount: async (): Promise<bigint | undefined> => { return (await this.appClient.state.global.getValue("product_count")) as bigint | undefined },
    },
  }

  public newGroup(): SupplyChainComposer {
    const client = this
    const composer = this.algorand.newGroup()
    let promiseChain:Promise<unknown> = Promise.resolve()
    const resultMappers: Array<undefined | ((x: ABIReturn | undefined) => any)> = []
    return {
      /**
       * Add a register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64 method call against the SupplyChain contract
       */
      registerCrop(params: CallParams<SupplyChainArgs['obj']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'] | SupplyChainArgs['tuple']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.registerCrop(params)))
        resultMappers.push((v) => client.decodeReturnValue('register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64', v))
        return this
      },
      /**
       * Add a opt_in_asa(uint64,account)void method call against the SupplyChain contract
       */
      optInAsa(params: CallParams<SupplyChainArgs['obj']['opt_in_asa(uint64,account)void'] | SupplyChainArgs['tuple']['opt_in_asa(uint64,account)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.optInAsa(params)))
        resultMappers.push(undefined)
        return this
      },
      /**
       * Add a transfer_asa(uint64,account,uint64)void method call against the SupplyChain contract
       */
      transferAsa(params: CallParams<SupplyChainArgs['obj']['transfer_asa(uint64,account,uint64)void'] | SupplyChainArgs['tuple']['transfer_asa(uint64,account,uint64)void']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.transferAsa(params)))
        resultMappers.push(undefined)
        return this
      },
      /**
       * Add a get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string) method call against the SupplyChain contract
       */
      getProductInfo(params: CallParams<SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.getProductInfo(params)))
        resultMappers.push((v) => client.decodeReturnValue('get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)', v))
        return this
      },
      /**
       * Add a get_product_count()uint64 method call against the SupplyChain contract
       */
      getProductCount(params: CallParams<SupplyChainArgs['obj']['get_product_count()uint64'] | SupplyChainArgs['tuple']['get_product_count()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.getProductCount(params)))
        resultMappers.push((v) => client.decodeReturnValue('get_product_count()uint64', v))
        return this
      },
      get update() {
        return {
          updateApplication: (params: CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']> & AppClientCompilationParams) => {
            promiseChain = promiseChain.then(async () => composer.addAppUpdateMethodCall(await client.params.update.updateApplication(params)))
            resultMappers.push(undefined)
            return this
          },
        }
      },
      get delete() {
        return {
          deleteApplication: (params: CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']>) => {
            promiseChain = promiseChain.then(async () => composer.addAppDeleteMethodCall(await client.params.delete.deleteApplication(params)))
            resultMappers.push(undefined)
            return this
          },
        }
      },
      /**
       * Add a clear state call to the SupplyChain contract
       */
      clearState(params: AppClientBareCallParams) {
        promiseChain = promiseChain.then(() => composer.addAppCall(client.params.clearState(params)))
        return this
      },
      addTransaction(txn: Transaction, signer?: TransactionSigner) {
        promiseChain = promiseChain.then(() => composer.addTransaction(txn, signer))
        return this
      },
      async composer() {
        await promiseChain
        return composer
      },
      async simulate(options?: SimulateOptions) {
        await promiseChain
        const result = await (!options ? composer.simulate() : composer.simulate(options))
        return {
          ...result,
          returns: result.returns?.map((val, i) => resultMappers[i] !== undefined ? resultMappers[i]!(val) : val.returnValue)
        }
      },
      async send(params?: SendParams) {
        await promiseChain
        const result = await composer.send(params)
        return {
          ...result,
          returns: result.returns?.map((val, i) => resultMappers[i] !== undefined ? resultMappers[i]!(val) : val.returnValue)
        }
      }
    } as unknown as SupplyChainComposer
  }
}
export type SupplyChainComposer<TReturns extends [...any[]] = []> = {
  /**
   * Calls the register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64 ABI method.
   *
  * Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.
  Creates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.

   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  registerCrop(params?: CallParams<SupplyChainArgs['obj']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'] | SupplyChainArgs['tuple']['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64']>): SupplyChainComposer<[...TReturns, SupplyChainReturns['register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64'] | undefined]>

  /**
   * Calls the opt_in_asa(uint64,account)void ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  optInAsa(params?: CallParams<SupplyChainArgs['obj']['opt_in_asa(uint64,account)void'] | SupplyChainArgs['tuple']['opt_in_asa(uint64,account)void']>): SupplyChainComposer<[...TReturns, SupplyChainReturns['opt_in_asa(uint64,account)void'] | undefined]>

  /**
   * Calls the transfer_asa(uint64,account,uint64)void ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  transferAsa(params?: CallParams<SupplyChainArgs['obj']['transfer_asa(uint64,account,uint64)void'] | SupplyChainArgs['tuple']['transfer_asa(uint64,account,uint64)void']>): SupplyChainComposer<[...TReturns, SupplyChainReturns['transfer_asa(uint64,account,uint64)void'] | undefined]>

  /**
   * Calls the get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string) ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  getProductInfo(params?: CallParams<SupplyChainArgs['obj']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | SupplyChainArgs['tuple']['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)']>): SupplyChainComposer<[...TReturns, SupplyChainReturns['get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)'] | undefined]>

  /**
   * Calls the get_product_count()uint64 ABI method.
   *
   * Returns the total number of registered products in the supply chain.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  getProductCount(params?: CallParams<SupplyChainArgs['obj']['get_product_count()uint64'] | SupplyChainArgs['tuple']['get_product_count()uint64']>): SupplyChainComposer<[...TReturns, SupplyChainReturns['get_product_count()uint64'] | undefined]>

  /**
   * Gets available delete methods
   */
  readonly delete: {
    /**
     * Deletes an existing instance of the SupplyChain smart contract using the delete_application()void ABI method.
     *
     * @param args The arguments for the smart contract call
     * @param params Any additional parameters for the call
     * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
     */
    deleteApplication(params?: CallParams<SupplyChainArgs['obj']['delete_application()void'] | SupplyChainArgs['tuple']['delete_application()void']>): SupplyChainComposer<[...TReturns, SupplyChainReturns['delete_application()void'] | undefined]>
  }

  /**
   * Gets available update methods
   */
  readonly update: {
    /**
     * Updates an existing instance of the SupplyChain smart contract using the update_application()void ABI method.
     *
     * @param args The arguments for the smart contract call
     * @param params Any additional parameters for the call
     * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
     */
    updateApplication(params?: CallParams<SupplyChainArgs['obj']['update_application()void'] | SupplyChainArgs['tuple']['update_application()void']>): SupplyChainComposer<[...TReturns, SupplyChainReturns['update_application()void'] | undefined]>
  }

  /**
   * Makes a clear_state call to an existing instance of the SupplyChain smart contract.
   *
   * @param args The arguments for the bare call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  clearState(params?: AppClientBareCallParams): SupplyChainComposer<[...TReturns, undefined]>

  /**
   * Adds a transaction to the composer
   *
   * @param txn A transaction to add to the transaction group
   * @param signer The optional signer to use when signing this transaction.
   */
  addTransaction(txn: Transaction, signer?: TransactionSigner): SupplyChainComposer<TReturns>
  /**
   * Returns the underlying AtomicTransactionComposer instance
   */
  composer(): Promise<TransactionComposer>
  /**
   * Simulates the transaction group and returns the result
   */
  simulate(): Promise<SupplyChainComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  simulate(options: SkipSignaturesSimulateOptions): Promise<SupplyChainComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  simulate(options: RawSimulateOptions): Promise<SupplyChainComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  /**
   * Sends the transaction group to the network and returns the results
   */
  send(params?: SendParams): Promise<SupplyChainComposerResults<TReturns>>
}
export type SupplyChainComposerResults<TReturns extends [...any[]]> = Expand<SendAtomicTransactionComposerResults & {
  returns: TReturns
}>

