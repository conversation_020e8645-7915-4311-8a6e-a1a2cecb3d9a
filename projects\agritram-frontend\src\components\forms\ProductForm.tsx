import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ProductFormValues } from '../../types';

interface ProductFormProps {
  onSubmit: (values: ProductFormValues) => void;
  onCancel: () => void;
}

const categories = ['Grains', 'Coffee', 'Tea', 'Spices', 'Fruits', 'Vegetables', 'Nuts', 'Herbs'];

const ProductForm: React.FC<ProductFormProps> = ({ onSubmit, onCancel }) => {
  const [values, setValues] = useState<ProductFormValues>({
    name: '',
    category: 'Grains',
    quantity: 0,
    available: 0,
    price: 0,
    description: '',
  });

  const [errors, setErrors] = useState<Partial<Record<keyof ProductFormValues, string>>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // For number fields, convert string to number
    if (['quantity', 'available', 'price'].includes(name)) {
      const numValue = parseFloat(value);
      setValues(prev => ({ ...prev, [name]: isNaN(numValue) ? 0 : numValue }));
    } else {
      setValues(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error when field is edited
    if (errors[name as keyof ProductFormValues]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof ProductFormValues, string>> = {};
    
    if (!values.name.trim()) {
      newErrors.name = 'Product name is required';
    }
    
    if (values.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }
    
    if (values.available < 0) {
      newErrors.available = 'Available quantity cannot be negative';
    }
    
    if (values.available > values.quantity) {
      newErrors.available = 'Available cannot exceed total quantity';
    }
    
    if (values.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(values);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-5">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
          Product Name
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={values.name}
          onChange={handleChange}
          className={`w-full px-3 py-2 border ${
            errors.name ? 'border-error-500' : 'border-neutral-300'
          } rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500`}
          placeholder="Enter product name"
        />
        {errors.name && (
          <p className="mt-1 text-sm text-error-500">{errors.name}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="category" className="block text-sm font-medium text-neutral-700 mb-1">
          Category
        </label>
        <select
          id="category"
          name="category"
          value={values.category}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-neutral-700 mb-1">
            Total Quantity (kg)
          </label>
          <input
            type="number"
            id="quantity"
            name="quantity"
            value={values.quantity}
            onChange={handleChange}
            min="0"
            step="0.01"
            className={`w-full px-3 py-2 border ${
              errors.quantity ? 'border-error-500' : 'border-neutral-300'
            } rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500`}
          />
          {errors.quantity && (
            <p className="mt-1 text-sm text-error-500">{errors.quantity}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="available" className="block text-sm font-medium text-neutral-700 mb-1">
            Available Quantity (kg)
          </label>
          <input
            type="number"
            id="available"
            name="available"
            value={values.available}
            onChange={handleChange}
            min="0"
            max={values.quantity}
            step="0.01"
            className={`w-full px-3 py-2 border ${
              errors.available ? 'border-error-500' : 'border-neutral-300'
            } rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500`}
          />
          {errors.available && (
            <p className="mt-1 text-sm text-error-500">{errors.available}</p>
          )}
        </div>
      </div>
      
      <div>
        <label htmlFor="price" className="block text-sm font-medium text-neutral-700 mb-1">
          Price per kg ($)
        </label>
        <input
          type="number"
          id="price"
          name="price"
          value={values.price}
          onChange={handleChange}
          min="0"
          step="0.01"
          className={`w-full px-3 py-2 border ${
            errors.price ? 'border-error-500' : 'border-neutral-300'
          } rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500`}
        />
        {errors.price && (
          <p className="mt-1 text-sm text-error-500">{errors.price}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
          Description (Optional)
        </label>
        <textarea
          id="description"
          name="description"
          value={values.description}
          onChange={handleChange}
          rows={3}
          className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="Enter product description"
        />
      </div>
      
      <div className="flex justify-end space-x-3 pt-2">
        <motion.button
          type="button"
          onClick={onCancel}
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="px-4 py-2 text-sm font-medium text-neutral-700 bg-neutral-100 rounded-lg hover:bg-neutral-200 transition-colors"
        >
          Cancel
        </motion.button>
        
        <motion.button
          type="submit"
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors"
        >
          Add Product
        </motion.button>
      </div>
    </form>
  );
};

export default ProductForm;