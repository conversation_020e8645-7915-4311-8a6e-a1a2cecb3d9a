{"name": "SupplyChain", "structs": {"Coordinates": [{"name": "lat", "type": "string"}, {"name": "long", "type": "string"}], "GrowthPeriod": [{"name": "startDate", "type": "string"}, {"name": "harvestDate", "type": "string"}], "Location": [{"name": "address", "type": "string"}, {"name": "coordinates", "type": "Coordinates"}], "ProductInfo": [{"name": "farmer_address", "type": "address"}, {"name": "cost", "type": "uint64"}, {"name": "crop_greade", "type": "string"}, {"name": "quantity", "type": "uint64"}, {"name": "location", "type": "Location"}, {"name": "growth_period", "type": "GrowthPeriod"}, {"name": "soil_type", "type": "string"}, {"name": "irrigation_type", "type": "string"}, {"name": "fertilizers_used", "type": "string[]"}, {"name": "certification", "type": "string"}]}, "methods": [{"name": "register_crop", "args": [{"type": "(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)", "struct": "ProductInfo", "name": "info", "desc": "Detailed information about the crop to register."}], "returns": {"type": "uint64", "desc": "The asset ID of the newly created ASA representing the crop."}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": false, "desc": "Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.\nCreates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.", "events": [], "recommendations": {}}, {"name": "opt_in_asa", "args": [{"type": "uint64", "name": "product_id"}, {"type": "account", "name": "framer_address"}], "returns": {"type": "void"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": false, "events": [], "recommendations": {}}, {"name": "transfer_asa", "args": [{"type": "uint64", "name": "product_id"}, {"type": "account", "name": "framer_address"}, {"type": "uint64", "name": "quantity"}], "returns": {"type": "void"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": false, "events": [], "recommendations": {}}, {"name": "get_product_info", "args": [{"type": "uint64", "name": "product_id"}], "returns": {"type": "(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)", "struct": "ProductInfo"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": true, "events": [], "recommendations": {}}, {"name": "get_product_count", "args": [], "returns": {"type": "uint64"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": true, "desc": "Returns the total number of registered products in the supply chain.", "events": [], "recommendations": {}}, {"name": "delete_application", "args": [], "returns": {"type": "void"}, "actions": {"create": [], "call": ["DeleteApplication"]}, "readonly": false, "desc": "Deletes the application if the transaction sender is the creator.\nThe application can only be deleted by its creator.", "events": [], "recommendations": {}}, {"name": "update_application", "args": [], "returns": {"type": "void"}, "actions": {"create": [], "call": ["UpdateApplication"]}, "readonly": false, "desc": "Allows the application to be updated only by its creator.", "events": [], "recommendations": {}}], "arcs": [22, 28], "networks": {}, "state": {"schema": {"global": {"ints": 1, "bytes": 0}, "local": {"ints": 0, "bytes": 0}}, "keys": {"global": {"product_count": {"keyType": "AVMString", "valueType": "AVMUint64", "key": "cHJvZHVjdF9jb3VudA=="}}, "local": {}, "box": {}}, "maps": {"global": {}, "local": {}, "box": {}}}, "bareActions": {"create": ["NoOp"], "call": []}, "sourceInfo": {"approval": {"sourceInfo": [{"pc": [116], "errorMessage": "OnCompletion is not DeleteApplication"}, {"pc": [128, 145, 165, 190, 212], "errorMessage": "OnCompletion is not NoOp"}, {"pc": [102], "errorMessage": "OnCompletion is not UpdateApplication"}, {"pc": [237], "errorMessage": "can only call when creating"}, {"pc": [105, 119, 131, 148, 168, 193, 215], "errorMessage": "can only call when not creating"}, {"pc": [254, 300, 369], "errorMessage": "check self.product_count exists"}], "pcOffsetMethod": "none"}, "clear": {"sourceInfo": [], "pcOffsetMethod": "none"}}, "source": {"approval": "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", "clear": "I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"}, "byteCode": {"approval": "CiADAQAEJgINcHJvZHVjdF9jb3VudAQVH3x1MRhAAAMoI2cxG0EAvYIHBIE1IPUEUSV6RwQMTMUUBL9s32QESgwWlAQzs0meBLU+JZM2GgCOBwBxAFsAQgAuAB0ADwACI0MxGSQSRDEYRIgBDSJDMRmBBRJEMRhEiAD4IkMxGRREMRhEiADnFilMULAiQzEZFEQxGEQ2GgEXiADJKUxQsCJDMRkURDEYRDYaARc2GgIXwBw2GgOIAJIiQzEZFEQxGEQ2GgEXNhoCF8AciABkIkMxGRREMRhENhoBiAASFilMULAiQzEZQP92MRgURCJDigEBsTIAi/+BKlsjKGVEFjIKRwOyLLIrsiqyKbImgARDUk9QsiUjsiOyIoEDshCyAbO0PEkWi/+/IyhlRCIIKExniYoCALEyACOyEov+shGL/7IUJLIQsgGziYoDALEyAIv/F7ISi/2yEYv+shQkshCyAbOJigEBi/8WvkiJIyhlRIkxADIJEkSJMQAyCRJEiQ==", "clear": "CoEBQw=="}, "compilerInfo": {"compiler": "puya", "compilerVersion": {"major": 4, "minor": 7, "patch": 0}}, "events": [], "templateVariables": {}}