# Generated by Django 5.2 on 2025-04-25 11:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("crops", "0003_alter_croptransfer_unique_together_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name="croptransfer",
            name="crop",
        ),
        migrations.RemoveField(
            model_name="croptransfer",
            name="from_user",
        ),
        migrations.RemoveField(
            model_name="croptransfer",
            name="to_user",
        ),
        migrations.AddField(
            model_name="croptransfer",
            name="crop",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="transfers",
                to="crops.crops",
            ),
        ),
        migrations.AddField(
            model_name="croptransfer",
            name="from_user",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sent_transactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="croptransfer",
            name="to_user",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="received_transactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
