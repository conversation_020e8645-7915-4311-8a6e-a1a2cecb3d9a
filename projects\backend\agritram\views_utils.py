from django.shortcuts import get_object_or_404
from rest_framework import status


def list_create_view(request, Model, Serializer):
    if request.method == "GET":
        instances = Model.objects.all()
        serializer = Serializer(instances, many=True)
        return serializer.data, status.HTTP_200_OK
    elif request.method == "POST":
        serializer = Serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return serializer.data, status.HTTP_201_CREATED
        return serializer.errors, status.HTTP_400_BAD_REQUEST


# Reusable function for retrieve, update, and delete


def retrieve_update_delete_view(request, pk, Model, Serializer):
    instance = get_object_or_404(Model, pk=pk)
    if request.method == "GET":
        serializer = Serializer(instance)
        return serializer.data, status.HTTP_200_OK
    elif request.method == "PUT":
        serializer = Serializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return serializer.data, status.HTTP_200_OK
        return serializer.errors, status.HTTP_400_BAD_REQUEST
    elif request.method == "DELETE":
        instance.delete()
        return None, status.HTTP_204_NO_CONTENT
