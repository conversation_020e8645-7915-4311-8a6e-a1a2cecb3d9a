import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

interface DialogCustomProps {
  asaBalance: number
  showBuyModal: boolean
  setShowBuyModal: (value: boolean) => void
}

export default function DialogCustom({ asaBalance, showBuyModal, setShowBuyModal }: DialogCustomProps) {
  const [tokenAmount, setTokenAmount] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [estimatedPrice, setEstimatedPrice] = useState(0)
  const navigate = useNavigate()

  const handleBuyTokens = async () => {
    if (!tokenAmount || parseFloat(tokenAmount) <= 0) {
      setError('Please enter a valid token amount')
      return
    } else {
      setShowBuyModal(false)
      navigate('/buy', {
        state: {
          tokenAmount: estimatedPrice.toString(),
        },
      })
    }
  }

  const TOKEN_PRICE = 1

  useEffect(() => {
    // Calculate estimated price whenever token amount changes
    const price = parseFloat(tokenAmount) * TOKEN_PRICE || 0
    setEstimatedPrice(price)
  }, [tokenAmount])

  return (
    <Dialog open={showBuyModal} onOpenChange={setShowBuyModal}>
      <DialogContent className="sm:max-w-[425px] modal-content custom-scrollbar custom-scrollbar">
        <DialogHeader>
          <DialogTitle className="text-[#FFFFFF] text-xl font-bold">Buy KCT Tokens</DialogTitle>
          <DialogDescription className="text-[#BDBDBD]">Enter the amount of tokens you want to purchase.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="tokenAmount" className="text-right text-[#FFFFFF]">
              Amount
            </Label>
            <Input
              id="tokenAmount"
              type="number"
              value={tokenAmount}
              onChange={(e) => {
                setTokenAmount(e.target.value)
                setError('')
              }}
              className="col-span-3 modal-input"
              placeholder="Enter token amount"
              min="0"
              disabled={isLoading}
            />
          </div>

          {/* Estimated Price Display */}
          <div className="text-[#BDBDBD] text-sm">Estimated Price: ${estimatedPrice.toFixed(2)} USD</div>

          {/* Balance Display */}
          <div className="text-[#BDBDBD] text-sm">Current Balance: {asaBalance.toFixed(2)} KCT</div>

          {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        </div>

        <DialogFooter className="gap-3">
          <Button
            variant="outline"
            onClick={() => {
              setShowBuyModal(false)
              setTokenAmount('')
              setError('')
            }}
            disabled={isLoading}
            className="bg-[#BDBDBD] py-2 rounded-lg font-semibold
                 hover:bg-[#9E9E9E] transition-colors duration-300"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleBuyTokens}
            disabled={isLoading}
            className="bg-[#00E676] hover:bg-[#00E676]/80 text-[#212121] font-semibold"
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-[#212121]"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </div>
            ) : (
              'Confirm Purchase'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
