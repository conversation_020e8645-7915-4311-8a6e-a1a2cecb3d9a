repos:
  - repo: local
    hooks:
      
      - id: black
        name: black
        description: "Black: The uncompromising Python code formatter"
        entry: poetry run black
        language: system
        minimum_pre_commit_version: 2.9.2
        require_serial: true
        types_or: [ python, pyi ]
      
      
      - id: ruff
        name: ruff
        description: "Run 'ruff' for extremely fast Python linting"
        entry: poetry run ruff
        language: system
        types: [ python ]
        args: [ "check", "--fix" ]
        require_serial: false
        additional_dependencies: [ ]
        minimum_pre_commit_version: '0'
        files: '^(smart_contracts|tests)/'
      
      
      - id: mypy
        name: mypy
        description: '`mypy` will check Python types for correctness'
        entry: poetry run mypy
        language: system
        types_or: [ python, pyi ]
        require_serial: true
        additional_dependencies: [ ]
        minimum_pre_commit_version: '2.9.2'
        files: '^(smart_contracts|tests)/'
      
      # # Uncomment to enable TEAL static analysis using Tealer package
      # - id: tealer
      #   name: tealer
      #   description: "Run AlgoKit `Tealer` for TEAL static analysis"
      #   entry: algokit
      #   language: system
      #   args: [project, run, "audit-teal"]
      #   require_serial: false
      #   additional_dependencies: []
      #   minimum_pre_commit_version: "0"
      #   files: '^.*\.teal$'
