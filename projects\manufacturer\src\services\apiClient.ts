import { useAuthStore } from '@/stores/authStore'
import { VITE_BACKEND_URL } from '@/utils/variable'
import axios, { AxiosRequestConfig, CanceledError } from 'axios'

const backendURL = VITE_BACKEND_URL

if (!backendURL) {
  throw new Error('VITE_BACKEND_URL is not defined in environment variables.')
}

const apiClient = axios.create({
  baseURL: backendURL,
  headers: {
    'Content-Type': 'application/json',
  },
})

apiClient.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Token ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      useAuthStore.getState().logout()
      window.location.href = '/login'
    }
    return Promise.reject(error)
  },
)

export default apiClient
export type { AxiosRequestConfig, CanceledError }
