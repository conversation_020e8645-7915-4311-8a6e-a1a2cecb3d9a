import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Sprout, ArrowRight, Users, Factory, Wallet } from 'lucide-react';
import { useAuthStore } from '../stores/authStore';

const Login = () => {
  const navigate = useNavigate();
  const { login } = useAuthStore();
  const [userType, setUserType] = useState<'buyer' | 'seller' | null>(null);
  const [userName, setUserName] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [error, setError] = useState('');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userType) {
      setError('Please select a user type');
      return;
    }
    
    if (!userName.trim()) {
      setError('Please enter your name');
      return;
    }
    
    if (!walletAddress.trim()) {
      setError('Please enter a wallet address');
      return;
    }
    
    login(userType, userName, walletAddress);
    navigate(userType === 'buyer' ? '/dashboard' : '/seller/dashboard');
  };
  
  // Generate mock wallet address
  const generateWalletAddress = () => {
    const chars = '0123456789abcdef';
    let address = '0x';
    for (let i = 0; i < 40; i++) {
      address += chars[Math.floor(Math.random() * chars.length)];
    }
    setWalletAddress(address);
  };
  
  return (
    <div className="flex min-h-screen bg-neutral-50">
      <div className="w-full lg:w-1/2 flex flex-col justify-center p-8 sm:p-12 lg:p-16">
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Sprout className="w-8 h-8 text-primary-500" />
            <span className="text-2xl font-bold text-neutral-900">AgriTram</span>
          </div>
          <h1 className="text-3xl font-bold text-neutral-900">Welcome to AgriTram</h1>
          <p className="mt-2 text-neutral-600">
            The secure blockchain platform for agricultural trade and escrow services.
          </p>
        </div>
        
        <motion.form 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6" 
          onSubmit={handleSubmit}
        >
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Select User Type
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={() => setUserType('buyer')}
                className={`flex items-center p-4 border rounded-lg text-left transition-colors ${
                  userType === 'buyer'
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-neutral-200 hover:border-primary-200 hover:bg-primary-50'
                }`}
              >
                <Users className="w-5 h-5 mr-3" />
                <div>
                  <p className="font-medium">Buyer</p>
                  <p className="text-sm text-neutral-500">Purchase agricultural goods</p>
                </div>
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={() => setUserType('seller')}
                className={`flex items-center p-4 border rounded-lg text-left transition-colors ${
                  userType === 'seller'
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-neutral-200 hover:border-primary-200 hover:bg-primary-50'
                }`}
              >
                <Factory className="w-5 h-5 mr-3" />
                <div>
                  <p className="font-medium">Seller</p>
                  <p className="text-sm text-neutral-500">Sell agricultural products</p>
                </div>
              </motion.button>
            </div>
          </div>
          
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-2">
              Your Name
            </label>
            <input
              id="name"
              type="text"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              className="w-full px-4 py-3 rounded-lg border border-neutral-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter your name"
            />
          </div>
          
          <div>
            <label htmlFor="wallet" className="block text-sm font-medium text-neutral-700 mb-2">
              Wallet Address
            </label>
            <div className="flex">
              <input
                id="wallet"
                type="text"
                value={walletAddress}
                onChange={(e) => setWalletAddress(e.target.value)}
                className="flex-1 px-4 py-3 rounded-l-lg border border-neutral-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter your wallet address"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="button"
                onClick={generateWalletAddress}
                className="px-4 py-3 rounded-r-lg bg-neutral-100 text-neutral-700 border border-l-0 border-neutral-200 hover:bg-neutral-200"
              >
                <Wallet className="w-5 h-5" />
              </motion.button>
            </div>
          </div>
          
          {error && (
            <p className="text-sm text-error-600 bg-error-50 p-3 rounded-lg">
              {error}
            </p>
          )}
          
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            className="flex items-center justify-center w-full px-6 py-3 text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors"
          >
            <span className="mr-2">Access Dashboard</span>
            <ArrowRight className="w-5 h-5" />
          </motion.button>
        </motion.form>
      </div>
      
      <div className="hidden lg:block lg:w-1/2 bg-primary-600">
        <div className="flex flex-col justify-center h-full p-16 text-white">
          <h2 className="text-4xl font-bold mb-6">Transforming Agricultural Trade</h2>
          <ul className="space-y-4">
            <li className="flex items-start">
              <div className="mr-4 p-2 bg-white/10 rounded-full">
                <Wallet className="w-5 h-5" />
              </div>
              <div>
                <p className="font-medium">Secure Escrow</p>
                <p className="text-white/80">Funds securely locked in smart contracts</p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-4 p-2 bg-white/10 rounded-full">
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
              </div>
              <div>
                <p className="font-medium">Blockchain Verified</p>
                <p className="text-white/80">Immutable record of all transactions</p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-4 p-2 bg-white/10 rounded-full">
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </div>
              <div>
                <p className="font-medium">Multi-signature Approval</p>
                <p className="text-white/80">Require consensus for fund release</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Login;