import { CreditCard, Globe } from 'lucide-react'
import React from 'react'

interface PaymentMethodSelectionProps {
  selectedMethod: 'card' | 'google-pay' | 'apple-pay' | 'mobile-money'
  setSelectedMethod: (method: 'card' | 'google-pay' | 'apple-pay' | 'mobile-money') => void
}

const PaymentMethodSelection: React.FC<PaymentMethodSelectionProps> = ({ selectedMethod, setSelectedMethod }) => {
  return (
    <div className="bg-alt-bg p-6 rounded-lg mb-4 shadow-md">
      <h2 className="text-xl font-semibold text-primary-text mb-4">Payment Method</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <button
          type="button"
          onClick={() => setSelectedMethod('card')}
          className={`p-4 rounded-lg border ${
            selectedMethod === 'card'
              ? 'bg-button-bg text-button-text'
              : 'border-border-primary text-primary-text hover:bg-button-bg-hover hover:text-button-text'
          } transition-colors`}
        >
          <div className="flex items-center">
            <CreditCard className="h-6 w-6 mr-3" />
            <span>Credit/Debit Card</span>
          </div>
        </button>

        {/* Show Google Pay on Android */}
        {/Android/i.test(navigator.userAgent) && (
          <button
            type="button"
            onClick={() => setSelectedMethod('google-pay')}
            className={`p-4 rounded-lg border ${
              selectedMethod === 'google-pay'
                ? 'border-border-primary bg-button-primary bg-opacity-20'
                : 'border-border-primary hover:border-white'
            } transition-colors`}
          >
            <div className="flex items-center">
              <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google Pay" className="h-6 w-6 mr-3" />
              <span className="text-white">Google Pay</span>
            </div>
          </button>
        )}

        {/* Show Apple Pay on iOS */}
        {/iPhone|iPad|iPod/i.test(navigator.userAgent) && (
          <button
            type="button"
            onClick={() => setSelectedMethod('apple-pay')}
            className={`p-4 rounded-lg border ${
              selectedMethod === 'apple-pay'
                ? 'border-border-primary bg-button-bg text-button-text'
                : 'border-border-primary hover:border-border-primary'
            } transition-colors`}
          >
            <div className="flex items-center">
              <img
                src="https://developer.apple.com/design/human-interface-guidelines/foundations/branding/images/apple-pay-logo.png"
                alt="Apple Pay"
                className="h-6 w-6 mr-3"
              />
              <span className="text-white">Apple Pay</span>
            </div>
          </button>
        )}

        <button
          type="button"
          onClick={() => setSelectedMethod('mobile-money')}
          className={`p-4 rounded-lg border ${
            selectedMethod === 'mobile-money'
              ? 'bg-button-bg text-button-text'
              : 'border-border-primary text-primary-text hover:bg-button-bg-hover hover:text-button-text'
          } transition-colors`}
        >
          <div className="flex items-center">
            <Globe className="h-6 w-6 mr-3" />
            <span>Mobile Money</span>
          </div>
        </button>
      </div>
    </div>
  )
}

export default PaymentMethodSelection
