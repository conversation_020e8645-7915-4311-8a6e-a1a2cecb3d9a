import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Package, Plus, Search, Wheat, Coffee, BrainIcon as GrainIcon, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import AppLayout from '../../components/layout/AppLayout';
import Modal from '../../components/ui/Modal';
import ProductForm from '../../components/forms/ProductForm';
import { InventoryItem, ProductFormValues } from '../../types';
import { getCategoryIcon } from '../../utils/iconUtils';

// Mock inventory data for initial state if localStorage is empty
const initialInventory: InventoryItem[] = [
  { 
    id: 1, 
    name: 'Organic Wheat', 
    description: 'Premium quality organic wheat', 
    quantity: 2500, 
    unit: 'kg', 
    available: 2000,
    price: 25,
    images: ['wheat.jpg'],
    category: 'Grains',
    icon: <Wheat className="w-5 h-5" />
  },
  { 
    id: 2, 
    name: 'Organic Rice', 
    description: 'Premium quality organic rice', 
    quantity: 1500, 
    unit: 'kg', 
    available: 1200,
    price: 30,
    images: ['rice.jpg'],
    category: 'Grains',
    icon: <GrainIcon className="w-5 h-5" />
  },
  { 
    id: 3, 
    name: 'Premium Coffee Beans', 
    description: 'Premium quality coffee beans', 
    quantity: 800, 
    unit: 'kg', 
    available: 700,
    price: 75,
    images: ['coffee.jpg'],
    category: 'Coffee',
    icon: <Coffee className="w-5 h-5" />
  },
];

const STORAGE_KEY = 'inventoryItems';

const Inventory = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>(() => {
    const savedInventory = localStorage.getItem(STORAGE_KEY);
    if (savedInventory) {
      const parsedInventory = JSON.parse(savedInventory);
      // Reattach React elements (icons) after parsing
      return parsedInventory.map((item: InventoryItem) => ({
        ...item,
        icon: getCategoryIcon(item.category)
      }));
    }
    return initialInventory;
  });
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Save to localStorage whenever inventory changes
  useEffect(() => {
    // Remove React elements before storing
    const inventoryForStorage = inventory.map(item => {
      const { icon, ...rest } = item;
      return rest;
    });
    localStorage.setItem(STORAGE_KEY, JSON.stringify(inventoryForStorage));
  }, [inventory]);
  
  // Filter inventory based on search query and category
  const filteredInventory = inventory.filter((item) => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });
  
  // Get unique categories
  const categories = ['All', ...Array.from(new Set(inventory.map(item => item.category)))];
  
  // Handle adding a new product
  const handleAddProduct = (values: ProductFormValues) => {
    const newProduct: InventoryItem = {
      id: Math.max(0, ...inventory.map(item => item.id)) + 1,
      name: values.name,
      description: values.description || `${values.name} product`,
      quantity: values.quantity,
      unit: 'kg',
      available: values.available,
      price: values.price,
      images: [],
      category: values.category,
      icon: getCategoryIcon(values.category)
    };
    
    setInventory(prev => [...prev, newProduct]);
    setIsAddModalOpen(false);
  };

  return (
    <AppLayout title="Inventory Management">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search inventory..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-full rounded-lg border border-neutral-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setIsAddModalOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add Product</span>
        </motion.button>
      </div>
      
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <motion.button
            key={category}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setSelectedCategory(category)}
            className={`px-3 py-1 text-sm font-medium rounded-lg ${
              selectedCategory === category
                ? 'bg-primary-500 text-white'
                : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
            }`}
          >
            {category}
          </motion.button>
        ))}
      </div>
      
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b border-neutral-200">
          <h2 className="text-lg font-semibold text-neutral-900">Product Inventory</h2>
        </div>
        
        {filteredInventory.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-neutral-200">
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Product</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Category</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Total Quantity</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Available</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Price</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredInventory.map((item) => (
                  <tr
                    key={item.id}
                    className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors"
                  >
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-primary-50 text-primary-600">
                          {item.icon}
                        </div>
                        <div>
                          <p className="font-medium text-neutral-900">{item.name}</p>
                          <p className="text-xs text-neutral-500">{item.description}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <p className="text-sm text-neutral-900">{item.category}</p>
                    </td>
                    <td className="py-4 px-4">
                      <p className="text-sm font-medium text-neutral-900">{item.quantity} {item.unit}</p>
                    </td>
                    <td className="py-4 px-4">
                      <p className="text-sm font-medium text-primary-600">{item.available} {item.unit}</p>
                    </td>
                    <td className="py-4 px-4">
                      <p className="text-sm font-medium text-neutral-900">${item.price}/{item.unit}</p>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-1 text-neutral-500 hover:text-primary-600"
                        >
                          <Edit className="w-4 h-4" />
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-1 text-neutral-500 hover:text-error-600"
                        >
                          <Trash2 className="w-4 h-4" />
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-1 text-neutral-500 hover:text-neutral-700"
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </motion.button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="py-12 text-center">
            <Package className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
            <p className="text-lg font-medium text-neutral-900 mb-2">No products found</p>
            <p className="text-neutral-500 max-w-md mx-auto">
              Try adjusting your search or filter to find what you're looking for.
            </p>
          </div>
        )}
      </div>
      
      {/* Add Product Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New Product"
      >
        <ProductForm
          onSubmit={handleAddProduct}
          onCancel={() => setIsAddModalOpen(false)}
        />
      </Modal>
    </AppLayout>
  );
};

export default Inventory;