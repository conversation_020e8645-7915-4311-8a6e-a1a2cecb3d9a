import DialogCustom from '@/components/dashboard/DialogCustom'
import { Header } from '@/components/Header'
import { MetricCard } from '@/components/MetricCard'
import { NewsFeed } from '@/components/NewsFeed'
import { Token<PERSON>hart } from '@/components/TokenChart'
import { But<PERSON> } from '@/components/ui/button'
import apiClient from '@/services/apiClient'
import { VITE_ALGORAND_ASSET_ID_KCT, VITE_ALGORAND_ASSET_ID_KTT, VITE_ALGORAND_DECIMAL } from '@/utils/variable'
import { useWallet } from '@txnlab/use-wallet-react'
import algosdk from 'algosdk'
import { Coins, DollarSign, Flame, History, Leaf } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { getAlgodConfigFromViteEnvironment } from '../utils/network/getAlgoClientConfigs'
const DashboardLayout = () => {
  const navigate = useNavigate()
  const { activeAddress } = useWallet()
  const [kttBalance, setKttBalance] = useState(0)
  const [kctBalance, setKctBalance] = useState(0)
  const [totalBalance, setTotalBalance] = useState(0)
  const [showBuyModal, setShowBuyModal] = useState(false)
  const [kttBurnBalance, setKttBurnBalance] = useState(0)

  const kttId = parseInt(VITE_ALGORAND_ASSET_ID_KTT)
  const kctId = parseInt(VITE_ALGORAND_ASSET_ID_KCT)

  useEffect(() => {
    const fetchAsaBalance = async () => {
      if (!activeAddress || !kttId) {
        console.warn('Active address or asset ID is missing.')
        return
      }

      try {
        const algodConfig = getAlgodConfigFromViteEnvironment()
        const algod = new algosdk.Algodv2(algodConfig.token as string, algodConfig.server, algodConfig.port)
        const accountInfo = await algod.accountInformation(activeAddress).do()
        const assets = accountInfo.assets ?? []
        const ktt = assets.find((a) => a.assetId === BigInt(kttId))
        const kct = assets.find((a) => a.assetId === BigInt(kctId))

        setKttBalance(ktt ? Number(ktt.amount) / Math.pow(10, VITE_ALGORAND_DECIMAL || 0) : 0)
        setKctBalance(kct ? Number(kct.amount) / Math.pow(10, VITE_ALGORAND_DECIMAL || 0) : 0)
      } catch (error) {
        console.error('Error fetching ASA balance:', error)
        setKttBalance(0)
      }
    }

    fetchAsaBalance()
  }, [activeAddress, kttId, kctId])

  useEffect(() => {
    const fetchKttBurn = async () => {
      if (!activeAddress || !kttId) {
        console.warn('Active address or asset ID is missing.')
        return
      }

      try {
        const response = await apiClient.get('/transaction/burn-tokens/')
        console.log(response.data)

        if (response.status === 200) {
          setKttBurnBalance(parseFloat(response.data['total_amount_stablecoin_ktt']))
        } else {
          throw new Error('Transaction failed.')
        }
      } catch (error) {
        console.error('Error fetching KTT burn balance:', error)
        setKttBurnBalance(0)
      }
    }

    fetchKttBurn()
  }, [activeAddress, kttId, kttBurnBalance])

  useEffect(() => {
    setTotalBalance(kttBalance + kctBalance * 1.1)
  }, [kttBalance, kctBalance])

  return (
    <div className="min-h-screen bg-[#212121]">
      <Header totalBalance={totalBalance} />

      <main className="container mx-auto px-4 py-8">
        <DialogCustom asaBalance={kttBalance} showBuyModal={showBuyModal} setShowBuyModal={() => setShowBuyModal(false)} />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <TokenChart />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4 mb-8 animate-fade-in">
            <MetricCard
              title="KTT Tokens"
              value={kttBalance.toFixed(2)}
              isPositive={true}
              className="w-full p-4 transition duration-300 text-center flex flex-col items-center justify-center bg-[#303030] text-[#FFFFFF]"
            />
            <MetricCard
              title="KCT"
              value={kctBalance.toFixed(2)}
              isPositive={false}
              className="w-full p-4 transition duration-300 text-center flex flex-col items-center justify-center bg-[#303030] text-[#FFFFFF]"
            />
            <MetricCard
              title="KTT Tokens Burned"
              value={kttBurnBalance.toString()}
              className="w-full p-4 transition duration-300 text-center flex flex-col items-center justify-center bg-[#303030] text-[#FFFFFF]"
            />
            <MetricCard
              title="Total Balance"
              value={`$ ${totalBalance.toFixed(2)}`}
              className="w-full p-4 transition duration-300 text-center flex flex-col items-center justify-center bg-[#303030] text-[#FFFFFF]"
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-8">
          <Button className="bg-[#4CAF50] hover:bg-[#66BB6A] text-[#FFFFFF] font-medium" onClick={() => navigate('/pledge')}>
            <Leaf className="mr-2 h-4 w-4" />
            Pledge Crops
          </Button>
          <Button
            className="bg-[#FF9800] hover:bg-[#FFA726] text-[#FFFFFF] font-medium"
            onClick={() =>
              navigate('/convert', {
                state: {
                  kttBalance: kttBalance,
                  kctBalance: kctBalance,
                },
              })
            }
          >
            <DollarSign className="mr-2 h-4 w-4" />
            Convert to Investment
          </Button>
          <Button
            className="bg-[#F44336] hover:bg-[#E57373] text-[#FFFFFF] font-medium"
            onClick={() =>
              navigate('/sell', {
                state: {
                  kttBalance: kttBalance,
                  kctBalance: kctBalance,
                },
              })
            }
          >
            <DollarSign className="mr-2 h-4 w-4" />
            Sell Investment
          </Button>
          <Button
            className="bg-[#F44336] hover:bg-[#E57373] text-[#FFFFFF] font-medium"
            onClick={() =>
              navigate('/burn', {
                state: {
                  asaBalance: kttBalance,
                },
              })
            }
          >
            <Flame className="mr-2 h-4 w-4" />
            Burn Tokens
          </Button>
          <Button className="bg-[#00E676] hover:bg-[#00E676]/80 text-[#212121] font-medium" onClick={() => setShowBuyModal(true)}>
            <Coins className="mr-2 h-4 w-4" />
            Buy Tokens
          </Button>
          <Button
            className="bg-[#FF9800] hover:bg-[#FFA726] text-[#FFFFFF] font-medium"
            onClick={() =>
              navigate('/transfer', {
                state: {
                  kttBalance: kttBalance,
                },
              })
            }
          >
            <DollarSign className="mr-2 h-4 w-4" />
            Transfer KTT
          </Button>
          <Button
            variant="outline"
            className="text-[#FFFFFF] border-[#FFFFFF] hover:bg-[#64B5F6] hover:text-[#212121] bg-[#1976D2]"
            onClick={() => navigate('/transactions')}
          >
            <History className="mr-2 h-4 w-4" />
            View All Transactions
          </Button>
        </div>

        <NewsFeed />
      </main>
    </div>
  )
}

export default DashboardLayout
