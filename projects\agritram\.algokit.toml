[algokit]
min_version = "v2.0.0"

[generate.smart-contract]
description = "Generate a new smart contract for existing project"
path = ".algokit/generators/create_contract"

[generate.env-file]
description = "Generate a new generic or Algorand network specific .env file"
path = ".algokit/generators/create_env_file"

[project]
type = 'contract'
name = 'agritram'
artifacts = 'smart_contracts/artifacts'

[project.deploy]
command = "poetry run python -m smart_contracts deploy"
environment_secrets = ["DEPLOYER_MNEMONIC", "DISPENSER_MNEMONIC"]

[project.deploy.localnet]
environment_secrets = []

[project.run]
# Commands intented for use locally and in CI
build = { commands = [
  'poetry run python -m smart_contracts build',
], description = 'Build all smart contracts in the project' }
test = { commands = [
  'poetry run pytest -v -s --log-cli-level=INFO',
], description = 'Run smart contract tests' }
audit = { commands = [
  'poetry run pip-audit',
], description = 'Audit with pip-audit. NOTE: If used with poetry >v2, make sure to install `poetry-plugin-export` as per https://github.com/python-poetry/poetry-plugin-export#installation.' }
lint = { commands = [
  'poetry run black --check --diff .',
  'poetry run ruff check .',
  'poetry run mypy',
], description = 'Perform linting' }
audit-teal = { commands = [
  # 🚨 IMPORTANT 🚨: For strict TEAL validation, remove --exclude statements. The default starter contract is not for production. Ensure thorough testing and adherence to best practices in smart contract development. This is not a replacement for a professional audit.
  'algokit task analyze smart_contracts/artifacts --recursive --force --exclude rekey-to --exclude is-updatable --exclude missing-fee-check --exclude is-deletable --exclude can-close-asset --exclude can-close-account --exclude unprotected-deletable --exclude unprotected-updatable',
], description = 'Audit TEAL files' }

# Commands intented for CI only, prefixed with `ci-` by convention
ci-teal-diff = { commands = [
  'git add -N ./smart_contracts/artifacts',
  'git diff --exit-code --minimal ./smart_contracts/artifacts',
], description = 'Check TEAL files for differences' }
