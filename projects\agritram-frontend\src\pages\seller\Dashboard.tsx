import { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Package, Truck, ArrowRightLeft, Calendar, ChevronRight, Wheat } from 'lucide-react';
import AppLayout from '../../components/layout/AppLayout';
import StatCard from '../../components/dashboard/StatCard';
import { useTransactionStore, Transaction } from '../../stores/transactionStore';
import { useNavigate } from 'react-router-dom';

// Mock data for seller
const mockInventory = [
  { id: 1, name: 'Organic Wheat', quantity: 2500, unit: 'kg', available: 2000 },
  { id: 2, name: 'Organic Rice', quantity: 1500, unit: 'kg', available: 1200 },
  { id: 3, name: 'Premium Coffee Beans', quantity: 800, unit: 'kg', available: 700 },
];

const mockPendingShipments = [
  { id: 'ship-1', productName: 'Organic Wheat', quantity: 500, dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), transactionId: 'tx-1' },
  { id: 'ship-2', productName: 'Organic Rice', quantity: 300, dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), transactionId: 'tx-2' },
];

const SellerDashboard = () => {
  const navigate = useNavigate();
  const { transactions, fetchTransactions } = useTransactionStore();
  
  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);
  
  // Get active transactions
  const activeTransactions = transactions.filter(tx => tx.status === 'active');
  
  // Calculate total inventory
  const totalInventory = mockInventory.reduce((sum, item) => sum + item.quantity, 0);
  
  // Calculate total pending shipments
  const totalPendingShipments = mockPendingShipments.length;
  
  // Calculate total funds awaiting release
  const totalAwaitingRelease = transactions.reduce((sum, tx) => {
    // Count funds that are verified but not released
    const verifiedMilestones = tx.milestones.filter(m => m.status === 'verified');
    return sum + verifiedMilestones.reduce((mSum, m) => mSum + m.amount, 0);
  }, 0);
  
  // Calculate total funds received
  const totalReceived = transactions.reduce((sum, tx) => sum + tx.releaseAmount, 0);
  
  // Navigate to transaction details
  const handleTransactionClick = (transaction: Transaction) => {
    navigate(`/transactions?id=${transaction.id}`);
  };
  
  // Navigate to inventory
  const handleViewInventory = () => {
    navigate('/inventory');
  };
  
  // Navigate to shipments
  const handleViewShipments = () => {
    navigate('/shipments');
  };
  
  return (
    <AppLayout title="Seller Dashboard">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Inventory"
          value={`${totalInventory} kg`}
          icon={<Package className="w-5 h-5" />}
          trend={{ value: 5.2, isPositive: true }}
        />
        <StatCard
          title="Pending Shipments"
          value={totalPendingShipments}
          icon={<Truck className="w-5 h-5" />}
          trend={{ value: 3.1, isPositive: false }}
        />
        <StatCard
          title="Awaiting Release"
          value={`$${totalAwaitingRelease.toLocaleString()}`}
          icon={<Calendar className="w-5 h-5" />}
          trend={{ value: 7.4, isPositive: true }}
        />
        <StatCard
          title="Total Received"
          value={`$${totalReceived.toLocaleString()}`}
          icon={<ArrowRightLeft className="w-5 h-5" />}
          trend={{ value: 12.5, isPositive: true }}
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-xl shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-neutral-900">Inventory Overview</h2>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleViewInventory}
              className="text-sm font-medium text-primary-600 hover:text-primary-700"
            >
              View all
            </motion.button>
          </div>
          
          {mockInventory.length > 0 ? (
            <div className="space-y-4">
              {mockInventory.map((item) => (
                <motion.div
                  key={item.id}
                  whileHover={{ x: 4 }}
                  className="p-4 border border-neutral-100 rounded-lg"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-primary-50 text-primary-600">
                        <Wheat className="w-4 h-4" />
                      </div>
                      <div>
                        <p className="font-medium text-neutral-900">{item.name}</p>
                        <p className="text-sm text-neutral-500">{item.quantity} {item.unit}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-neutral-900">Available</p>
                      <p className="text-sm text-primary-600">{item.available} {item.unit}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-neutral-500">
              <p>No inventory items</p>
            </div>
          )}
        </div>
        
        <div className="bg-white p-6 rounded-xl shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-neutral-900">Pending Shipments</h2>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleViewShipments}
              className="text-sm font-medium text-primary-600 hover:text-primary-700"
            >
              View all
            </motion.button>
          </div>
          
          {mockPendingShipments.length > 0 ? (
            <div className="space-y-4">
              {mockPendingShipments.map((shipment) => (
                <motion.div
                  key={shipment.id}
                  whileHover={{ x: 4 }}
                  className="p-4 border border-neutral-100 rounded-lg"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-neutral-900">{shipment.productName}</p>
                      <p className="text-sm text-neutral-500">{shipment.quantity} units</p>
                    </div>
                    <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-secondary-100 text-secondary-700">
                      <Calendar className="w-3 h-3" />
                      <span className="text-xs font-medium">
                        Due: {shipment.dueDate.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex justify-between items-center">
                    <p className="text-xs text-neutral-500">
                      Transaction ID: {shipment.transactionId}
                    </p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-1 text-xs font-medium text-primary-600 hover:text-primary-700"
                    >
                      <span>Mark as Shipped</span>
                      <ChevronRight className="w-3 h-3" />
                    </motion.button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-neutral-500">
              <p>No pending shipments</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-xl shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold text-neutral-900">Active Transactions</h2>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/transactions')}
            className="text-sm font-medium text-primary-600 hover:text-primary-700"
          >
            View all
          </motion.button>
        </div>
        
        {activeTransactions.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-neutral-200">
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Product</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Buyer</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Amount</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Released</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Current Milestone</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {activeTransactions.map((transaction) => {
                  // Find current milestone
                  const currentMilestone = transaction.milestones.find(m => m.status === 'pending');
                  
                  return (
                    <tr
                      key={transaction.id}
                      className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors"
                    >
                      <td className="py-4 px-4">
                        <div>
                          <p className="font-medium text-neutral-900">{transaction.productName}</p>
                          <p className="text-xs text-neutral-500">{transaction.quantity} units</p>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <p className="text-sm text-neutral-900 font-mono">
                          {transaction.buyerAddress.slice(0, 6)}...{transaction.buyerAddress.slice(-4)}
                        </p>
                      </td>
                      <td className="py-4 px-4">
                        <p className="font-medium text-neutral-900">${transaction.totalAmount.toLocaleString()}</p>
                      </td>
                      <td className="py-4 px-4">
                        <p className="font-medium text-success-700">${transaction.releaseAmount.toLocaleString()}</p>
                      </td>
                      <td className="py-4 px-4">
                        {currentMilestone ? (
                          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-primary-100 text-primary-700">
                            <span className="text-xs font-medium">{currentMilestone.name}</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-success-100 text-success-700">
                            <span className="text-xs font-medium">Completed</span>
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handleTransactionClick(transaction)}
                          className="text-sm font-medium text-primary-600 hover:text-primary-700"
                        >
                          View
                        </motion.button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="py-8 text-center text-neutral-500">
            <p>No active transactions</p>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default SellerDashboard;