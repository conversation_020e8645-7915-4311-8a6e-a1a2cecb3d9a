import { Arrow<PERSON>ownU<PERSON>, Clock, CreditCard, ExternalLink, Cast as Gas, Hash, User } from 'lucide-react'
import React from 'react'

interface TransactionData {
  transaction_id: string
  algorand_tx_id: string
  from_address: string
  to_address: string
  amount_usd: string
  amount_stablecoin_ktt: string
  amount_stablecoin_kct: string
  transaction_type: string
  status: string
  timestamp: string
  gas_fee: string
}

const TransactionDetails: React.FC = () => {
  const transaction: TransactionData = {
    transaction_id: 'transaction_1742208398',
    algorand_tx_id: 'VBT6EV3YD5KLJVBXVSWCRRWSVTLQRPGHYX5WXFSNKV6HMZPVN5YQ',
    from_address: '5NC5PPDOUOUQ72TXKFSG2ZGT2W47HLQZTFB2T2DZD7XPIWZCBKIZF4LREI',
    to_address: '5NC5PPDOUOUQ72TXKFSG2ZGT2W47HLQZTFB2T2DZD7XPIWZCBKIZF4LR<PERSON>',
    amount_usd: '0.100000',
    amount_stablecoin_ktt: '0.100000',
    amount_stablecoin_kct: '0.000000',
    transaction_type: 'BURN',
    status: 'COMPLETED',
    timestamp: '2025-03-17 10:46:38',
    gas_fee: '0.001000',
  }

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-6)}`
  }

  return (
    <div className="min-h-screen bg-[#212121] text-white p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 flex items-center gap-2">
          <Hash className="text-[#00E676]" />
          Transaction Details
        </h1>

        <div className="bg-[#303030] rounded-xl p-6 shadow-lg">
          {/* Status Banner */}
          <div className="bg-[#1B5E20] rounded-lg p-4 mb-6 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ArrowDownUp className="text-[#00E676]" />
              <span className="font-semibold">{transaction.transaction_type}</span>
            </div>
            <span className="bg-[#00E676] text-[#212121] px-4 py-1 rounded-full font-medium">{transaction.status}</span>
          </div>

          {/* Transaction Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-[#BDBDBD] text-sm">Transaction ID</label>
                <p className="font-mono bg-[#212121] p-2 rounded mt-1">{transaction.transaction_id}</p>
              </div>

              <div>
                <label className="text-[#BDBDBD] text-sm">Algorand Transaction ID</label>
                <p className="font-mono bg-[#212121] p-2 rounded mt-1 flex items-center justify-between">
                  {truncateAddress(transaction.algorand_tx_id)}
                  <ExternalLink size={16} className="text-[#00E676]" />
                </p>
              </div>

              <div>
                <label className="text-[#BDBDBD] text-sm flex items-center gap-2">
                  <User size={16} />
                  From Address
                </label>
                <p className="font-mono bg-[#212121] p-2 rounded mt-1 flex items-center justify-between">
                  {truncateAddress(transaction.from_address)}
                  <ExternalLink size={16} className="text-[#00E676]" />
                </p>
              </div>

              <div>
                <label className="text-[#BDBDBD] text-sm flex items-center gap-2">
                  <User size={16} />
                  To Address
                </label>
                <p className="font-mono bg-[#212121] p-2 rounded mt-1 flex items-center justify-between">
                  {truncateAddress(transaction.to_address)}
                  <ExternalLink size={16} className="text-[#00E676]" />
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-[#BDBDBD] text-sm flex items-center gap-2">
                  <CreditCard size={16} />
                  Amount (USD)
                </label>
                <p className="text-xl font-bold text-[#00E676]">${transaction.amount_usd}</p>
              </div>

              <div>
                <label className="text-[#BDBDBD] text-sm">KTT Amount</label>
                <p className="font-semibold">{transaction.amount_stablecoin_ktt} KTT</p>
              </div>

              <div>
                <label className="text-[#BDBDBD] text-sm">KCT Amount</label>
                <p className="font-semibold">{transaction.amount_stablecoin_kct} KCT</p>
              </div>

              <div>
                <label className="text-[#BDBDBD] text-sm flex items-center gap-2">
                  <Gas size={16} />
                  Gas Fee
                </label>
                <p className="font-semibold">{transaction.gas_fee} ALGO</p>
              </div>

              <div>
                <label className="text-[#BDBDBD] text-sm flex items-center gap-2">
                  <Clock size={16} />
                  Timestamp
                </label>
                <p className="font-semibold">{transaction.timestamp}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TransactionDetails
