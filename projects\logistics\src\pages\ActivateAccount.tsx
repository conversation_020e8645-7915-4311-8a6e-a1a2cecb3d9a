import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import AuthLayout from "@/components/AuthLayout";
import apiClient from "@/services/apiClient";
import axios from "axios";

const ActivateAccount = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { uid, token } = useParams();
  const [isActivating, setIsActivating] = useState(true);

  useEffect(() => {
    const activateAccount = async () => {
      try {
        console.log("Activating account with params:", uid, token);
        await apiClient.post("/user/activate-account/", { uid, token });
        toast({
          title: "Account Activated!",
          description:
            "Your account has been successfully activated. Redirecting to login...",
        });
        setIsActivating(false);
        setTimeout(() => {
          navigate("/login");
        }, 2000);
      } catch (error) {
        console.error("Account activation error:", error);
        if (axios.isAxiosError(error)) {
          toast({
            title: "Activation Failed",
            description:
              error.response?.data.error?.[0] || "Failed to activate account",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Activation Failed",
            description: "Failed to activate account",
            variant: "destructive",
          });
        }
        setIsActivating(false);
      }
    };

    if (uid && token) {
      activateAccount();
    }
  }, [uid, token, navigate, toast]);

  return (
    <AuthLayout
      title="Account Activation"
      subtitle={
        isActivating
          ? "Please wait while we activate your account..."
          : "Account activation failed"
      }
    >
      <div className="flex flex-col items-center justify-center space-y-4">
        {isActivating ? (
          <div className="animate-pulse text-accent">
            Activating your account...
          </div>
        ) : (
          <button
            onClick={() => navigate("/login")}
            className="w-full py-3 px-4 bg-accent text-accent-foreground rounded-lg font-medium hover:bg-accent/90 transition-colors"
          >
            Return to Login
          </button>
        )}
      </div>
    </AuthLayout>
  );
};

export default ActivateAccount;
