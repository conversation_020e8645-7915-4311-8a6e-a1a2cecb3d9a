#pragma version 10
#pragma typetrack false


main:
    intcblock 0 1 4
    bytecblock "asset" 0x151f7c75 "minted_tokens" "burnt_tokens"
    txn ApplicationID
    bnz main_after_if_else@2


    bytec_2
    intc_0
    app_global_put


    bytec_3
    intc_0
    app_global_put


    bytec_0
    intc_0
    app_global_put

main_after_if_else@2:


    txn NumAppArgs
    bz main_bare_routing@12
    pushbytess 0x8213ade6 0x5ba22a84 0x528f9dcb 0x9a988f95 0x7aca8dfa 0x2d9a0de7 0xc372f979
    txna ApplicationArgs 0
    match main_create_route@5 main_get_asset_id_route@6 main_get_minted_tokens_route@7 main_get_burnt_tokens_route@8 main_mint_tokens_route@9 main_burn_tokens_route@10 main_transfer_tokens_route@11

main_after_if_else@14:


    intc_0
    return

main_transfer_tokens_route@11:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    txna ApplicationArgs 2
    btoi
    txnas Accounts
    txna ApplicationArgs 3
    btoi
    txnas Accounts


    callsub transfer_tokens
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1
    swap
    concat
    log
    intc_1
    return

main_burn_tokens_route@10:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    txna ApplicationArgs 2
    btoi
    txnas Accounts


    callsub burn_tokens
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1
    swap
    concat
    log
    intc_1
    return

main_mint_tokens_route@9:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert


    txna ApplicationArgs 1
    txna ApplicationArgs 2
    btoi
    txnas Accounts


    callsub mint_tokens
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1
    swap
    concat
    log
    intc_1
    return

main_get_burnt_tokens_route@8:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert
    callsub get_burnt_tokens
    itob
    bytec_1
    swap
    concat
    log
    intc_1
    return

main_get_minted_tokens_route@7:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert
    callsub get_minted_tokens
    itob
    bytec_1
    swap
    concat
    log
    intc_1
    return

main_get_asset_id_route@6:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert
    callsub get_asset_id
    itob
    bytec_1
    swap
    concat
    log
    intc_1
    return

main_create_route@5:


    txn OnCompletion
    !
    assert
    txn ApplicationID
    assert
    callsub create
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1
    swap
    concat
    log
    intc_1
    return

main_bare_routing@12:


    txn OnCompletion
    bnz main_after_if_else@14
    txn ApplicationID
    !
    assert
    intc_1
    return



create:



    txn Sender
    global CreatorAddress
    ==
    assert


    intc_0
    bytec_0
    app_global_get_ex
    assert
    !
    assert













    itxn_begin


    global MinTxnFee


    global CurrentApplicationAddress




    dupn 3
    itxn_field ConfigAssetClawback
    itxn_field ConfigAssetFreeze
    itxn_field ConfigAssetReserve
    itxn_field ConfigAssetManager


    pushbytes "AGRI-USD"
    itxn_field ConfigAssetUnitName


    pushbytes "Agri USD Coin"
    itxn_field ConfigAssetName


    pushint 2
    itxn_field ConfigAssetDecimals


    pushint 18446744073709551615
    itxn_field ConfigAssetTotal



    pushint 3
    itxn_field TypeEnum
    itxn_field Fee













    itxn_submit
    itxn TxID



    bytec_0
    itxn CreatedAssetID
    app_global_put


    retsub



get_asset_id:


    intc_0
    bytec_0
    app_global_get_ex
    assert
    retsub



get_minted_tokens:


    intc_0
    bytec_2
    app_global_get_ex
    assert
    retsub



get_burnt_tokens:


    intc_0
    bytec_3
    app_global_get_ex
    assert
    retsub



mint_tokens:







    proto 2 1







    itxn_begin


    global MinTxnFee


    intc_0
    bytec_0
    app_global_get_ex
    assert


    frame_dig -2
    btoi
    dup
    itxn_field AssetAmount
    swap
    itxn_field XferAsset
    frame_dig -1
    itxn_field AssetReceiver


    intc_2
    itxn_field TypeEnum
    swap
    itxn_field Fee







    itxn_submit
    itxn TxID


    intc_0
    bytec_2
    app_global_get_ex
    assert
    uncover 2
    +
    bytec_2
    swap
    app_global_put


    retsub



burn_tokens:







    proto 2 1








    itxn_begin


    global MinTxnFee


    global CurrentApplicationAddress


    intc_0
    bytec_0
    app_global_get_ex
    assert


    frame_dig -2
    btoi
    frame_dig -1
    itxn_field AssetSender
    dup
    itxn_field AssetAmount
    swap
    itxn_field XferAsset
    swap
    itxn_field AssetReceiver


    intc_2
    itxn_field TypeEnum
    swap
    itxn_field Fee








    itxn_submit
    itxn TxID


    intc_0
    bytec_3
    app_global_get_ex
    assert
    uncover 2
    +
    bytec_3
    swap
    app_global_put


    retsub



transfer_tokens:








    proto 3 1








    itxn_begin


    global MinTxnFee


    intc_0
    bytec_0
    app_global_get_ex
    assert


    frame_dig -3
    btoi
    frame_dig -1
    itxn_field AssetSender
    dup
    itxn_field AssetAmount
    swap
    itxn_field XferAsset
    frame_dig -2
    itxn_field AssetReceiver


    intc_2
    itxn_field TypeEnum
    swap
    itxn_field Fee








    itxn_submit
    itxn TxID


    intc_0
    bytec_2
    app_global_get_ex
    assert
    uncover 2
    +
    bytec_2
    swap
    app_global_put


    retsub