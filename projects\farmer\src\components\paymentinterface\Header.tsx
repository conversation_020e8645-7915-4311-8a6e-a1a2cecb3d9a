import { AlertCircle, Lock, Shield } from 'lucide-react'

interface HeaderProps {
  amount: number
  currency: string
  sessionTimeout: number
}

export default function Header({ amount, currency, sessionTimeout }: HeaderProps) {
  return (
    <>
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-primary-text mb-4">Secure Payment</h1>
        <div className="text-4xl font-bold text-secondary-text">
          ${' '}
          {amount.toLocaleString(undefined, {
            style: 'currency',
            currency: currency,
          })}
        </div>
      </div>

      {/* Security Indicators */}
      <div className="flex items-center justify-center gap-4 mb-8">
        <div className="flex items-center text-primary-text">
          <Lock className="h-5 w-5 mr-2 text-secondary-text" />
          <span>SSL Encrypted</span>
        </div>
        <div className="flex items-center text-primary-text">
          <Shield className="h-5 w-5 mr-2 text-secondary-text" />
          <span>PCI DSS Compliant</span>
        </div>
      </div>

      {/* Session Timeout Warning */}
      {sessionTimeout < 60 && (
        <div className="bg-button-danger/20 p-4 rounded-lg mb-6">
          <div className="flex items-center text-button-danger">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>Session expires in {sessionTimeout} seconds</span>
          </div>
        </div>
      )}
    </>
  )
}
