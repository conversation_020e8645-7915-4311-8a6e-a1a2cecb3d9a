import { AlertCircle, Lock, Shield } from 'lucide-react'

interface HeaderProps {
  amount: number
  currency: string
  sessionTimeout: number
}

export default function Header({ amount, currency, sessionTimeout }: HeaderProps) {
  return (
    <>
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-4">Secure Payment</h1>
        <div className="text-4xl font-bold text-[#00E676]">
          {amount.toLocaleString(undefined, {
            style: 'currency',
            currency: currency,
          })}
        </div>
      </div>

      {/* Security Indicators */}
      <div className="flex items-center justify-center gap-4 mb-8">
        <div className="flex items-center text-[#BDBDBD]">
          <Lock className="h-5 w-5 mr-2" />
          <span>SSL Encrypted</span>
        </div>
        <div className="flex items-center text-[#BDBDBD]">
          <Shield className="h-5 w-5 mr-2" />
          <span>PCI DSS Compliant</span>
        </div>
      </div>

      {/* Session Timeout Warning */}
      {sessionTimeout < 60 && (
        <div className="bg-red-900/20 border border-red-500 p-4 rounded-lg mb-6">
          <div className="flex items-center text-red-500">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>Session expires in {sessionTimeout} seconds</span>
          </div>
        </div>
      )}
    </>
  )
}
