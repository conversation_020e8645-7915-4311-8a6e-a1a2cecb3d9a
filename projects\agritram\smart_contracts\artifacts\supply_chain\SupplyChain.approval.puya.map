{"version": 3, "sources": ["../../supply_chain/contract.py"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCQ;AAAqB;AAArB;AAJR;;AAAA;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAAA;;;;;;;;;;;;;;;;AAAA;;AA8FK;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;;;AAAA;;AATA;;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;AAAA;;AAPA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AANA;;AAAA;AAAA;AAAA;;AAAA;AAxEL;;;AAAA;AAwEK;;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAfA;;AAAA;AAAA;AAAA;;AAAA;AAzDL;;;AAAA;AAAA;;;AAAA;AAAA;;AAAA;;;AAyDK;;;AAAA;;AAdA;;AAAA;AAAA;AAAA;;AAAA;AA3CL;;;AAAA;AAAA;;;AAAA;AAAA;;AA2CK;;;AAAA;;AArCA;;AAAA;AAAA;AAAA;;AAAA;AANL;;;AAMK;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AANL;;AAAA;;;;;;;;;AAMA;;;AAiBsB;AASN;;AARG;;AAAA;;AAAA;AAGY;AAAA;AAAA;AAAA;AAAR;AACH;;AACA;;;;;;;;;;;;AAHE;;;;;;;;AADD;;;;;AAFC;;;;;;AAAA;;;AAYd;AAAU;AAEV;;AAAA;AAEqB;AAAA;AAAA;AAAA;AAAqB;AAArB;AAArB;AAAA;AAAA;AAEA;AAER;;;AAMQ;AAIQ;;AADS;;;;;;;;;;;AAHjB;;;;;AAAA;AAMA;AAER;;;AAOQ;AAIQ;;AADS;;AAAA;;;;;;;;;;;AAHjB;;;;;AAAA;AAMA;AAER;;;AAEQ;;AAAU;AACC;AAAA;AACX;AAOO;AAAA;AAAA;AAAA;AAAP;AASO;;AAAc;;AAAd;AAAP;;AAUO;;AAAc;;AAAd;AAAP;", "op_pc_offset": 0, "pc_events": {"1": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.__algopy_entrypoint_with_init", "params": {}, "block": "main", "stack_in": [], "op": "intcblock 1 0 4"}, "6": {"op": "bytecblock \"product_count\" 0x151f7c75"}, "27": {"op": "txn ApplicationID", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "29": {"op": "bnz main_after_if_else@2", "stack_out": []}, "32": {"op": "bytec_0 // \"product_count\"", "defined_out": ["\"product_count\""], "stack_out": ["\"product_count\""]}, "33": {"op": "intc_1 // 0", "defined_out": ["\"product_count\"", "0"], "stack_out": ["\"product_count\"", "0"]}, "34": {"op": "app_global_put", "stack_out": []}, "35": {"block": "main_after_if_else@2", "stack_in": [], "op": "txn NumAppArgs", "defined_out": ["tmp%0#2"], "stack_out": ["tmp%0#2"]}, "37": {"op": "bz main_bare_routing@12", "stack_out": []}, "40": {"op": "pushbytess 0x813520f5 0x51257a47 0x0c4cc514 0xbf6cdf64 0x4a0c1694 0x33b3499e 0xb53e2593 // method \"register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64\", method \"opt_in_asa(uint64,account)void\", method \"transfer_asa(uint64,account,uint64)void\", method \"get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)\", method \"get_product_count()uint64\", method \"delete_application()void\", method \"update_application()void\"", "defined_out": ["Method(delete_application()void)", "Method(get_product_count()uint64)", "Method(get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))", "Method(opt_in_asa(uint64,account)void)", "Method(register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64)", "Method(transfer_asa(uint64,account,uint64)void)", "Method(update_application()void)"], "stack_out": ["Method(register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64)", "Method(opt_in_asa(uint64,account)void)", "Method(transfer_asa(uint64,account,uint64)void)", "Method(get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))", "Method(get_product_count()uint64)", "Method(delete_application()void)", "Method(update_application()void)"]}, "77": {"op": "txna ApplicationArgs 0", "defined_out": ["Method(delete_application()void)", "Method(get_product_count()uint64)", "Method(get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))", "Method(opt_in_asa(uint64,account)void)", "Method(register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64)", "Method(transfer_asa(uint64,account,uint64)void)", "Method(update_application()void)", "tmp%2#0"], "stack_out": ["Method(register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64)", "Method(opt_in_asa(uint64,account)void)", "Method(transfer_asa(uint64,account,uint64)void)", "Method(get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))", "Method(get_product_count()uint64)", "Method(delete_application()void)", "Method(update_application()void)", "tmp%2#0"]}, "80": {"op": "match main_register_crop_route@5 main_opt_in_asa_route@6 main_transfer_asa_route@7 main_get_product_info_route@8 main_get_product_count_route@9 main_delete_application_route@10 main_update_application_route@11", "stack_out": []}, "96": {"block": "main_after_if_else@14", "stack_in": [], "op": "intc_1 // 0", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "97": {"op": "return", "stack_out": []}, "98": {"block": "main_update_application_route@11", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%39#0"], "stack_out": ["tmp%39#0"]}, "100": {"op": "intc_2 // UpdateApplication", "defined_out": ["UpdateApplication", "tmp%39#0"], "stack_out": ["tmp%39#0", "UpdateApplication"]}, "101": {"op": "==", "defined_out": ["tmp%40#0"], "stack_out": ["tmp%40#0"]}, "102": {"error": "OnCompletion is not UpdateApplication", "op": "assert // OnCompletion is not UpdateApplication", "stack_out": []}, "103": {"op": "txn ApplicationID", "defined_out": ["tmp%41#0"], "stack_out": ["tmp%41#0"]}, "105": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "106": {"callsub": "smart_contracts.supply_chain.contract.SupplyChain.update_application", "op": "callsub update_application"}, "109": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "110": {"op": "return", "stack_out": []}, "111": {"block": "main_delete_application_route@10", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%35#0"], "stack_out": ["tmp%35#0"]}, "113": {"op": "pushint 5 // DeleteApplication", "defined_out": ["DeleteApplication", "tmp%35#0"], "stack_out": ["tmp%35#0", "DeleteApplication"]}, "115": {"op": "==", "defined_out": ["tmp%36#0"], "stack_out": ["tmp%36#0"]}, "116": {"error": "OnCompletion is not DeleteApplication", "op": "assert // OnCompletion is not DeleteApplication", "stack_out": []}, "117": {"op": "txn ApplicationID", "defined_out": ["tmp%37#0"], "stack_out": ["tmp%37#0"]}, "119": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "120": {"callsub": "smart_contracts.supply_chain.contract.SupplyChain.delete_application", "op": "callsub delete_application"}, "123": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "124": {"op": "return", "stack_out": []}, "125": {"block": "main_get_product_count_route@9", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%30#0"], "stack_out": ["tmp%30#0"]}, "127": {"op": "!", "defined_out": ["tmp%31#0"], "stack_out": ["tmp%31#0"]}, "128": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "129": {"op": "txn ApplicationID", "defined_out": ["tmp%32#0"], "stack_out": ["tmp%32#0"]}, "131": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "132": {"callsub": "smart_contracts.supply_chain.contract.SupplyChain.get_product_count", "op": "callsub get_product_count", "defined_out": ["to_encode%1#0"], "stack_out": ["to_encode%1#0"]}, "135": {"op": "itob", "defined_out": ["val_as_bytes%1#0"], "stack_out": ["val_as_bytes%1#0"]}, "136": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "val_as_bytes%1#0"], "stack_out": ["val_as_bytes%1#0", "0x151f7c75"]}, "137": {"op": "swap", "stack_out": ["0x151f7c75", "val_as_bytes%1#0"]}, "138": {"op": "concat", "defined_out": ["tmp%34#0"], "stack_out": ["tmp%34#0"]}, "139": {"op": "log", "stack_out": []}, "140": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "141": {"op": "return", "stack_out": []}, "142": {"block": "main_get_product_info_route@8", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%23#0"], "stack_out": ["tmp%23#0"]}, "144": {"op": "!", "defined_out": ["tmp%24#0"], "stack_out": ["tmp%24#0"]}, "145": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "146": {"op": "txn ApplicationID", "defined_out": ["tmp%25#0"], "stack_out": ["tmp%25#0"]}, "148": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "149": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[8]%3#0"], "stack_out": ["reinterpret_bytes[8]%3#0"]}, "152": {"op": "btoi", "defined_out": ["tmp%27#0"], "stack_out": ["tmp%27#0"]}, "153": {"callsub": "smart_contracts.supply_chain.contract.SupplyChain.get_product_info", "op": "callsub get_product_info", "defined_out": ["tmp%28#0"], "stack_out": ["tmp%28#0"]}, "156": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "tmp%28#0"], "stack_out": ["tmp%28#0", "0x151f7c75"]}, "157": {"op": "swap", "stack_out": ["0x151f7c75", "tmp%28#0"]}, "158": {"op": "concat", "defined_out": ["tmp%29#0"], "stack_out": ["tmp%29#0"]}, "159": {"op": "log", "stack_out": []}, "160": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "161": {"op": "return", "stack_out": []}, "162": {"block": "main_transfer_asa_route@7", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%16#0"], "stack_out": ["tmp%16#0"]}, "164": {"op": "!", "defined_out": ["tmp%17#0"], "stack_out": ["tmp%17#0"]}, "165": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "166": {"op": "txn ApplicationID", "defined_out": ["tmp%18#0"], "stack_out": ["tmp%18#0"]}, "168": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "169": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[8]%1#0"], "stack_out": ["reinterpret_bytes[8]%1#0"]}, "172": {"op": "btoi", "defined_out": ["tmp%20#0"], "stack_out": ["tmp%20#0"]}, "173": {"op": "txna ApplicationArgs 2", "defined_out": ["reinterpret_bytes[1]%1#0", "tmp%20#0"], "stack_out": ["tmp%20#0", "reinterpret_bytes[1]%1#0"]}, "176": {"op": "btoi", "defined_out": ["tmp%20#0", "tmp%21#0"], "stack_out": ["tmp%20#0", "tmp%21#0"]}, "177": {"op": "txnas Accounts", "defined_out": ["tmp%20#0", "tmp%22#0"], "stack_out": ["tmp%20#0", "tmp%22#0"]}, "179": {"op": "txna ApplicationArgs 3", "defined_out": ["reinterpret_bytes[8]%2#0", "tmp%20#0", "tmp%22#0"], "stack_out": ["tmp%20#0", "tmp%22#0", "reinterpret_bytes[8]%2#0"]}, "182": {"callsub": "smart_contracts.supply_chain.contract.SupplyChain.transfer_asa", "op": "callsub transfer_asa", "stack_out": []}, "185": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "186": {"op": "return", "stack_out": []}, "187": {"block": "main_opt_in_asa_route@6", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%9#0"], "stack_out": ["tmp%9#0"]}, "189": {"op": "!", "defined_out": ["tmp%10#0"], "stack_out": ["tmp%10#0"]}, "190": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "191": {"op": "txn ApplicationID", "defined_out": ["tmp%11#0"], "stack_out": ["tmp%11#0"]}, "193": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "194": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[8]%0#0"], "stack_out": ["reinterpret_bytes[8]%0#0"]}, "197": {"op": "btoi", "defined_out": ["tmp%13#0"], "stack_out": ["tmp%13#0"]}, "198": {"op": "txna ApplicationArgs 2", "defined_out": ["reinterpret_bytes[1]%0#0", "tmp%13#0"], "stack_out": ["tmp%13#0", "reinterpret_bytes[1]%0#0"]}, "201": {"op": "btoi", "defined_out": ["tmp%13#0", "tmp%14#0"], "stack_out": ["tmp%13#0", "tmp%14#0"]}, "202": {"op": "txnas Accounts", "defined_out": ["tmp%13#0", "tmp%15#0"], "stack_out": ["tmp%13#0", "tmp%15#0"]}, "204": {"callsub": "smart_contracts.supply_chain.contract.SupplyChain.opt_in_asa", "op": "callsub opt_in_asa", "stack_out": []}, "207": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "208": {"op": "return", "stack_out": []}, "209": {"block": "main_register_crop_route@5", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%3#0"], "stack_out": ["tmp%3#0"]}, "211": {"op": "!", "defined_out": ["tmp%4#0"], "stack_out": ["tmp%4#0"]}, "212": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "213": {"op": "txn ApplicationID", "defined_out": ["tmp%5#0"], "stack_out": ["tmp%5#0"]}, "215": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "216": {"op": "txna ApplicationArgs 1", "defined_out": ["tmp%7#0"], "stack_out": ["tmp%7#0"]}, "219": {"callsub": "smart_contracts.supply_chain.contract.SupplyChain.register_crop", "op": "callsub register_crop", "defined_out": ["to_encode%0#0"], "stack_out": ["to_encode%0#0"]}, "222": {"op": "itob", "defined_out": ["val_as_bytes%0#0"], "stack_out": ["val_as_bytes%0#0"]}, "223": {"op": "bytec_1 // 0x151f7c75", "defined_out": ["0x151f7c75", "val_as_bytes%0#0"], "stack_out": ["val_as_bytes%0#0", "0x151f7c75"]}, "224": {"op": "swap", "stack_out": ["0x151f7c75", "val_as_bytes%0#0"]}, "225": {"op": "concat", "defined_out": ["tmp%8#0"], "stack_out": ["tmp%8#0"]}, "226": {"op": "log", "stack_out": []}, "227": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "228": {"op": "return", "stack_out": []}, "229": {"block": "main_bare_routing@12", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%43#0"], "stack_out": ["tmp%43#0"]}, "231": {"op": "bnz main_after_if_else@14", "stack_out": []}, "234": {"op": "txn ApplicationID", "defined_out": ["tmp%44#0"], "stack_out": ["tmp%44#0"]}, "236": {"op": "!", "defined_out": ["tmp%45#0"], "stack_out": ["tmp%45#0"]}, "237": {"error": "can only call when creating", "op": "assert // can only call when creating", "stack_out": []}, "238": {"op": "intc_0 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "239": {"op": "return", "stack_out": []}, "240": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.register_crop", "params": {"info#0": "bytes"}, "block": "register_crop", "stack_in": [], "op": "proto 1 1"}, "243": {"op": "itxn_begin"}, "244": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "246": {"op": "frame_dig -1", "defined_out": ["info#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "info#0 (copy)"]}, "248": {"op": "pushint 42 // 42", "defined_out": ["42", "info#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "info#0 (copy)", "42"]}, "250": {"op": "extract_uint64", "defined_out": ["inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0"]}, "251": {"op": "intc_1 // 0", "defined_out": ["0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "0"]}, "252": {"op": "bytec_0 // \"product_count\"", "defined_out": ["\"product_count\"", "0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "0", "\"product_count\""]}, "253": {"op": "app_global_get_ex", "defined_out": ["inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "maybe_value%0#0", "maybe_exists%0#0"]}, "254": {"error": "check self.product_count exists", "op": "assert // check self.product_count exists", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "maybe_value%0#0"]}, "255": {"op": "itob", "defined_out": ["inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0"]}, "256": {"op": "global CurrentApplicationAddress", "defined_out": ["inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0"]}, "258": {"op": "dupn 3", "defined_out": ["inner_txn_params%0%%param_ConfigAssetClawback_idx_0#0", "inner_txn_params%0%%param_ConfigAssetFreeze_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0", "inner_txn_params%0%%param_ConfigAssetFreeze_idx_0#0", "inner_txn_params%0%%param_ConfigAssetClawback_idx_0#0"]}, "260": {"op": "itxn_field ConfigAssetClawback", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0", "inner_txn_params%0%%param_ConfigAssetFreeze_idx_0#0"]}, "262": {"op": "itxn_field ConfigAssetFreeze", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0", "inner_txn_params%0%%param_ConfigAssetReserve_idx_0#0"]}, "264": {"op": "itxn_field ConfigAssetReserve", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0", "inner_txn_params%0%%param_ConfigAssetManager_idx_0#0"]}, "266": {"op": "itxn_field ConfigAssetManager", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_ConfigAssetName_idx_0#0"]}, "268": {"op": "itxn_field ConfigAssetName", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0"]}, "270": {"op": "pushbytes \"CROP\"", "defined_out": ["\"CROP\"", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "\"CROP\""]}, "276": {"op": "itxn_field ConfigAssetUnitName", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0"]}, "278": {"op": "intc_1 // 0", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0", "0"]}, "279": {"op": "itxn_field ConfigAssetDecimals", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_ConfigAssetTotal_idx_0#0"]}, "281": {"op": "itxn_field ConfigAssetTotal", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "283": {"op": "pushint 3 // acfg", "defined_out": ["acfg", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "acfg"]}, "285": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "287": {"op": "itxn_field Fee", "stack_out": []}, "289": {"op": "itxn_submit"}, "290": {"op": "itxn CreatedAssetID", "defined_out": ["itxn_result.CreatedAssetID#0"], "stack_out": ["itxn_result.CreatedAssetID#0"]}, "292": {"op": "dup", "defined_out": ["itxn_result.CreatedAssetID#0", "itxn_result.CreatedAssetID#0 (copy)"], "stack_out": ["itxn_result.CreatedAssetID#0", "itxn_result.CreatedAssetID#0 (copy)"]}, "293": {"op": "itob", "defined_out": ["box_key#0", "itxn_result.CreatedAssetID#0"], "stack_out": ["itxn_result.CreatedAssetID#0", "box_key#0"]}, "294": {"op": "frame_dig -1", "stack_out": ["itxn_result.CreatedAssetID#0", "box_key#0", "info#0 (copy)"]}, "296": {"op": "box_put", "stack_out": ["itxn_result.CreatedAssetID#0"]}, "297": {"op": "intc_1 // 0", "stack_out": ["itxn_result.CreatedAssetID#0", "0"]}, "298": {"op": "bytec_0 // \"product_count\"", "stack_out": ["itxn_result.CreatedAssetID#0", "0", "\"product_count\""]}, "299": {"op": "app_global_get_ex", "defined_out": ["itxn_result.CreatedAssetID#0", "maybe_exists%1#0", "maybe_value%1#0"], "stack_out": ["itxn_result.CreatedAssetID#0", "maybe_value%1#0", "maybe_exists%1#0"]}, "300": {"error": "check self.product_count exists", "op": "assert // check self.product_count exists", "stack_out": ["itxn_result.CreatedAssetID#0", "maybe_value%1#0"]}, "301": {"op": "intc_0 // 1", "defined_out": ["1", "itxn_result.CreatedAssetID#0", "maybe_value%1#0"], "stack_out": ["itxn_result.CreatedAssetID#0", "maybe_value%1#0", "1"]}, "302": {"op": "+", "defined_out": ["itxn_result.CreatedAssetID#0", "new_state_value%0#0"], "stack_out": ["itxn_result.CreatedAssetID#0", "new_state_value%0#0"]}, "303": {"op": "bytec_0 // \"product_count\"", "stack_out": ["itxn_result.CreatedAssetID#0", "new_state_value%0#0", "\"product_count\""]}, "304": {"op": "swap", "stack_out": ["itxn_result.CreatedAssetID#0", "\"product_count\"", "new_state_value%0#0"]}, "305": {"op": "app_global_put", "stack_out": ["itxn_result.CreatedAssetID#0"]}, "306": {"retsub": true, "op": "retsub"}, "307": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.opt_in_asa", "params": {"product_id#0": "uint64", "framer_address#0": "bytes"}, "block": "opt_in_asa", "stack_in": [], "op": "proto 2 0"}, "310": {"op": "itxn_begin"}, "311": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "313": {"op": "intc_1 // 0", "defined_out": ["0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "0"]}, "314": {"op": "itxn_field AssetAmount", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "316": {"op": "frame_dig -2", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "product_id#0 (copy)"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "product_id#0 (copy)"]}, "318": {"op": "itxn_field XferAsset", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "320": {"op": "frame_dig -1", "defined_out": ["framer_address#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "framer_address#0 (copy)"]}, "322": {"op": "itxn_field AssetReceiver", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "324": {"op": "intc_2 // axfer", "defined_out": ["axfer", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "axfer"]}, "325": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "327": {"op": "itxn_field Fee", "stack_out": []}, "329": {"op": "itxn_submit"}, "330": {"retsub": true, "op": "retsub"}, "331": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.transfer_asa", "params": {"product_id#0": "uint64", "framer_address#0": "bytes", "quantity#0": "bytes"}, "block": "transfer_asa", "stack_in": [], "op": "proto 3 0"}, "334": {"op": "itxn_begin"}, "335": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "337": {"op": "frame_dig -1", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "quantity#0 (copy)"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "quantity#0 (copy)"]}, "339": {"op": "btoi", "defined_out": ["inner_txn_params%0%%param_AssetAmount_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetAmount_idx_0#0"]}, "340": {"op": "itxn_field AssetAmount", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "342": {"op": "frame_dig -3", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "product_id#0 (copy)"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "product_id#0 (copy)"]}, "344": {"op": "itxn_field XferAsset", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "346": {"op": "frame_dig -2", "defined_out": ["framer_address#0 (copy)", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "framer_address#0 (copy)"]}, "348": {"op": "itxn_field AssetReceiver", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "350": {"op": "intc_2 // axfer", "defined_out": ["axfer", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "axfer"]}, "351": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "353": {"op": "itxn_field Fee", "stack_out": []}, "355": {"op": "itxn_submit"}, "356": {"retsub": true, "op": "retsub"}, "357": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.get_product_info", "params": {"product_id#0": "uint64"}, "block": "get_product_info", "stack_in": [], "op": "proto 1 1"}, "360": {"op": "frame_dig -1", "defined_out": ["product_id#0 (copy)"], "stack_out": ["product_id#0 (copy)"]}, "362": {"op": "itob", "defined_out": ["box_key#0"], "stack_out": ["box_key#0"]}, "363": {"op": "box_get", "defined_out": ["box_data#0", "tmp%1#0"], "stack_out": ["box_data#0", "tmp%1#0"]}, "364": {"op": "pop", "stack_out": ["box_data#0"]}, "365": {"retsub": true, "op": "retsub"}, "366": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.get_product_count", "params": {}, "block": "get_product_count", "stack_in": [], "op": "intc_1 // 0", "defined_out": ["0"], "stack_out": ["0"]}, "367": {"op": "bytec_0 // \"product_count\"", "defined_out": ["\"product_count\"", "0"], "stack_out": ["0", "\"product_count\""]}, "368": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0"], "stack_out": ["maybe_value%0#0", "maybe_exists%0#0"]}, "369": {"error": "check self.product_count exists", "op": "assert // check self.product_count exists", "stack_out": ["maybe_value%0#0"]}, "370": {"retsub": true, "op": "retsub"}, "371": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.delete_application", "params": {}, "block": "delete_application", "stack_in": [], "op": "txn Sender", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "373": {"op": "global CreatorAddress", "defined_out": ["tmp%0#0", "tmp%1#0"], "stack_out": ["tmp%0#0", "tmp%1#0"]}, "375": {"op": "==", "defined_out": ["tmp%2#0"], "stack_out": ["tmp%2#0"]}, "376": {"op": "assert", "stack_out": []}, "377": {"retsub": true, "op": "retsub"}, "378": {"subroutine": "smart_contracts.supply_chain.contract.SupplyChain.update_application", "params": {}, "block": "update_application", "stack_in": [], "op": "txn Sender", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "380": {"op": "global CreatorAddress", "defined_out": ["tmp%0#0", "tmp%1#0"], "stack_out": ["tmp%0#0", "tmp%1#0"]}, "382": {"op": "==", "defined_out": ["tmp%2#0"], "stack_out": ["tmp%2#0"]}, "383": {"op": "assert", "stack_out": []}, "384": {"retsub": true, "op": "retsub"}}}