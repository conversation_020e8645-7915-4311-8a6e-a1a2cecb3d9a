import { create } from 'zustand';

export interface Milestone {
  id: number;
  name: string;
  description: string;
  amount: number;
  status: 'pending' | 'completed' | 'verified' | 'released' | 'disputed';
  dueDate: string;
  completedDate?: string;
}

export interface Transaction {
  id: string;
  contractAddress: string;
  buyerAddress: string;
  sellerAddress: string;
  productName: string;
  quantity: number;
  totalAmount: number;
  releaseAmount: number;
  status: 'pending' | 'active' | 'completed' | 'disputed' | 'canceled';
  lastUpdated: string;
  createdAt: string;
  milestones: Milestone[];
  documents: string[];
}

interface TransactionState {
  transactions: Transaction[];
  activeTransaction: Transaction | null;
  loading: boolean;
  error: string | null;
  fetchTransactions: () => Promise<void>;
  setActiveTransaction: (id: string) => void;
  approveMilestone: (transactionId: string, milestoneId: number) => Promise<void>;
  releaseFunds: (transactionId: string, milestoneId: number) => Promise<void>;
  disputeMilestone: (transactionId: string, milestoneId: number) => Promise<void>;
}

// Mock data
const mockTransactions: Transaction[] = [
  {
    id: 'tx-1',
    contractAddress: '0x1234567890abcdef1234567890abcdef12345678',
    buyerAddress: '0xabcdef1234567890abcdef1234567890abcdef12',
    sellerAddress: '0x7890abcdef1234567890abcdef1234567890abcd',
    productName: 'Organic Wheat',
    quantity: 500,
    totalAmount: 12500,
    releaseAmount: 2500,
    status: 'active',
    lastUpdated: new Date().toISOString(),
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    milestones: [
      {
        id: 1,
        name: 'Contract Signing',
        description: 'Initial contract signing and deposit',
        amount: 2500,
        status: 'completed',
        dueDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        completedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        name: 'Quality Verification',
        description: 'Product quality verification',
        amount: 2500,
        status: 'pending',
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 3,
        name: 'Shipping Confirmation',
        description: 'Product shipping confirmation',
        amount: 2500,
        status: 'pending',
        dueDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 4,
        name: 'Delivery Verification',
        description: 'Product delivery verification',
        amount: 2500,
        status: 'pending',
        dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 5,
        name: 'Final Payment',
        description: 'Final payment release',
        amount: 2500,
        status: 'pending',
        dueDate: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    documents: [
      'contract_tx1.pdf',
      'quality_certificate_tx1.pdf'
    ]
  },
  {
    id: 'tx-2',
    contractAddress: '0x2345678901abcdef2345678901abcdef23456789',
    buyerAddress: '0xabcdef1234567890abcdef1234567890abcdef12',
    sellerAddress: '0x8901abcdef2345678901abcdef2345678901abcde',
    productName: 'Organic Rice',
    quantity: 300,
    totalAmount: 9000,
    releaseAmount: 0,
    status: 'pending',
    lastUpdated: new Date().toISOString(),
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    milestones: [
      {
        id: 1,
        name: 'Contract Signing',
        description: 'Initial contract signing and deposit',
        amount: 1800,
        status: 'pending',
        dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        name: 'Quality Verification',
        description: 'Product quality verification',
        amount: 1800,
        status: 'pending',
        dueDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 3,
        name: 'Shipping Confirmation',
        description: 'Product shipping confirmation',
        amount: 1800,
        status: 'pending',
        dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 4,
        name: 'Delivery Verification',
        description: 'Product delivery verification',
        amount: 1800,
        status: 'pending',
        dueDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 5,
        name: 'Final Payment',
        description: 'Final payment release',
        amount: 1800,
        status: 'pending',
        dueDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    documents: [
      'contract_tx2.pdf'
    ]
  },
  {
    id: 'tx-3',
    contractAddress: '0x3456789012abcdef3456789012abcdef34567890',
    buyerAddress: '0xabcdef1234567890abcdef1234567890abcdef12',
    sellerAddress: '0x9012abcdef3456789012abcdef3456789012abcde',
    productName: 'Premium Coffee Beans',
    quantity: 100,
    totalAmount: 7500,
    releaseAmount: 7500,
    status: 'completed',
    lastUpdated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    milestones: [
      {
        id: 1,
        name: 'Contract Signing',
        description: 'Initial contract signing and deposit',
        amount: 1500,
        status: 'released',
        dueDate: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString(),
        completedDate: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        name: 'Quality Verification',
        description: 'Product quality verification',
        amount: 1500,
        status: 'released',
        dueDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
        completedDate: new Date(Date.now() - 22 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 3,
        name: 'Shipping Confirmation',
        description: 'Product shipping confirmation',
        amount: 1500,
        status: 'released',
        dueDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        completedDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 4,
        name: 'Delivery Verification',
        description: 'Product delivery verification',
        amount: 1500,
        status: 'released',
        dueDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        completedDate: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 5,
        name: 'Final Payment',
        description: 'Final payment release',
        amount: 1500,
        status: 'released',
        dueDate: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
        completedDate: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    documents: [
      'contract_tx3.pdf',
      'quality_certificate_tx3.pdf',
      'shipping_info_tx3.pdf',
      'delivery_confirmation_tx3.pdf'
    ]
  }
];

export const useTransactionStore = create<TransactionState>((set, get) => ({
  transactions: [],
  activeTransaction: null,
  loading: false,
  error: null,

  fetchTransactions: async () => {
    set({ loading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      set({ transactions: mockTransactions, loading: false });
    } catch (error) {
      set({ error: 'Failed to fetch transactions', loading: false });
    }
  },

  setActiveTransaction: (id: string) => {
    const transaction = get().transactions.find(tx => tx.id === id) || null;
    set({ activeTransaction: transaction });
  },

  approveMilestone: async (transactionId: string, milestoneId: number) => {
    set({ loading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set(state => {
        const updatedTransactions = state.transactions.map(transaction => {
          if (transaction.id === transactionId) {
            const updatedMilestones = transaction.milestones.map(milestone => {
              if (milestone.id === milestoneId) {
                return { ...milestone, status: 'verified' as const, completedDate: new Date().toISOString() };
              }
              return milestone;
            });
            
            return {
              ...transaction,
              milestones: updatedMilestones,
              lastUpdated: new Date().toISOString()
            };
          }
          return transaction;
        });
        
        // Update active transaction if needed
        let activeTransaction = state.activeTransaction;
        if (activeTransaction && activeTransaction.id === transactionId) {
          activeTransaction = updatedTransactions.find(tx => tx.id === transactionId) || null;
        }
        
        return {
          transactions: updatedTransactions,
          activeTransaction,
          loading: false
        };
      });
    } catch (error) {
      set({ error: 'Failed to approve milestone', loading: false });
    }
  },

  releaseFunds: async (transactionId: string, milestoneId: number) => {
    set({ loading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set(state => {
        const updatedTransactions = state.transactions.map(transaction => {
          if (transaction.id === transactionId) {
            const updatedMilestones = transaction.milestones.map(milestone => {
              if (milestone.id === milestoneId) {
                return { ...milestone, status: 'released' as const };
              }
              return milestone;
            });
            
            // Calculate released amount
            const releaseAmount = updatedMilestones
              .filter(m => m.status === 'released')
              .reduce((sum, m) => sum + m.amount, 0);
            
            // Check if all milestones are released
            const allCompleted = updatedMilestones.every(m => m.status === 'released');
            
            return {
              ...transaction,
              milestones: updatedMilestones,
              releaseAmount,
              status: allCompleted ? 'completed' as const : transaction.status,
              lastUpdated: new Date().toISOString()
            };
          }
          return transaction;
        });
        
        // Update active transaction if needed
        let activeTransaction = state.activeTransaction;
        if (activeTransaction && activeTransaction.id === transactionId) {
          activeTransaction = updatedTransactions.find(tx => tx.id === transactionId) || null;
        }
        
        return {
          transactions: updatedTransactions,
          activeTransaction,
          loading: false
        };
      });
    } catch (error) {
      set({ error: 'Failed to release funds', loading: false });
    }
  },

  disputeMilestone: async (transactionId: string, milestoneId: number) => {
    set({ loading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set(state => {
        const updatedTransactions = state.transactions.map(transaction => {
          if (transaction.id === transactionId) {
            const updatedMilestones = transaction.milestones.map(milestone => {
              if (milestone.id === milestoneId) {
                return { ...milestone, status: 'disputed' as const };
              }
              return milestone;
            });
            
            return {
              ...transaction,
              milestones: updatedMilestones,
              status: 'disputed' as const,
              lastUpdated: new Date().toISOString()
            };
          }
          return transaction;
        });
        
        // Update active transaction if needed
        let activeTransaction = state.activeTransaction;
        if (activeTransaction && activeTransaction.id === transactionId) {
          activeTransaction = updatedTransactions.find(tx => tx.id === transactionId) || null;
        }
        
        return {
          transactions: updatedTransactions,
          activeTransaction,
          loading: false
        };
      });
    } catch (error) {
      set({ error: 'Failed to dispute milestone', loading: false });
    }
  }
}));