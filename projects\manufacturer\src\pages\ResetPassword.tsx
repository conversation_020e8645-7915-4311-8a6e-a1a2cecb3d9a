import AuthLayout from '@/components/AuthLayout'
import FormInput from '@/components/FormInput'
import { useToast } from '@/hooks/use-toast'
import apiClient from '@/services/apiClient'
import axios from 'axios'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

const ResetPassword = () => {
  const { toast } = useToast()
  const navigate = useNavigate()
  const { uid, token } = useParams()
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoading, setIsLoading] = useState(false)

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!newPassword) {
      newErrors.newPassword = 'New password is required'
    } else if (newPassword.length < 8) {
      newErrors.newPassword = 'Password must be at least 8 characters long'
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (confirmPassword !== newPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    try {
      console.log('Resetting password with params:', uid, token)

      // Make the API call to reset the password
      await apiClient.post('/user/reset-password-confirm/', {
        uid,
        token,
        password: newPassword,
      })

      toast({
        title: 'Password reset successful!',
        description: 'Your password has been updated. You can now log in with your new password.',
      })

      navigate('/login', {
        state: { message: 'Password reset successful' },
      })
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error(error)
        toast({
          title: 'Error',
          description: error.response?.data.error?.[0] || 'Failed to reset password',
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Error',
          description: 'Failed to reset password',
          variant: 'destructive',
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout title="Reset Password" subtitle="Enter your new password below">
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput
          label="New Password"
          type="password"
          value={newPassword}
          onChange={setNewPassword}
          error={errors.newPassword}
          placeholder="Enter your new password"
        />

        <FormInput
          label="Confirm Password"
          type="password"
          value={confirmPassword}
          onChange={setConfirmPassword}
          error={errors.confirmPassword}
          placeholder="Confirm your new password"
        />

        <button
          type="submit"
          disabled={isLoading}
          className={`w-full py-3 px-4 bg-button-bg text-button-text rounded-lg font-medium ${
            isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-button-bg-hover transition-colors'
          }`}
        >
          {isLoading ? 'Resetting Password...' : 'Reset Password'}
        </button>
      </form>
    </AuthLayout>
  )
}

export default ResetPassword
