import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// User interface based on the login response
export interface User {
  id: number
  name: string
  email: string
  role: string
  account_address?: string | null
  opt_in?: boolean
  onboarding_completed?: boolean
}

// Login response interface
export interface LoginResponse {
  token: string
  message: string
  user: User
}

// Auth store state interface
interface AuthState {
  // Authentication status
  isAuthenticated: boolean
  loading: boolean
  error: string | null

  // User data
  user: User | null
  token: string | null

  // Actions
  login: (loginData: LoginResponse) => void
  logout: () => void
  setUser: (user: User) => void
  setToken: (token: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  checkAuth: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
}

// Create the auth store with persistence
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      loading: false,
      error: null,
      user: null,
      token: null,

      // Login action - handles the complete login response
      login: (loginData: LoginResponse) => {
        const { token, user } = loginData

        set({
          isAuthenticated: true,
          user,
          token,
          loading: false,
          error: null,
        })
      },

      // Logout action - clears all user data
      logout: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          loading: false,
          error: null,
        })
        // Remove persisted snapshot
        localStorage.removeItem('auth-storage')
      },

      // Set user data
      setUser: (user: User) => {
        set({ user })
      },

      // Set authentication token
      setToken: (token: string) => {
        set({ token })
      },

      // Set loading state
      setLoading: (loading: boolean) => {
        set({ loading })
      },

      // Set error message
      setError: (error: string | null) => {
        set({ error })
      },

      // Clear error message
      clearError: () => {
        set({ error: null })
      },

      // Check authentication status on app initialization
      checkAuth: async () => {
        set({ loading: true })

        try {
          const { token, user } = get()

          if (token && user) {
            // Optionally verify token with backend here
            set({
              isAuthenticated: true,
              loading: false,
            })
          } else {
            set({
              isAuthenticated: false,
              loading: false,
            })
          }
        } catch (error) {
          console.error('Auth check failed:', error)
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            loading: false,
            error: 'Authentication check failed',
          })
        }
      },

      // Update user data partially
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData }
          set({ user: updatedUser })
        }
      },
    }),
    {
      name: 'auth-storage', // localStorage key
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    },
  ),
)

// Selector hooks for specific parts of the state
export const useUser = () => useAuthStore((state) => state.user)
export const useToken = () => useAuthStore((state) => state.token)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.loading)
export const useAuthError = () => useAuthStore((state) => state.error)

// Helper hooks for onboarding flow
export const useIsOnboardingCompleted = () => {
  const user = useAuthStore((state) => state.user)
  return user ? !!user.onboarding_completed : false
}

export const useRequiresOnboarding = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const user = useAuthStore((state) => state.user)
  return isAuthenticated && user && !user.onboarding_completed
}
