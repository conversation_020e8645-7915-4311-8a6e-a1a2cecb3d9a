import { AlgoViteClientConfig, AlgoViteKMDConfig } from '../../interfaces/network'
import {
  VITE_ALGOD_NETWORK,
  VITE_ALGOD_PORT,
  VITE_ALGOD_SERVER,
  VITE_ALGOD_TOKEN,
  VITE_INDEXER_PORT,
  VITE_INDEXER_SERVER,
  VITE_INDEXER_TOKEN,
  VITE_KMD_PASSWORD,
  VITE_KMD_PORT,
  VITE_KMD_SERVER,
  VITE_KMD_TOKEN,
  VITE_KMD_WALLET,
} from '../variable'

export function getAlgodConfigFromViteEnvironment(): AlgoViteClientConfig {
  if (!VITE_ALGOD_SERVER) {
    throw new Error('Attempt to get default algod configuration without specifying VITE_ALGOD_SERVER in the environment variables')
  }

  return {
    server: VITE_ALGOD_SERVER,
    port: VITE_ALGOD_PORT,
    token: VITE_ALGOD_TOKEN,
    network: VITE_ALGOD_NETWORK,
  }
}

export function getIndexerConfigFromViteEnvironment(): AlgoViteClientConfig {
  if (!VITE_INDEXER_SERVER) {
    throw new Error('Attempt to get default algod configuration without specifying VITE_INDEXER_SERVER in the environment variables')
  }

  return {
    server: VITE_INDEXER_SERVER,
    port: VITE_INDEXER_PORT,
    token: VITE_INDEXER_TOKEN,
    network: VITE_ALGOD_NETWORK,
  }
}

export function getKmdConfigFromViteEnvironment(): AlgoViteKMDConfig {
  if (!VITE_KMD_SERVER) {
    throw new Error('Attempt to get default kmd configuration without specifying VITE_KMD_SERVER in the environment variables')
  }

  return {
    server: VITE_KMD_SERVER,
    port: VITE_KMD_PORT,
    token: VITE_KMD_TOKEN,
    wallet: VITE_KMD_WALLET,
    password: VITE_KMD_PASSWORD,
  }
}
