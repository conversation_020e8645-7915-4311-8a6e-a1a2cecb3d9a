import React from 'react';
import { Wheat, Coffee, BrainIcon, Leaf, Apple, Carrot, Apple as NutsIcon, Flower } from 'lucide-react';

export const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Grains':
      return <Wheat className="w-5 h-5" />;
    case 'Coffee':
      return <Coffee className="w-5 h-5" />;
    case 'Tea':
      return <Leaf className="w-5 h-5" />;
    case 'Spices':
      return <BrainIcon className="w-5 h-5" />;
    case 'Fruits':
      return <Apple className="w-5 h-5" />;
    case 'Vegetables':
      return <Carrot className="w-5 h-5" />;
    case 'Nuts':
      return <NutsIcon className="w-5 h-5" />;
    case 'Herbs':
      return <Flower className="w-5 h-5" />;
    default:
      return <Wheat className="w-5 h-5" />;
  }
};