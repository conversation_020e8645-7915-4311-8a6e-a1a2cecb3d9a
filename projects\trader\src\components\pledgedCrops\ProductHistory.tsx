import { cn } from '@/lib/utils'
import { ellipseAddress } from '@/utils/ellipseAddress'
import { ArrowRight, Building, CheckCircle2, Clock, ExternalLink, Factory, FileText, User } from 'lucide-react'
import { OwnershipTransfer } from '../../utils/types'

interface ProductHistoryProps {
  transfers: OwnershipTransfer[]
}

const getStatusColor = (status: OwnershipTransfer['status']) => {
  switch (status) {
    case 'PENDING':
      return 'bg-status-pending-bg text-status-pending-text border-status-pending-text/20'
    case 'IN_PROGRESS':
      return 'bg-status-active-bg text-status-active-text border-status-active-text/20'
    case 'COMPLETED':
      return 'bg-status-completed-bg text-status-completed-text border-status-completed-text/20'
  }
}

const getStatusIcon = (status: OwnershipTransfer['status']) => {
  switch (status) {
    case 'PENDING':
      return <Clock className="h-4 w-4" />
    case 'IN_PROGRESS':
      return <ArrowRight className="h-4 w-4" />
    case 'COMPLETED':
      return <CheckCircle2 className="h-4 w-4" />
  }
}

export function ProductHistory({ transfers }: ProductHistoryProps) {
  if (!transfers || transfers.length === 0) {
    return (
      <div className="bg-card-bg p-6 rounded-lg shadow-md border border-card-border">
        <div className="bg-card-bg rounded-lg p-6">
          <h2 className="text-xl font-semibold text-primary-text mb-6">Ownership History</h2>
          <div className="text-secondary-text">No transfer history available.</div>
        </div>
      </div>
    )
  }

  const lastTransfer = transfers[transfers.length - 1]
  const currentOwnerId = lastTransfer?.to_user?.account_address
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(date)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'KTT',
    }).format(amount)
  }

  const getOwnerIcon = (type: OwnershipTransfer['from_user']['role']) => {
    switch (type) {
      case 'farmer':
        return <User className="h-5 w-5 text-accent" />
      case 'trader':
        return <Building className="h-5 w-5 text-accent" />
      case 'manufacturer':
        return <Factory className="h-5 w-5 text-accent" />
    }
  }

  return (
    <div className="bg-card-bg p-6 rounded-lg shadow-md border border-card-border">
      <div className="bg-card-bg rounded-lg p-6">
        <h2 className="text-xl font-semibold text-primary-text mb-6">Ownership History</h2>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-accent/20" />

          {/* Timeline entries */}
          <div className="space-y-8">
            {transfers.map((transfer, _) => (
              <div
                key={transfer.transaction_id}
                className={`relative pl-16 ${
                  transfer.to_user.account_address === currentOwnerId
                    ? 'bg-accent-light/10 -mx-6 px-6 py-4 rounded-lg border border-accent/20'
                    : ''
                }`}
              >
                {/* Timeline dot */}
                <div
                  className={`absolute left-4 w-4 h-4 rounded-full border-2 ${
                    transfer.to_user.account_address === currentOwnerId ? 'bg-accent border-accent' : 'bg-card-bg border-accent/40'
                  }`}
                />

                <div className="space-y-4">
                  {/* Transfer header */}
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-4 mb-2">
                        <span className="text-secondary-text flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          {formatDate(new Date(transfer.timestamp))}
                        </span>
                        <div
                          className={cn('px-2 py-1 rounded-full text-xs border flex items-center gap-1.5', getStatusColor(transfer.status))}
                        >
                          {getStatusIcon(transfer.status)}
                          {transfer.status.replace('_', ' ')}
                        </div>
                        {transfer.to_user.account_address === currentOwnerId && (
                          <span className="px-2 py-1 text-accent-dark bg-accent-light rounded-full text-xs">Current Owner</span>
                        )}
                      </div>
                      <div className="flex items-center gap-3 text-primary-text">
                        <div className="flex items-center gap-2">
                          {getOwnerIcon(transfer.from_user.role)}
                          <span>{transfer.from_user.name}</span>
                          <span className="text-secondary-text text-sm">({ellipseAddress(transfer.from_user.account_address)})</span>
                        </div>
                        <ArrowRight className="h-4 w-4 text-accent" />
                        <div className="flex items-center gap-2">
                          {getOwnerIcon(transfer.to_user.role)}
                          <span>{transfer.to_user.name}</span>
                          <span className="text-secondary-text text-sm">({ellipseAddress(transfer.to_user.account_address)})</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-primary-text font-semibold">{formatCurrency(transfer.cost)}</div>
                      <a
                        href={`https://lora.algokit.io/localnet/transaction/${transfer.transaction_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-secondary-text text-sm hover:text-accent flex items-center gap-1 justify-end transition-colors duration-200"
                      >
                        View Transaction
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>

                  {/* Transfer details */}
                  {(transfer.notes || (transfer.conditions?.length ?? 0) > 0) && (
                    <div className="bg-card-bg rounded-lg p-4 space-y-3 border border-card-border">
                      {transfer.notes && (
                        <div className="flex items-start gap-2 text-secondary-text">
                          <FileText className="h-4 w-4 mt-1 text-accent" />
                          <p>{transfer.notes}</p>
                        </div>
                      )}
                      {transfer.conditions && transfer.conditions.length > 0 && (
                        <div className="space-y-2">
                          <span className="text-secondary-text text-sm">Transfer Conditions:</span>
                          <ul className="space-y-1">
                            {transfer.conditions.map((condition, i) => (
                              <li key={i} className="flex items-center gap-2 text-primary-text">
                                <CheckCircle2 className="h-4 w-4 text-status-active-text" />
                                {condition}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Attached documents */}
                  {transfer.documents && transfer.documents.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {transfer.documents.map((doc, i) => (
                        <a
                          key={i}
                          href={doc.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-3 py-1 bg-accent-light/20 text-accent-dark rounded-full text-sm hover:bg-accent-light/30 transition-colors duration-200 flex items-center gap-2"
                        >
                          <FileText className="h-4 w-4" />
                          {doc.type}
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
