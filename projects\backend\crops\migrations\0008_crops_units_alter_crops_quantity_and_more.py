# Generated by Django 5.2 on 2025-05-03 04:15

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("crops", "0007_alter_croptransfer_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="crops",
            name="units",
            field=models.CharField(
                choices=[("kg", "kg"), ("tons", "tons")], default="tons", max_length=4
            ),
        ),
        migrations.AlterField(
            model_name="crops",
            name="quantity",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=16,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
        migrations.AlterField(
            model_name="croptransfer",
            name="cost",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=16,
                validators=[django.core.validators.MinValueValidator(0)],
            ),
        ),
    ]
