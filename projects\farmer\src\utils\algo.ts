import { AlgorandClient } from '@algorandfoundation/algokit-utils'
import { useWallet } from '@txnlab/use-wallet-react'
import { SupplyChainClient } from '../contracts/SupplyChain'
import { getAlgodConfigFromViteEnvironment } from './network/getAlgoClientConfigs'
import { VITE_SUPPLY_CHAIN_SM_ID } from './variable'

export const algodConfig = getAlgodConfigFromViteEnvironment()
export const algorand = AlgorandClient.fromConfig({ algodConfig })

/**
 * Creates a configured instance of {@link SupplyChainClient} using the current wallet and environment settings.
 *
 * @returns A {@link SupplyChainClient} initialized with the active wallet address and signer, if available.
 */
export function useSupplyChainClient() {
  const { activeAddress, transactionSigner: signer } = useWallet()

  return new SupplyChainClient({
    appId: BigInt(VITE_SUPPLY_CHAIN_SM_ID),
    algorand: algorand,
    defaultSender: activeAddress || undefined,
    defaultSigner: signer || undefined,
  })
}
