export interface WalletAccount {
  address: string
  name: string
  balance: number
}

export interface Asset {
  'asset-id': number
  amount: number
  'is-frozen': boolean
}
export interface Transaction {
  id: string
  type: 'earn' | 'burn' | 'pledge'
  amount: number
  timestamp: Date
  status: 'completed' | 'pending' | 'failed'
  networkFee?: number
  fromAddress: string
  toAddress: string
  transactionHash: string
  blockNumber?: number
  confirmations?: number
  errorMessage?: string
  notes?: string
  blockExplorerUrl?: string
}
export interface TransactionFilters {
  type?: 'earn' | 'burn' | 'pledge'
  status?: 'completed' | 'pending' | 'failed'
  dateFrom?: Date
  dateTo?: Date
}

export interface InventoryItem {
  id: number
  crop_name: string
  trader: {
    id: number
    name: string
    account_address: string
  }
  description: string
  unit: string
  ready_to_sell_quantity: number
  total_quantity_in_storage: number
  sold_quantity: number
  total_quantity_batches: number
  category: string
  icon?: React.ReactNode
}

export interface Crop {
  crop_id: string
  quantity: number
  unit: 'kg' | 'tons'
  crop_grade: 'A' | 'B' | 'C'
  certifications?: string
  location: {
    address: string
    coordinates: {
      lat: number
      long: number
    }
  }
  growth_period: {
    start_date: string
    harvest_date: string
  }
  soil_type: string
  irrigation_type: string
  fertilizers_used: string[]
  created_at: string
}

export interface OwnershipTransfer {
  transaction_id: string
  timestamp: string
  from_user: {
    name: string
    account_address: string
    role: 'farmer' | 'trader' | 'manufacturer'
  }
  to_user: {
    name: string
    account_address: string
    role: 'farmer' | 'trader' | 'manufacturer'
  }
  cost: number
  transactionHash?: string
  notes?: string
  conditions?: string[]
  verifiedBy?: string
  documents?: {
    type: string
    url: string
  }[]
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
}
