import { useUser, useAuthStore } from '../stores/authStore'

const UserProfile = () => {
  const user = useUser()
  const { logout, updateUser } = useAuthStore()

  if (!user) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg">
        <p className="text-gray-600">No user data available</p>
      </div>
    )
  }

  const handleLogout = () => {
    logout()
  }

  const handleUpdateProfile = () => {
    // Example of updating user data
    updateUser({
      name: 'Updated Trader Name',
    })
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Trader Profile</h2>
      
      <div className="space-y-3">
        <div>
          <label className="font-semibold text-gray-700">ID:</label>
          <p className="text-gray-600">{user.id}</p>
        </div>
        
        <div>
          <label className="font-semibold text-gray-700">Name:</label>
          <p className="text-gray-600">{user.name}</p>
        </div>
        
        <div>
          <label className="font-semibold text-gray-700">Email:</label>
          <p className="text-gray-600">{user.email}</p>
        </div>
        
        <div>
          <label className="font-semibold text-gray-700">Role:</label>
          <p className="text-gray-600 capitalize">{user.role}</p>
        </div>
        
        <div>
          <label className="font-semibold text-gray-700">Account Address:</label>
          <p className="text-gray-600 font-mono text-sm break-all">
            {user.account_address || 'Not connected'}
          </p>
        </div>
        
        <div>
          <label className="font-semibold text-gray-700">Opt-in Status:</label>
          <p className="text-gray-600">
            {user.opt_in ? (
              <span className="text-green-600">✓ Opted in</span>
            ) : (
              <span className="text-red-600">✗ Not opted in</span>
            )}
          </p>
        </div>
      </div>

      <div className="mt-6 space-x-4">
        <button
          onClick={handleUpdateProfile}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Update Profile
        </button>
        
        <button
          onClick={handleLogout}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Logout
        </button>
      </div>
    </div>
  )
}

export default UserProfile
