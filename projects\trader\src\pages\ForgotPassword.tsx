import AuthLayout from '@/components/AuthLayout'
import FormInput from '@/components/FormInput'
import { useToast } from '@/hooks/use-toast'
import apiClient from '@/services/apiClient'
import axios from 'axios'
import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'

const ForgotPassword = () => {
  const [email, setEmail] = useState('')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const navigate = useNavigate()

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    // Simulate API call
    try {
      await apiClient.post('user/forgot-password/', { email })
      toast({
        title: 'Success',
        description: 'Password reset link sent to your email',
      })
      navigate('/login')
    } catch (error) {
      console.error(error)

      if (axios.isAxiosError(error) && error.response) {
        toast({
          title: 'Error',
          description: error.response.data.message,
        })
      } else {
        toast({
          title: 'Error',
          description: 'An error occurred. Please try again.',
        })
        setIsLoading(false)
      }
    }
  }

  return (
    <AuthLayout title="Reset Password" subtitle="Enter your email to receive password reset instructions">
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput label="Email" type="email" value={email} onChange={setEmail} error={errors.email} placeholder="Enter your email" />

        <button
          type="submit"
          disabled={isLoading}
          className={`w-full py-3 px-4 bg-button-bg text-button-text rounded-lg font-medium
            ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-button-bg-hover'}`}
        >
          {isLoading ? 'Sending instructions...' : 'Send Instructions'}
        </button>

        <p className="text-center text-primary-text">
          <span className="opacity-60">Remember your password? </span>
          <Link to="/login" className="text-link-text hover:text-link-text/80 font-bold">
            Sign in
          </Link>
        </p>
      </form>
    </AuthLayout>
  )
}

export default ForgotPassword
