import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';

interface ProtectedRouteProps {
  children: ReactNode;
  isAllowed: boolean;
  redirectPath?: string;
}

const ProtectedRoute = ({ 
  children, 
  isAllowed, 
  redirectPath = '/login' 
}: ProtectedRouteProps) => {
  if (!isAllowed) {
    return <Navigate to={redirectPath} replace />;
  }
  
  return <>{children}</>;
};

export default ProtectedRoute;