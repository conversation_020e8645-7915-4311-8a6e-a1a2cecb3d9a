import { Batch } from '@/utils/types'
import { <PERSON>ert<PERSON><PERSON><PERSON>, ArrowDown, ArrowUp } from 'lucide-react'
import React, { useState } from 'react'
import DataGridRow from '../DataGrid/DataGridRow'

type SortField = keyof Batch | null
type SortDirection = 'asc' | 'desc'

interface DataGridProps {
  batches: Batch[]
}

const DataGrid: React.FC<DataGridProps> = ({ batches }) => {
  const [sortField, setSortField] = useState<SortField>('status')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')

  const handleSort = (field: keyof Batch) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const getSortedBatches = () => {
    if (!sortField) return batches

    return [...batches].sort((a, b) => {
      let comparison = 0
      const valueA = a[sortField]
      const valueB = b[sortField]

      // Custom sorting for status field
      if (sortField === 'status') {
        const statusOrder: Record<string, number> = {
          in_progress: 3,
          storage: 1,
          ready: 2,
          sold: 4,
        }

        const orderA = statusOrder[valueA as string] || 999
        const orderB = statusOrder[valueB as string] || 999

        comparison = orderA - orderB
      } else if (typeof valueA === 'string' && typeof valueB === 'string') {
        comparison = valueA.localeCompare(valueB)
      } else if (typeof valueA === 'number' && typeof valueB === 'number') {
        comparison = valueA - valueB
      } else {
        comparison = String(valueA).localeCompare(String(valueB))
      }

      return sortDirection === 'asc' ? comparison : -comparison
    })
  }

  const sortedBatches = getSortedBatches()

  const renderSortIndicator = (field: keyof Batch) => {
    if (sortField !== field) return null

    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4 ml-1 text-accent" /> : <ArrowDown className="h-4 w-4 ml-1 text-accent" />
  }

  const sortableHeaderClass = 'cursor-pointer hover:text-accent transition-colors'

  if (sortedBatches.length === 0) {
    return (
      <div className="bg-card-bg rounded-lg p-8 text-center">
        <div className="flex justify-center mb-4">
          <AlertCircle className="h-12 w-12 text-accent" />
        </div>
        <h3 className="text-xl font-semibold text-primary-text mb-2">No Batches Found</h3>
        <p className="text-secondary-text">Try adjusting your search or filter criteria, or add a new batch to get started.</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto rounded-lg shadow">
      <table className="min-w-full divide-y divide-card-border">
        <thead className="bg-card-bg">
          <tr>
            <th
              scope="col"
              className={`px-4 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider ${sortableHeaderClass}`}
              onClick={() => handleSort('crop_id')}
            >
              <div className="flex items-center">
                Batch ID
                {renderSortIndicator('crop_id')}
              </div>
            </th>

            <th
              scope="col"
              className={`px-4 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider ${sortableHeaderClass}`}
              onClick={() => handleSort('created_at')}
            >
              <div className="flex items-center">
                Planting Date
                {renderSortIndicator('created_at')}
              </div>
            </th>
            <th
              scope="col"
              className={`px-4 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider ${sortableHeaderClass}`}
              onClick={() => handleSort('status')}
            >
              <div className="flex items-center">
                Status
                {renderSortIndicator('status')}
              </div>
            </th>
            <th
              scope="col"
              className={`px-4 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider ${sortableHeaderClass}`}
              onClick={() => handleSort('quantity')}
            >
              <div className="flex items-center">
                Quantity
                {renderSortIndicator('quantity')}
              </div>
            </th>
            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
              Quality Grade
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-card-border">
          {sortedBatches.map((batch, index) => (
            <DataGridRow key={batch.crop_id} batch={batch} isEven={index % 2 === 0} />
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default DataGrid
