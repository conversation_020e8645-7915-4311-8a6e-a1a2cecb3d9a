import AssetsScreen from '@/components/onboarding/AssetsScreen'
import CompleteScreen from '@/components/onboarding/CompleteScreen'
import ContractScreen from '@/components/onboarding/ContractScreen'
import SelectAccountScreen from '@/components/onboarding/SelectAccountScreen'
import WalletScreen from '@/components/onboarding/WalletScreen'
import WelcomeScreen from '@/components/onboarding/WelcomeScreen'
import { CheckCircle2, HelpCircle } from 'lucide-react'
import React, { useState } from 'react'

type OnboardingStep = 'welcome' | 'wallet' | 'select-account' | 'contract' | 'assets' | 'complete'

interface StepIndicatorProps {
  currentStep: OnboardingStep
  steps: { id: OnboardingStep; label: string }[]
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep, steps }) => {
  const currentIndex = steps.findIndex((step) => step.id === currentStep)

  return (
    <div className="w-full max-w-3xl mx-auto mb-8">
      <div className="flex justify-between items-center relative">
        <div className="absolute left-0 top-1/2 h-0.5 w-full bg-[#424242] -z-10" />
        {steps.map((step, index) => {
          const isCompleted = index < currentIndex
          const isCurrent = index === currentIndex

          return (
            <div key={step.id} className="flex flex-col items-center gap-2">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  isCompleted ? 'bg-[#1B5E20] text-[#00E676]' : isCurrent ? 'bg-[#1B5E20] text-white' : 'bg-[#424242] text-[#BDBDBD]'
                }`}
              >
                {isCompleted ? <CheckCircle2 className="h-5 w-5" /> : <span>{index + 1}</span>}
              </div>
              <span className={`text-sm ${isCurrent ? 'text-white' : 'text-[#BDBDBD]'}`}>{step.label}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}
export function OnboardingFlow() {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedAccount, setSelectedAccount] = useState<string>('')
  const [isOptedIn, setIsOptedIn] = useState<boolean>(false)
  const [openWalletModal, setOpenWalletModal] = useState<boolean>(false)

  const toggleWalletModal = () => {
    setOpenWalletModal(!openWalletModal)
  }

  const steps = [
    { id: 'welcome' as const, label: 'Welcome' },
    { id: 'wallet' as const, label: 'Wallet' },
    { id: 'select-account' as const, label: 'Select Account' },
    { id: 'contract' as const, label: 'Contract' },
    { id: 'assets' as const, label: 'Assets' },
    { id: 'complete' as const, label: 'Complete' },
  ]

  const handleNext = () => {
    const currentIndex = steps.findIndex((step) => step.id === currentStep)
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id)
    }
  }

  const handleBack = () => {
    const currentIndex = steps.findIndex((step) => step.id === currentStep)
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1].id)
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 'welcome':
        return <WelcomeScreen handleNext={handleNext}></WelcomeScreen>

      case 'wallet':
        return (
          <WalletScreen
            handleNext={handleNext}
            handleBack={handleBack}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
            openWalletModal={openWalletModal}
            toggleWalletModal={toggleWalletModal}
          ></WalletScreen>
        )

      case 'select-account':
        return (
          <SelectAccountScreen
            handleNext={handleNext}
            handleBack={handleBack}
            selectedAccount={selectedAccount}
            setSelectedAccount={setSelectedAccount}
          ></SelectAccountScreen>
        )

      case 'contract':
        return (
          <ContractScreen
            handleNext={handleNext}
            handleBack={handleBack}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
            selectedAccount={selectedAccount}
          ></ContractScreen>
        )
      case 'assets':
        return (
          <AssetsScreen
            handleNext={handleNext}
            handleBack={handleBack}
            isOptedIn={isOptedIn}
            selectedAccount={selectedAccount}
            setIsOptedIn={setIsOptedIn}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
          ></AssetsScreen>
        )

      case 'complete':
        return <CompleteScreen handleBack={handleBack} selectedAccount={selectedAccount}></CompleteScreen>
    }
  }

  return (
    <div className="min-h-screen bg-[#212121] py-8 px-4">
      {/* Progress Indicator */}
      <StepIndicator currentStep={currentStep} steps={steps} />

      {/* Main Content */}
      <div className="container mx-auto py-8">{renderStep()}</div>
      {/* Help Section */}
      <div className="flex justify-between items-center p-4 bg-secondary rounded-lg">
        <div className="text-[#BDBDBD] flex items-center space-x-2">
          <HelpCircle className="h-5 w-5" />
          <span>Need help? </span>
          <a href="#" className="text-accent hover:text-[#00c853] transition-colors">
            View FAQ
          </a>
        </div>
        <a href="mailto:<EMAIL>" className="text-accent hover:text-[#00c853] transition-colors">
          Contact Support
        </a>
      </div>
    </div>
  )
}
