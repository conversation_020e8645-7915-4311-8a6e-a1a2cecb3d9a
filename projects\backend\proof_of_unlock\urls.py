# 1. GET method - Get user specific miletstone details
# 2. UPDATE - update milestones with request.data = {milestone_id, status}, transaction_id, statusUpdate
# 3. POST - Create PoU smart contract in Central DB

from django.urls import path
from .views import create_proof_of_unlock, get_proof_of_unlock_by_user, update_milestone

urlpatterns = [
    path("proof-of-unlock/", create_proof_of_unlock, name="create-proof-of-unlock"),
    path(
        "proof-of-unlock/user/",
        get_proof_of_unlock_by_user,
        name="get-proof-of-unlock-by-user",
    ),
    path(
        "milestones/update/",
        update_milestone,
        name="update-milestone",
    ),
]
