import { ArrowR<PERSON>, DollarSign, Leaf, Truck } from 'lucide-react'

interface WelcomeScreenProps {
  handleNext: () => void
}

export default function WelcomeScreen({ handleNext }: WelcomeScreenProps) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-primary-text mb-4">Welcome to Agritram!</h1>
        <p className="text-primary-text opacity-60 text-lg max-w-2xl mx-auto">
          Your gateway to modern farming. Track crops, manage supply chains, and access financial services - all in one secure platform.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <div className="bg-alt-bg p-6 rounded-lg text-center shadow-md">
          <div className="w-12 h-12 bg-button-bg rounded-full flex items-center justify-center mx-auto mb-4">
            <Leaf className="h-6 w-6 text-button-text" />
          </div>
          <h3 className="text-primary-text font-semibold mb-2">Crop Tracking</h3>
          <p className="text-primary-text opacity-60">Monitor your crops from planting to harvest with detailed analytics</p>
        </div>

        <div className="bg-alt-bg p-6 rounded-lg text-center shadow-md">
          <div className="w-12 h-12 bg-button-bg rounded-full flex items-center justify-center mx-auto mb-4">
            <Truck className="h-6 w-6 text-button-text" />
          </div>
          <h3 className="text-primary-text font-semibold mb-2">Supply Chain</h3>
          <p className="text-primary-text opacity-60">Streamline your supply chain with transparent tracking and verification</p>
        </div>

        <div className="bg-alt-bg p-6 rounded-lg text-center shadow-md">
          <div className="w-12 h-12 bg-button-bg rounded-full flex items-center justify-center mx-auto mb-4">
            <DollarSign className="h-6 w-6 text-button-text" />
          </div>
          <h3 className="text-primary-text font-semibold mb-2">Financial Access</h3>
          <p className="text-primary-text opacity-60">Access loans, insurance, and other financial services with ease</p>
        </div>
      </div>

      <div className="text-center">
        <button
          onClick={handleNext}
          className="bg-button-bg text-button-text px-8 py-3 rounded-lg hover:bg-button-bg-hover transition-colors duration-300 flex items-center justify-center mx-auto"
        >
          Get Started
          <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </div>
  )
}
