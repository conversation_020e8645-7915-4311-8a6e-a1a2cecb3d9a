from django.db import models
from user.models import User
from crops.models import Crops


class ProofOfUnlock(models.Model):
    """
    Represents a blockchain transaction for trading crops between users.
    """

    tx_id = models.CharField(
        max_length=100,
        primary_key=True,
        help_text="Unique transaction identifier (e.g., 'tx-1')",
    )
    contract_address = models.CharField( editable=False, blank=False)
    # Manufacturer
    buyer = models.ForeignKey(
        User,
        related_name="purchases",
        on_delete=models.CASCADE,
        help_text="User who buys the crops",
    )
    # Trader
    seller = models.ForeignKey(
        User,
        related_name="sales",
        on_delete=models.CASCADE,
        help_text="User who sells the crops",
    )
    crops = models.ManyToManyField(
        Crops,
        through="ProofOfUnlockCrop",
        related_name="proof_of_unlocks",
    )
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Total amount agreed for the transaction",
    )
    release_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Amount released so far",
    )

    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("active", "Active"),
        ("completed", "Completed"),
        ("disputed", "Disputed"),
        ("canceled", "Canceled"),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="active",
        help_text="Current status of the transaction",
    )
    created_at = models.DateTimeField(
        help_text="Timestamp when the transaction was created",
    )
    last_updated = models.DateTimeField(
        help_text="Timestamp when the transaction was last updated",
    )

    def __str__(self):
        return f"Transaction {self.tx_id}: {self.status}"


class ProofOfUnlockCrop(models.Model):
    """
    Intermediate model linking Transactions and Crops with quantity and unit.
    """

    transaction = models.ForeignKey(
        ProofOfUnlock,
        on_delete=models.CASCADE,
        related_name="transaction_crops",
    )
    crop = models.ForeignKey(
        Crops,
        on_delete=models.CASCADE,
    )
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Amount of crop in the specified unit",
    )
    unit = models.CharField(
        max_length=20,
        help_text="Unit of measurement (e.g., 'tonnes')",
    )

    def __str__(self):
        return f"{self.quantity} {self.unit} of {self.crop.name}"


class Milestone(models.Model):
    """
    Defines a payment milestone within a transaction.
    """

    transaction = models.ForeignKey(
        ProofOfUnlock,
        related_name="milestones",
        on_delete=models.CASCADE,
    )
    name = models.CharField(
        max_length=255,
        help_text="Milestone name",
    )
    description = models.TextField(
        help_text="Detailed description of the milestone",
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Amount tied to this milestone",
    )
    # released funds
    MILESTONE_STATUS_CHOICES = [
        ("pending", "Pending"),
        ("released", "Released"),
        ("completed", "Completed"),
        ("shipped", "Shipped"),
        ("disputed", "Disputed"),
        ("cancelled", "Cancelled"),
    ]
    status = models.CharField(
        max_length=20,
        choices=MILESTONE_STATUS_CHOICES,
        default="pending",
        help_text="Current fulfillment status of the milestone",
    )
    completed_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date when the milestone was completed",
    )

    def __str__(self):
        return f"{self.name} ({self.status})"


class Document(models.Model):
    """
    Stores documents related to a transaction (e.g., contracts, certificates).
    """

    transaction = models.ForeignKey(
        ProofOfUnlock,
        related_name="documents",
        on_delete=models.CASCADE,
    )
    filename = models.CharField(
        max_length=255,
        help_text="Name of the file",
    )
    file_type = models.CharField(
        max_length=50,
        help_text="MIME type of the document",
    )
    file_hash = models.CharField(
        max_length=64,
        help_text="Hash of the document for verification",
    )

    def __str__(self):
        return self.filename
