# Generated by Django 5.2 on 2025-04-22 08:49

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="MonthlyExpense",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("year", models.IntegerField()),
                ("month", models.IntegerField()),
                (
                    "total_amount",
                    models.DecimalField(decimal_places=6, default=0, max_digits=18),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("MINT", "Mint"),
                            ("TRANSFER", "Transfer"),
                            ("BURN", "Burn"),
                            ("PLEDGE", "Pledge"),
                            ("CONVERT_KTT_TO_KCT", "Convert KTT to KCT"),
                            ("CONVERT_KCT_TO_KTT", "Convert KCT to KTT"),
                        ],
                        default="MINT",
                        max_length=18,
                    ),
                ),
                ("transaction_count", models.IntegerField(default=0)),
            ],
            options={
                "ordering": ["year", "month"],
            },
        ),
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "transaction_id",
                    models.CharField(editable=False, primary_key=True, serialize=False),
                ),
                ("algorand_tx_id", models.CharField(max_length=52, unique=True)),
                ("from_address", models.CharField(max_length=58)),
                ("to_address", models.CharField(max_length=58)),
                (
                    "amount_usd",
                    models.DecimalField(decimal_places=6, default=0, max_digits=18),
                ),
                (
                    "amount_stablecoin_ktt",
                    models.DecimalField(decimal_places=6, default=0, max_digits=18),
                ),
                (
                    "amount_stablecoin_kct",
                    models.DecimalField(decimal_places=6, default=0, max_digits=18),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("MINT", "Mint"),
                            ("TRANSFER", "Transfer"),
                            ("BURN", "Burn"),
                            ("PLEDGE", "Pledge"),
                            ("CONVERT_KTT_TO_KCT", "Convert KTT to KCT"),
                            ("CONVERT_KCT_TO_KTT", "Convert KCT to KTT"),
                        ],
                        max_length=18,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("COMPLETED", "Completed"),
                            ("FAILED", "Failed"),
                        ],
                        default="PENDING",
                        max_length=10,
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("gas_fee", models.DecimalField(decimal_places=6, max_digits=18)),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("MTM", "Mobile Money Transfer"),
                            ("CARD", "Credit Card/Debit Card"),
                        ],
                        default="CARD",
                        max_length=10,
                    ),
                ),
            ],
        ),
    ]
