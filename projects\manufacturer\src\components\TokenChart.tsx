import { Card } from '@/components/ui/card'
import apiClient from '@/services/apiClient'
import { useEffect, useState } from 'react'
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

export const TokenChart = () => {
  const [data, setData] = useState([])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await apiClient.get('/transaction/monthly-expenses-minting/')
        const result = await response.data
        console.log(result)

        // Map the fetched data to the format required for the chart
        const chartData = result.map((item: { month_name: string; total_amount: string }) => ({
          date: item.month_name,
          tokens: parseFloat(item.total_amount).toFixed(2), // Convert total_amount to a number
        }))

        setData(chartData)
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }

    fetchData()
  }, [])

  return (
    <Card className="p-6 bg-alt-bg border-none h-[400px]">
      <h3 className="text-primary-text text-lg font-medium mb-4">Token History</h3>
      <ResponsiveContainer className="pb-8" width="100%" height="100%">
        <LineChart data={data}>
          <XAxis dataKey="date" stroke="#000000" tick={{ fill: '#000000' }} />
          <YAxis stroke="#000000" tick={{ fill: '#000000' }} />
          <Tooltip
            contentStyle={{
              backgroundColor: '#FFFFFF',
              border: '1px solid #2D4D31',
              borderRadius: '8px',
              color: '#000000',
            }}
          />
          <Line
            type="monotone"
            dataKey="tokens"
            stroke="#2D4D31"
            strokeWidth={2}
            dot={{ fill: '#2D4D31', strokeWidth: 2 }}
            activeDot={{ r: 6, fill: '#2D4D31' }}
          />
        </LineChart>
      </ResponsiveContainer>
    </Card>
  )
}
