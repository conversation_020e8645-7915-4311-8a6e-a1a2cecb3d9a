import { LoginResponse } from '../stores/authStore'
import apiClient from './apiClient'

export const login = async (email: string, password: string): Promise<LoginResponse> => {
  try {
    const response = await apiClient.post<LoginResponse>('/user/login/', {
      email,
      password,
    })

    if (response.data && response.data.token && response.data.user) {
      // Don't store in localStorage here - let the store handle persistence
      return response.data
    } else {
      throw new Error('Login response data is incomplete or missing required fields.')
    }
  } catch (error) {
    console.error('Error during login:', error)
    throw error
  }
}

export const registerUser = async (data: { name: string; email: string; password: string }) => {
  return apiClient.post('/user/register/', {
    name: data.name,
    email: data.email,
    password: data.password,
    role: 'trader',
  })
}
