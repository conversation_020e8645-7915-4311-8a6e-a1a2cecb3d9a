import { useToast } from '@/hooks/use-toast'
import apiClient from '@/services/apiClient'
import { AlertCircle, ArrowLeft, ArrowRight, Info, Loader2 } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

export function KCTToKTTConversion() {
  const location = useLocation()
  const dataReceived = location.state

  const [kttBalance, setKttBalance] = useState(0)
  const [kctBalance, setKctBalance] = useState(0)
  const [inputAmount, setInputAmount] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const conversionRate = 0.91
  const [error, setError] = useState<string | null>(null)
  const isKttToKct = false
  const navigate = useNavigate()
  const { toast } = useToast()

  const currentBalance = isKttToKct ? kttBalance : kctBalance
  const outputAmount = isKttToKct ? parseFloat(inputAmount) * conversionRate : parseFloat(inputAmount) / conversionRate

  const isValidAmount = parseFloat(inputAmount) > 0 && parseFloat(inputAmount) <= currentBalance
  const isLargeAmount = parseFloat(inputAmount) > currentBalance * 0.5

  // New effect to set balances from dataReceived
  useEffect(() => {
    if (dataReceived) {
      setKttBalance(dataReceived.kttBalance)
      setKctBalance(dataReceived.kctBalance)
    }
  }, [dataReceived])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setInputAmount(value)
      setError(null)
    }
  }

  const handleConvert = async () => {
    if (!isValidAmount) return

    setIsLoading(true)
    try {
      // TODO: Simulate API call
      const response = await apiClient.post('/transaction/convert-kct-to-ktt/', {
        amount: inputAmount,
      })

      if (response.status === 200) {
        toast({
          title: 'Success!',
          description: 'Tokens burned successfully.',
        })
        navigate(-1)
      } else {
        throw new Error('Transaction failed.')
      }
      setInputAmount('')
      setShowConfirmation(false)
    } catch (err) {
      setError('Transaction failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-main-bg py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-primary-text mb-8">
          <button onClick={() => navigate(-1)} className="mr-4 text-accent hover:text-accent-hover transition-colors">
            <ArrowLeft className="h-6 w-6" />
          </button>
          Sell Investment Tokens
        </h1>

        <>
          {/* Balance Display */}
          <div className="bg-card-bg shadow-md p-6 rounded-lg mb-6 grid grid-cols-1 md:grid-cols-2 gap-4 border border-card-border">
            <div className="p-4 rounded-lg bg-accent-light/5">
              <span className="text-secondary-text">Available KTT Balance</span>
              <span className="block text-2xl font-bold text-primary-text mt-1">{kttBalance.toLocaleString()} KTT</span>
            </div>
            <div className="p-4 rounded-lg bg-accent-light/5">
              <span className="text-secondary-text">Available KCT Balance</span>
              <span className="block text-2xl font-bold text-primary-text mt-1">{kctBalance.toLocaleString()} KCT</span>
            </div>
          </div>

          {/* Conversion Form */}
          <div className="bg-card-bg shadow-md p-6 rounded-lg mb-6 border border-card-border">
            <div className="space-y-4">
              {/* Input Amount */}
              <div>
                <label className="block text-sm font-medium text-primary-text mb-2">{isKttToKct ? 'KTT Amount' : 'KCT Amount'}</label>
                <div className="relative">
                  <input
                    type="text"
                    className={`w-full bg-card-bg border ${
                      error ? 'border-button-danger' : 'border-card-border'
                    } rounded-lg px-4 py-2 text-primary-text focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-200`}
                    value={inputAmount}
                    onChange={handleInputChange}
                    placeholder="0.00"
                  />
                  <button
                    onClick={() => setInputAmount(currentBalance.toString())}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-accent hover:text-accent-hover transition-colors"
                  >
                    MAX
                  </button>
                </div>
              </div>

              {/* Swap Direction Button */}
              {/* <div className="flex justify-center">
                <button onClick={handleSwapDirection} className="p-2 rounded-full hover:bg-[#424242] transition-colors">
                  <ArrowLeftRight className="h-6 w-6 text-[#00E676]" />
                </button>
              </div> */}

              {/* Conversion Rate */}
              <div className="flex items-center justify-center space-x-4 py-4 bg-accent-light/5 rounded-lg">
                <span className="text-primary-text font-medium">1 {isKttToKct ? 'KTT' : 'KCT'}</span>
                <ArrowRight className="h-5 w-5 text-accent" />
                <span className="text-primary-text font-medium">
                  {isKttToKct ? conversionRate.toFixed(2) : (1 / conversionRate).toFixed(2)} {isKttToKct ? 'KCT' : 'KTT'}
                </span>
                {/* <button
                  onClick={() => {
                    setConversionRate({
                      rate: 1.5 + (Math.random() * 0.1 - 0.05),
                      lastUpdated: new Date(),
                    })
                  }}
                  className="text-[#BDBDBD] hover:text-white transition-colors"
                >
                  <RefreshCw className="h-4 w-4" />
                </button> */}
              </div>

              {/* Output Amount */}
              <div>
                <label className="block text-sm font-medium text-primary-text mb-2">
                  {isKttToKct ? 'KCT Amount' : 'KTT Amount'} (Estimated)
                </label>
                <div className="w-full bg-card-bg border border-card-border px-4 py-2 rounded-lg text-primary-text">
                  {outputAmount ? outputAmount.toFixed(2) : '0'}
                </div>
              </div>

              {/* Gas Estimate */}
              {/* <div className="flex items-center justify-between text-sm text-[#BDBDBD] bg-[#212121] p-3 rounded-lg">
                <span>Estimated Gas Fee:</span>
                <span>{gasEstimate} ETH</span>
              </div> */}

              {/* Error Message */}
              {error && (
                <div className="flex items-center space-x-2 text-button-danger bg-button-danger/10 p-3 rounded-lg">
                  <AlertCircle className="h-5 w-5" />
                  <span>{error}</span>
                </div>
              )}

              {/* Convert Button */}
              <button
                onClick={() => setShowConfirmation(true)}
                disabled={!isValidAmount || isLoading}
                className="w-full bg-button-bg text-button-text px-6 py-3 rounded-lg hover:bg-button-bg-hover transition-colors duration-300 flex items-center justify-center space-x-2 disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Selling...</span>
                  </>
                ) : (
                  <span>Sell Tokens</span>
                )}
              </button>
            </div>
          </div>

          {/* Transaction History */}
          {/* <div className="bg-[#303030] rounded-lg overflow-hidden">
            <div
              className="p-4 flex items-center justify-between cursor-pointer hover:bg-[#424242]"
              onClick={() => setShowHistory(!showHistory)}
            >
              <div className="flex items-center space-x-2">
                <History className="h-5 w-5 text-[#BDBDBD]" />
                <h2 className="text-white font-medium">Transaction History</h2>
              </div>
              <RefreshCw className={`h-5 w-5 text-[#BDBDBD] ${isLoading ? 'animate-spin' : ''}`} />
            </div>

            {showHistory && (
              <div className="border-t border-[#424242]">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-[#BDBDBD] text-left">
                        <th className="p-4">From</th>
                        <th className="p-4">To</th>
                        <th className="p-4">Status</th>
                        <th className="p-4">Time</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((tx) => (
                        <tr key={tx.id} className="border-t border-[#424242]">
                          <td className="p-4 text-white">
                            {tx.fromAmount.toLocaleString()} {tx.fromToken}
                          </td>
                          <td className="p-4 text-[#00BCD4]">
                            {tx.toAmount.toLocaleString()} {tx.toToken}
                          </td>
                          <td className="p-4">
                            <span
                              className={`flex items-center space-x-1 ${
                                tx.status === 'completed' ? 'text-green-500' : tx.status === 'pending' ? 'text-yellow-500' : 'text-red-500'
                              }`}
                            >
                              {tx.status === 'completed' ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : tx.status === 'pending' ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <XCircle className="h-4 w-4" />
                              )}
                              <span>{tx.status}</span>
                            </span>
                          </td>
                          <td className="p-4 text-[#BDBDBD]">{tx.timestamp.toLocaleTimeString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div> */}
        </>

        {/* Confirmation Modal */}
        {showConfirmation && (
          <div className="fixed inset-0 bg-pop-over-bg bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-card-bg rounded-lg p-6 max-w-md w-full border border-card-border">
              <h3 className="text-xl font-bold text-primary-text mb-4">Confirm Conversion</h3>

              <div className="space-y-4">
                <div className="bg-accent-light/5 p-4 rounded-lg space-y-2">
                  <div className="flex justify-between text-primary-text">
                    <span>From:</span>
                    <span className="font-medium">
                      {inputAmount} {isKttToKct ? 'KTT' : 'KCT'}
                    </span>
                  </div>
                  <div className="flex justify-between text-primary-text">
                    <span>To:</span>
                    <span className="font-medium">
                      {outputAmount.toFixed(2)} {isKttToKct ? 'KCT' : 'KTT'}
                    </span>
                  </div>
                  <div className="flex justify-between text-primary-text">
                    <span>Rate:</span>
                    <span className="font-medium">
                      1 {isKttToKct ? 'KTT' : 'KCT'} = {isKttToKct ? conversionRate.toFixed(2) : (1 / conversionRate).toFixed(2)}{' '}
                      {isKttToKct ? 'KCT' : 'KTT'}
                    </span>
                  </div>
                  {/* <div className="flex justify-between text-[#BDBDBD]">
                    <span>Gas Fee:</span>
                    <span>{gasEstimate} ETH</span>
                  </div> */}
                </div>

                {isLargeAmount && (
                  <div className="flex items-start space-x-2 text-border-warning bg-border-warning/10 p-4 rounded-lg">
                    <Info className="h-5 w-5 flex-shrink-0" />
                    <p className="text-sm">
                      You are about to convert a large amount of tokens. Please double-check the details before confirming.
                    </p>
                  </div>
                )}

                <div className="flex gap-4">
                  <button
                    onClick={handleConvert}
                    disabled={isLoading}
                    className="flex-1 bg-button-bg text-button-text px-6 py-3 rounded-lg hover:bg-button-bg-hover transition-colors duration-300 flex items-center justify-center disabled:opacity-50"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin mr-2" />
                        <span>Selling...</span>
                      </>
                    ) : (
                      <span>Confirm</span>
                    )}
                  </button>
                  <button
                    onClick={() => setShowConfirmation(false)}
                    className="flex-1 bg-button-danger text-button-text px-6 py-3 rounded-lg hover:bg-button-danger-hover transition-colors duration-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
