import apiClient from '@/services/apiClient'
import { useWallet } from '@txnlab/use-wallet-react'
import { File<PERSON>he<PERSON>, Loader2 } from 'lucide-react'
import { ellipseAddress } from '../../utils/ellipseAddress'

interface ContractScreenProps {
  handleNext: () => void
  handleBack: () => void
  isLoading: boolean
  setIsLoading: (value: boolean) => void
  selectedAccount: string
}
/**
 * Displays the smart contract approval screen, allowing users to approve FarmChain's smart contract for their selected account.
 *
 * Renders information about the contract integration, transaction details for the selected account, and navigation controls for proceeding or going back. Handles the approval process with loading state management and error handling.
 */
export default function ContractScreen({ handleNext, handleBack, isLoading, setIsLoading, selectedAccount }: ContractScreenProps) {
  const { wallets } = useWallet()

  const handleApproveContract = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.post('/user/opt-in-account/', {})
      if (response.status !== 200) {
        throw new Error('Approve smart contract failed')
      }
      handleNext()
    } catch (error) {
      console.error('Contract approval failed:', error)
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-primary-text mb-4">Smart Contract Integration</h2>
        <p className="text-primary-text opacity-60">
          This step allows FarmChain to help manage your farm operations securely on the blockchain.
        </p>
      </div>

      <div className="bg-alt-bg shadow-md p-6 rounded-lg space-y-6">
        <div className="space-y-4">
          <div className="flex items-start gap-4">
            <FileCheck className="h-6 w-6 text-primary-text mt-1" />
            <div>
              <h3 className="text-primary-text font-semibold mb-2">What happens in this step?</h3>
              <p className="text-primary-text opacity-60">By approving the smart contract, you're enabling FarmChain to:</p>
              <ul className="list-disc list-inside text-primary-text opacity-60 mt-2 space-y-1">
                <li>Track your crop data securely</li>
                <li>Process supply chain transactions</li>
                <li>Manage financial services access</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-border-primary pt-4">
            <h3 className="text-primary-text font-semibold mb-3">Transaction Details:</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-primary-text opacity-60">Selected Account:</span>
                <span className="text-primary-text font-mono">
                  {ellipseAddress(
                    wallets
                      ?.find((wallet) => wallet.accounts?.some((account) => account.address === selectedAccount))
                      ?.accounts?.find((account) => account.address === selectedAccount)?.address,
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-primary-text/10 text-primary-text opacity-60 hover:text-primary-text hover:border-primary-text  duration-300"
        >
          Back
        </button>
        <button
          onClick={handleApproveContract}
          disabled={isLoading}
          className="bg-button-bg text-button-text px-6 py-2 rounded-lg hover:bg-button-bg-hover  duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
              <Loader2 className="animate-spin h-5 w-5 mr-2" />
              Approving...
            </>
          ) : (
            <>
              <FileCheck className="h-5 w-5 mr-2" />
              Approve Contract
            </>
          )}
        </button>
      </div>
    </div>
  )
}
