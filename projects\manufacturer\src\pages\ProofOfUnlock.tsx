import { ellipseAddress } from '@/utils/ellipseAddress'
import { useWallet } from '@txnlab/use-wallet-react'
import { CheckCircle2, FileText, Filter, Key, LockKeyhole, Search, Wallet } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'
import MilestoneProgress from '../components/proofOfUnlock/MilestoneProgress'
import TransactionCard from '../components/proofOfUnlock/TransactionCard'
import { Milestone, Transaction, useTransactionStore } from '../stores/transactionStore'

const ProofOfUnlock = () => {
  const location = useLocation()
  const { transactions, activeTransaction, setActiveTransaction, loading, approveMilestone, releaseFunds, fetchTransactions } =
    useTransactionStore()
  const [filter, setFilter] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const { activeAddress, transactionSigner: signer } = useWallet()
  // Get transaction ID from URL query params if it exists
  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const id = params.get('id')

    if (id) {
      setActiveTransaction(id)
    }

    fetchTransactions()
  }, [location.search, setActiveTransaction, fetchTransactions])

  // Filter transactions based on status and search query
  const filteredTransactions = transactions.filter((tx: Transaction) => {
    const matchesFilter = filter === 'all' || tx.status === filter
    const matchesSearch =
      tx.crops.some((crop) => crop.productName.toLowerCase().includes(searchQuery.toLowerCase())) ||
      tx.tx_id.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesFilter && matchesSearch
  })

  const handleSelectTransaction = (transaction: Transaction) => {
    setActiveTransaction(transaction.tx_id)
  }

  const handleApprove = (milestoneId: number) => {
    if (activeTransaction) {
      approveMilestone(activeTransaction.tx_id, milestoneId, activeAddress as string, signer)
    }
  }

  const handleRelease = (milestoneId: number) => {
    if (activeTransaction) {
      releaseFunds(activeTransaction.tx_id, milestoneId, activeAddress as string, signer)
    }
  }

  const renderMilestoneActions = (milestone: Milestone) => {
    // Find the index of the current milestone
    const currentIndex = activeTransaction?.milestones.findIndex((m) => m.id === milestone.id) ?? -1

    // Check if previous milestone is released
    const previousMilestone = currentIndex > 0 ? activeTransaction?.milestones[currentIndex - 1] : null
    const isPreviousReleased = previousMilestone?.status === 'released'

    // First milestone or previous milestone is released
    if (currentIndex === 0 || isPreviousReleased) {
      // Special handling for milestone index 1 (second milestone)
      if (currentIndex === 1) {
        switch (milestone.status) {
          case 'pending':
            return (
              <div className="flex items-center gap-2 text-secondary-text">
                <LockKeyhole className="w-4 h-4" />
                <span className="text-sm font-medium">Waiting for Trader to Update</span>
              </div>
            )
          case 'shipped':
            return (
              <div className="flex gap-2">
                <button
                  onClick={() => handleApprove(milestone.id)}
                  className="px-3 py-1 text-sm font-medium text-button-text bg-accent rounded-lg hover:bg-accent-hover transition-colors"
                >
                  Approve
                </button>
              </div>
            )
          case 'verified':
          case 'completed':
            return (
              <div className="flex items-center gap-2">
                <Wallet className="w-4 h-4 text-accent" />
                <button
                  onClick={() => handleRelease(milestone.id)}
                  className="px-3 py-1 text-sm font-medium text-button-text bg-accent rounded-lg hover:bg-accent-hover transition-colors"
                >
                  Release Funds
                </button>
              </div>
            )
          case 'released':
            return (
              <div className="flex items-center gap-2 text-status-completed-text">
                <CheckCircle2 className="w-4 h-4" />
                <span className="text-sm font-medium">Funds Released</span>
              </div>
            )
          default:
            return null
        }
      }

      // Normal flow for other milestones
      switch (milestone.status) {
        case 'pending':
          return (
            <div className="flex gap-2">
              <button
                onClick={() => handleApprove(milestone.id)}
                className="px-3 py-1 text-sm font-medium text-button-text bg-accent rounded-lg hover:bg-accent-hover transition-colors"
              >
                Approve
              </button>
            </div>
          )
        case 'verified':
        case 'completed':
          return (
            <div className="flex items-center gap-2">
              <Wallet className="w-4 h-4 text-accent" />
              <button
                onClick={() => handleRelease(milestone.id)}
                className="px-3 py-1 text-sm font-medium text-button-text bg-accent rounded-lg hover:bg-accent-hover transition-colors"
              >
                Release Funds
              </button>
            </div>
          )
        case 'released':
          return (
            <div className="flex items-center gap-2 text-status-completed-text">
              <CheckCircle2 className="w-4 h-4" />
              <span className="text-sm font-medium">Funds Released</span>
            </div>
          )
        default:
          return null
      }
    }

    // If previous milestone is not released, show disabled state
    return (
      <div className="flex items-center gap-2 text-secondary-text">
        <LockKeyhole className="w-4 h-4" />
        <span className="text-sm font-medium">Complete Previous Milestone</span>
      </div>
    )
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-status-active-bg text-status-active-text">Active</span>
      case 'completed':
        return (
          <span className="px-2 py-1 text-xs font-medium rounded-full bg-status-completed-bg text-status-completed-text">Completed</span>
        )
      default:
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-accent-light/20 text-accent-dark">{status}</span>
    }
  }

  return (
    <div className="min-h-screen bg-main-bg p-6">
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="lg:w-1/3">
          <div className="bg-card-bg p-4 rounded-xl shadow-md border border-card-border mb-6">
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-text w-4 h-4" />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full rounded-lg border border-card-border bg-card-bg text-primary-text text-sm focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-colors"
              />
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  filter === 'all' ? 'bg-accent text-button-text' : 'bg-accent-light/10 text-accent-dark hover:bg-accent-light/20'
                }`}
              >
                All
              </button>
              <button
                onClick={() => setFilter('active')}
                className={`px-3 py-1 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  filter === 'active' ? 'bg-accent text-button-text' : 'bg-accent-light/10 text-accent-dark hover:bg-accent-light/20'
                }`}
              >
                Active
              </button>
              <button
                onClick={() => setFilter('completed')}
                className={`px-3 py-1 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  filter === 'completed' ? 'bg-accent text-button-text' : 'bg-accent-light/10 text-accent-dark hover:bg-accent-light/20'
                }`}
              >
                Completed
              </button>
            </div>
          </div>

          <div className="space-y-4">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-accent"></div>
              </div>
            ) : filteredTransactions.length > 0 ? (
              filteredTransactions.map((transaction: Transaction) => (
                <TransactionCard key={transaction.tx_id} transaction={transaction} onClick={() => handleSelectTransaction(transaction)} />
              ))
            ) : (
              <div className="bg-card-bg rounded-xl shadow-md border border-card-border p-8 text-center">
                <Filter className="w-8 h-8 text-secondary-text mx-auto mb-2" />
                <p className="text-primary-text font-medium">No transactions found</p>
                <p className="text-secondary-text text-sm">Try changing your search or filter</p>
              </div>
            )}
          </div>
        </div>

        <div className="lg:w-2/3">
          {activeTransaction ? (
            <div className="bg-card-bg rounded-xl shadow-md border border-card-border overflow-hidden">
              <div className="p-6 border-b border-card-border">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <h2 className="text-xl font-semibold text-primary-text">
                        {/* Group crops by product name */}
                        {Object.values(
                          activeTransaction.crops.reduce((acc, crop) => {
                            if (!acc[crop.productName]) {
                              acc[crop.productName] = true
                            }
                            return acc
                          }, {} as Record<string, boolean>),
                        ).length > 1 ? (
                          <span>Multiple Products</span>
                        ) : (
                          <span>{activeTransaction.crops[0].productName}</span>
                        )}
                      </h2>
                      {getStatusBadge(activeTransaction.status)}
                    </div>
                    {/* Crop Details */}
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-2 mb-2">
                        {activeTransaction.crops.map((crop, idx) => (
                          <div key={idx} className="flex items-center">
                            <div className="flex items-center bg-accent-light/10 rounded-lg px-2 py-1">
                              <span className="text-xs font-medium text-primary-text">
                                {Object.values(
                                  activeTransaction.crops.reduce((acc, c) => {
                                    if (!acc[c.productName]) {
                                      acc[c.productName] = true
                                    }
                                    return acc
                                  }, {} as Record<string, boolean>),
                                ).length > 1 && <span className="mr-1 text-accent">{crop.productName}:</span>}
                                <span>
                                  {crop.quantity} {crop.unit}
                                </span>
                              </span>
                              {crop.crop_id && (
                                <span className="ml-1.5 text-xs text-accent border-l border-card-border pl-1.5">#{crop.crop_id}</span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="bg-accent-light/10 border border-accent rounded-lg px-4 py-3 mb-2 flex flex-col items-start">
                        <span className="text-xs text-accent font-semibold uppercase mb-1">Total Value</span>
                        <span className="text-2xl font-bold text-accent">KTT {activeTransaction.total_amount.toLocaleString()}</span>
                      </div>
                      <div className="flex gap-6 text-sm text-secondary-text">
                        <span>
                          <strong>Total Batches:</strong> {new Set(activeTransaction.crops.map((crop) => crop.crop_id)).size}
                        </span>
                        <span>
                          <strong>Total Quantity:</strong>{' '}
                          {Number(
                            activeTransaction.crops.reduce((total, crop) => {
                              if (crop.unit === 'tonnes' || crop.unit === 'tons') {
                                return total + (crop.quantity || 0)
                              } else if (crop.unit === 'kg' || crop.unit === 'kilograms') {
                                return total + (crop.quantity || 0) / 1000
                              } else {
                                return total
                              }
                            }, 0) || 0,
                          ).toFixed(3)}{' '}
                          tonnes
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end">
                    <p className="text-sm text-secondary-text">Created on</p>
                    <p className="text-sm font-medium text-primary-text">{new Date(activeTransaction.created_at).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-primary-text mb-4">Contract Details</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="p-4 bg-accent-light/5 rounded-lg border border-card-border hover:bg-accent-light/10 transition-colors duration-200">
                    <p className="text-sm font-medium text-secondary-text">Contract Address</p>
                    <p className="text-sm font-medium text-primary-text font-mono mt-1">{activeTransaction.contract_address}</p>
                  </div>

                  <div className="p-4 bg-accent-light/5 rounded-lg border border-card-border hover:bg-accent-light/10 transition-colors duration-200">
                    <p className="text-sm font-medium text-secondary-text">Seller Address</p>
                    <p className="text-sm font-medium text-primary-text font-mono mt-1">{ellipseAddress(activeTransaction.seller)}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-primary-text">Milestone Progress</h3>
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium text-accent">KTT {activeTransaction.release_amount.toLocaleString()}</span>
                    <span className="text-sm text-secondary-text">/ KTT {activeTransaction.total_amount.toLocaleString()} released</span>
                  </div>
                </div>

                <MilestoneProgress milestones={activeTransaction.milestones} />

                <div className="mt-6">
                  <div className="space-y-4">
                    {activeTransaction.milestones.map((milestone: Milestone) => (
                      <div
                        key={milestone.id}
                        className="p-4 border border-card-border bg-accent-light/5 rounded-lg hover:bg-accent-light/10 transition-colors duration-200"
                      >
                        <div className="flex justify-between items-center">
                          <div className="flex-1 mr-4">
                            <h4 className="font-medium text-primary-text truncate">{milestone.name}</h4>
                            <p className="text-sm text-secondary-text mt-1">{milestone.description}</p>
                          </div>
                          <p className="text-lg font-medium text-accent whitespace-nowrap">KTT {milestone.amount.toLocaleString()}</p>
                        </div>

                        <div className="mt-4 flex flex-wrap justify-between items-center gap-2">
                          <div className="flex items-center">
                            {milestone.completedDate && (
                              <p className="text-xs text-accent">Completed: {new Date(milestone.completedDate).toLocaleDateString()}</p>
                            )}
                          </div>

                          {renderMilestoneActions(milestone)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mt-8">
                  <h3 className="text-lg font-semibold text-primary-text mb-4">Documents</h3>

                  {activeTransaction.documents.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {activeTransaction.documents.map((document, index) => (
                        <div
                          key={index}
                          className="flex items-center p-4 bg-accent-light/5 rounded-lg border border-card-border cursor-pointer hover:bg-accent-light/10 transition-colors duration-200"
                        >
                          <FileText className="w-5 h-5 text-accent mr-3" />
                          <span className="text-sm font-medium text-primary-text">{document.filename}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-secondary-text">No documents available</p>
                  )}
                </div>

                {activeTransaction.status === 'active' && (
                  <div className="mt-8 bg-accent-light/5 p-4 rounded-lg border border-card-border">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-full bg-accent-light/10 text-accent">
                        <LockKeyhole className="w-5 h-5" />
                      </div>
                      <div>
                        <p className="font-medium text-primary-text">Funds in Escrow</p>
                        <p className="text-sm text-secondary-text mt-1">
                          KTT {(activeTransaction.total_amount - activeTransaction.release_amount).toLocaleString()} is currently locked in
                          the smart contract escrow.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {activeTransaction.status === 'completed' && (
                  <div className="mt-8 bg-status-completed-bg p-4 rounded-lg border border-status-completed-text">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-full bg-accent-light/10 text-status-completed-text">
                        <CheckCircle2 className="w-5 h-5" />
                      </div>
                      <div>
                        <p className="font-medium text-primary-text">Transaction Completed</p>
                        <p className="text-sm text-secondary-text mt-1">All funds have been released and the transaction is complete.</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-card-bg rounded-xl shadow-md border border-card-border p-12 text-center flex flex-col items-center justify-center h-500 sticky top-6">
              <Key className="w-12 h-12 text-secondary-text mb-4" />
              <h3 className="text-xl font-semibold text-primary-text mb-2">Select a Transaction</h3>
              <p className="text-secondary-text max-w-md mx-auto">
                Choose a transaction from the list to view details, manage escrow funds, and track milestones.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ProofOfUnlock
