from django.core.management.base import BaseCommand
from news.models import News

class Command(BaseCommand):
    help = 'Adds sample news articles about cocoa beans to the database'

    def handle(self, *args, **kwargs):
        news_articles = [
            {
                "title": "The Rise of Sustainable Cocoa Farming",
                "description": "In recent years, the demand for sustainable cocoa has surged as consumers become more conscious of the environmental and social impacts of chocolate production. Farmers are adopting agroforestry practices that not only improve cocoa yield but also enhance biodiversity. Organizations are working to provide training and resources to farmers, ensuring that cocoa production is both profitable and sustainable. This shift is crucial for the future of the cocoa industry, as it addresses issues such as deforestation and child labor.",
                "date": "2024-03-10",
                "article_link": "https://example.com/sustainable-cocoa-farming"
            },
            {
                "title": "Innovations in Cocoa Processing Technology",
                "description": "The cocoa industry is witnessing a technological revolution with the introduction of advanced processing techniques. New methods are being developed to enhance the flavor profiles of cocoa beans while reducing waste. Innovations such as cold-pressing and fermentation control are allowing chocolate makers to create unique products that cater to diverse consumer preferences. These advancements not only improve product quality but also increase the efficiency of cocoa processing, benefiting producers and consumers alike.",
                "date": "2024-03-12",
                "article_link": "https://example.com/cocoa-processing-innovations"
            },
            {
                "title": "Cocoa Beans: A Key Player in Climate Change Mitigation",
                "description": "Recent studies have highlighted the role of cocoa beans in climate change mitigation. Cocoa trees, when grown in agroforestry systems, can sequester significant amounts of carbon dioxide, contributing to global efforts to combat climate change. Researchers are advocating for the integration of cocoa cultivation with other crops to enhance carbon storage and improve soil health. This approach not only benefits the environment but also provides farmers with diversified income sources.",
                "date": "2024-03-15",
                "article_link": "https://example.com/cocoa-climate-change"
            },
            {
                "title": "Global Cocoa Market Trends 2024",
                "description": "The global cocoa market is experiencing significant shifts in 2024, with emerging markets showing increased demand for premium chocolate products. Market analysts predict a 15% growth in sustainable cocoa sourcing, while prices have stabilized following last year's volatility. West African producers are implementing new quality control measures to maintain their market leadership, and Asian markets are showing unprecedented growth in cocoa consumption.",
                "date": "2024-03-08",
                "article_link": "https://example.com/cocoa-market-trends-2024"
            },
            {
                "title": "Breakthrough in Cocoa Disease Resistance",
                "description": "Scientists have made a breakthrough in developing disease-resistant cocoa varieties through advanced genetic research. The new varieties show promising resistance to common diseases like black pod disease and witches' broom, which typically cause significant crop losses. This development could potentially increase global cocoa production by 25% and provide more stable income for farmers in cocoa-producing regions.",
                "date": "2024-03-05",
                "article_link": "https://example.com/cocoa-disease-resistance"
            }
        ]

        try:
            for article in news_articles:
                News.objects.create(
                    title=article['title'],
                    description=article['description'],
                    date=article['date'],
                    article_link=article['article_link']
                )
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully created news article: {article["title"]}')
                )

            self.stdout.write(
                self.style.SUCCESS('Successfully added all sample news articles')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error occurred: {str(e)}')
            )
