import { useWallet } from '@txnlab/use-wallet-react'
import { CheckCircle2, LayoutDashboard } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '../../stores/authStore'

interface CompleteScreenProps {
  handleBack: () => void
  selectedAccount: string
}

/**
 * Displays a confirmation screen summarizing completed onboarding steps and providing navigation options.
 *
 * Renders a summary of wallet connection, account selection, smart contract approval, and asset opt-in, along with buttons to proceed to the dashboard, watch a tutorial, or go back.
 *
 * @param selectedAccount - The address of the selected account to display in the summary.
 * @param handleBack - Callback invoked when the "Back" button is clicked.
 */
export default function CompleteScreen({ handleBack, selectedAccount }: CompleteScreenProps) {
  const navigate = useNavigate()
  const { wallets } = useWallet()
  const { updateUser } = useAuthStore()

  const handleGoToDashboard = () => {
    // Mark onboarding as completed
    updateUser({ onboarding_completed: true })
    navigate('/dashboard')
  }
  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <div className="w-16 h-16 bg-button-bg rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle2 className="h-8 w-8 text-button-text" />
        </div>
        <h2 className="text-3xl font-bold text-primary-text mb-4">Setup Complete!</h2>
        <p className="text-primary-text opacity-60">You're all set to start using FarmChain. Here's what you've completed:</p>
      </div>

      <div className="bg-alt-bg p-6 rounded-lg space-y-4 shadow-md">
        <div className="flex items-center gap-3 text-primary-text">
          <CheckCircle2 className="h-5 w-5" />
          <span>Wallet connected successfully</span>
        </div>
        <div className="flex items-center gap-3 text-primary-text">
          <CheckCircle2 className="h-5 w-5" />
          <span>Account selected: {wallets.find((wallet) => wallet.accounts[0].address === selectedAccount)?.metadata.name}</span>
        </div>
        <div className="flex items-center gap-3 text-primary-text">
          <CheckCircle2 className="h-5 w-5" />
          <span>Smart contract approved</span>
        </div>
        <div className="flex items-center gap-3 text-primary-text">
          <CheckCircle2 className="h-5 w-5" />
          <span>2 assets opted-in</span>
        </div>
      </div>

      <div className="flex flex-col items-center gap-4">
        <button
          onClick={handleGoToDashboard}
          className="bg-button-bg text-button-text px-8 py-3 rounded-lg hover:bg-button-bg-hover transition-colors duration-300 flex items-center"
        >
          <LayoutDashboard className="h-5 w-5 mr-2" />
          Go to Dashboard
        </button>
        <button
          onClick={() => alert('Starting tutorial...')}
          className="text-primary-text opacity-60 hover:text-primary-text hover:opacity-100 transition-colors duration-300"
        >
          Watch Quick Tutorial
        </button>
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-primary-text/10 text-primary-text opacity-60 hover:text-primary-text hover:border-primary-text  duration-300"
        >
          Back
        </button>
      </div>
    </div>
  )
}
