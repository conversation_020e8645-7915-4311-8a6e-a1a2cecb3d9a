# Generated by Django 5.2 on 2025-05-06 11:38

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("crops", "0009_rename_units_crops_unit"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="StatusTable",
            fields=[
                (
                    "id",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Storage"),
                            (2, "Ready"),
                            (3, "In Progress"),
                            (4, "Sold"),
                        ],
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=32, unique=True)),
            ],
            options={
                "verbose_name": "Status",
                "verbose_name_plural": "Statuses",
            },
        ),
        migrations.CreateModel(
            name="CropStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Storage"),
                            (2, "Ready"),
                            (3, "In Progress"),
                            (4, "Sold"),
                        ],
                        help_text="The status of the crop (e.g., Storage, Ready, In Progress, Sold).",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="When this status record was created.",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="When this status record was last updated.",
                    ),
                ),
                (
                    "crop",
                    models.ForeignKey(
                        help_text="The crop for which the status is tracked.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="status_records",
                        to="crops.crops",
                    ),
                ),
                (
                    "trader",
                    models.ForeignKey(
                        help_text="The trader (user) for whom the crop status is tracked.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="crop_statuses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Crop Status Records",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CropQuantity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "total_quantity_to_date",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total quantity of this crop transacted by the trader since first joining the platform.",
                        max_digits=16,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "total_quantity_in_storage",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total quantity of this crop currently in storage for the trader.",
                        max_digits=16,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "ready_to_sell_quantity",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Quantity of this crop that is ready to be sold. Cannot exceed total quantity.",
                        max_digits=16,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "sold_quantity",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total quantity of this crop that has been sold. Cannot exceed total quantity.",
                        max_digits=16,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "total_quantity_batches",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of batches that make up the total quantity to date.",
                    ),
                ),
                (
                    "storage_batches",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of batches that make up the total quantity in storage.",
                    ),
                ),
                (
                    "ready_to_sell_batches",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of batches that make up the ready to sell quantity.",
                    ),
                ),
                (
                    "sold_batches",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of batches that make up the sold quantity.",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="Timestamp for when the quantities were last updated.",
                    ),
                ),
                (
                    "crop",
                    models.ForeignKey(
                        help_text="The crop type for which the quantities are tracked.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quantities",
                        to="crops.crops",
                    ),
                ),
                (
                    "trader",
                    models.ForeignKey(
                        help_text="The trader (user) who owns these crop quantities.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="crop_quantities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Crop Quantities",
                "ordering": ["-updated_at"],
                "unique_together": {("trader", "crop")},
            },
        ),
    ]
