import apiClient from '@/services/apiClient'
import { proofOfUnlockFactory } from '@/utils/algo'
import { VITE_ALGORAND_ASSET_ID_KTT } from '@/utils/variable'
import { TransactionSigner } from 'algosdk'
import { create } from 'zustand'

export interface Milestone {
  id: number
  name: string
  description: string
  amount: number
  status: 'pending' | 'completed' | 'verified' | 'released' | 'disputed' | 'shipped'
  completedDate?: string
}

export interface Transaction {
  tx_id: string
  contract_address: string
  buyer: string
  seller: string
  total_amount: number
  release_amount: number
  status: 'pending' | 'active' | 'completed' | 'disputed' | 'canceled'
  last_updated: string
  created_at: string
  crops: {
    productName: string
    crop_id: string
    quantity: number
    unit: string
  }[]
  milestones: Milestone[]
  documents: { filename: string; fileType: string; hash: string }[]
}

interface TransactionState {
  transactions: Transaction[]
  activeTransaction: Transaction | null
  loading: boolean
  error: string | null
  fetchTransactions: () => Promise<void>
  setActiveTransaction: (id: string) => void
  approveMilestone: (transactionId: string, milestoneId: number, activeAddress: string, signer: TransactionSigner) => Promise<void>
  releaseFunds: (transactionId: string, milestoneId: number, activeAddress: string, signer: TransactionSigner) => Promise<void>
}

export const useTransactionStore = create<TransactionState>((set, get) => ({
  transactions: [],
  activeTransaction: null,
  loading: false,
  error: null,

  fetchTransactions: async () => {
    set({ loading: true, error: null })

    try {
      const response = await apiClient.get('/proof-of-unlock/proof-of-unlock/user/')
      // Add default productName if not present
      const transactionsWithDefaults = response.data.map((tx: Transaction) => ({
        ...tx,
        crops: tx.crops.map((crop) => ({
          ...crop,
          productName: crop.productName || 'Cocoa Beans',
        })),
      }))
      set({ transactions: transactionsWithDefaults, loading: false })
    } catch (error) {
      set({ error: 'Failed to fetch transactions', loading: false })
    }
  },

  setActiveTransaction: (id: string) => {
    const transaction = get().transactions.find((tx) => tx.tx_id === id) || null
    set({ activeTransaction: transaction })
  },

  approveMilestone: async (transactionId: string, milestoneId: number, activeAddress: string, signer: TransactionSigner) => {
    set({ loading: true, error: null })
    console.log('Approve milestone', transactionId, milestoneId)

    try {
      // Get the transaction and log first crop ID
      const transaction = get().transactions.find((tx) => tx.tx_id === transactionId)
      let factory
      if (transaction && transaction.crops.length > 0) {
        console.log('First crop ID:', transaction.crops[0].crop_id)
        factory = await proofOfUnlockFactory(transaction.crops[0].crop_id.toString(), activeAddress, signer)
        console.log('Factory', factory.appClient.appId)
      }
      const index = transaction?.milestones.findIndex((milestone) => milestone.id === milestoneId)
      console.log('Index', index)
      if (factory && typeof index === 'number') {
        await factory.appClient.send.approveMilestone({
          args: {
            milestoneIndex: BigInt(index),
          },
        })
      }
      await apiClient.put('/proof-of-unlock/milestones/update/', {
        milestone: { id: milestoneId, status: 'completed' },
        tx_id: transactionId,
      })

      set((state) => {
        const updatedTransactions = state.transactions.map((transaction) => {
          if (transaction.tx_id === transactionId) {
            const updatedMilestones = transaction.milestones.map((milestone) => {
              if (milestone.id === milestoneId) {
                return { ...milestone, status: 'verified' as const, completedDate: new Date().toISOString() }
              }
              return milestone
            })

            return {
              ...transaction,
              milestones: updatedMilestones,
              last_updated: new Date().toISOString(),
            }
          }
          return transaction
        })

        // Update active transaction if needed
        let activeTransaction = state.activeTransaction
        if (activeTransaction && activeTransaction.tx_id === transactionId) {
          activeTransaction = updatedTransactions.find((tx) => tx.tx_id === transactionId) || null
        }

        return {
          transactions: updatedTransactions,
          activeTransaction,
          loading: false,
        }
      })
    } catch (error) {
      set({ error: 'Failed to approve milestone', loading: false })
    }
  },

  releaseFunds: async (transactionId: string, milestoneId: number, activeAddress: string, signer: TransactionSigner) => {
    set({ loading: true, error: null })

    try {
      const transaction = get().transactions.find((tx) => tx.tx_id === transactionId)
      let factory
      if (transaction && transaction.crops.length > 0) {
        console.log('First crop ID:', transaction.crops[0].crop_id)
        factory = await proofOfUnlockFactory(transaction.crops[0].crop_id.toString(), activeAddress, signer)
        console.log('Factory', factory.appClient.appId)
      }
      const index = transaction?.milestones.findIndex((milestone) => milestone.id === milestoneId)
      console.log('Index', index)
      if (factory && typeof index === 'number') {
        await factory.appClient.send.releaseFunds({
          args: {
            asset: BigInt(VITE_ALGORAND_ASSET_ID_KTT),
          },
        })
      }

      await apiClient.put('/proof-of-unlock/milestones/update/', {
        milestone: { id: milestoneId, status: 'released' },
        tx_id: transactionId,
        status: index === 2 ? 'completed' : 'active',
      })

      set((state) => {
        const updatedTransactions = state.transactions.map((transaction) => {
          if (transaction.tx_id === transactionId) {
            const updatedMilestones = transaction.milestones.map((milestone) => {
              if (milestone.id === milestoneId) {
                return { ...milestone, status: 'released' as const }
              }
              return milestone
            })

            // Calculate released amount
            const releaseAmount = updatedMilestones.filter((m) => m.status === 'released').reduce((sum, m) => sum + Number(m.amount), 0)

            // Check if all milestones are released
            const allCompleted = updatedMilestones.every((m) => m.status === 'released')

            return {
              ...transaction,
              milestones: updatedMilestones,
              release_amount: releaseAmount,
              status: allCompleted ? ('completed' as const) : transaction.status,
              last_updated: new Date().toISOString(),
            }
          }
          return transaction
        })

        // Update active transaction if needed
        let activeTransaction = state.activeTransaction
        if (activeTransaction && activeTransaction.tx_id === transactionId) {
          activeTransaction = updatedTransactions.find((tx) => tx.tx_id === transactionId) || null
        }

        return {
          transactions: updatedTransactions,
          activeTransaction,
          loading: false,
        }
      })
    } catch (error) {
      set({ error: 'Failed to release funds', loading: false })
    }
  },
}))
