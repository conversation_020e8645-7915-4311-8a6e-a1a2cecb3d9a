import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { 
  useAuthStore, 
  useIsOnboardingCompleted, 
  useRequiresOnboarding,
  useUser 
} from '../stores/authStore'

/**
 * Demo component showing the complete onboarding flow logic
 * This demonstrates how the system prevents access to onboarding
 * when user already has account_address
 */
const OnboardingFlowDemo = () => {
  const navigate = useNavigate()
  const { updateUser, logout } = useAuthStore()
  const user = useUser()
  const isOnboardingCompleted = useIsOnboardingCompleted()
  const requiresOnboarding = useRequiresOnboarding()

  // Automatically redirect if user has already completed onboarding
  useEffect(() => {
    if (isOnboardingCompleted) {
      console.log('User has already completed onboarding, redirecting to dashboard...')
      navigate('/dashboard')
    }
  }, [isOnboardingCompleted, navigate])

  // If user doesn't need onboarding, don't render anything
  if (!requiresOnboarding || isOnboardingCompleted) {
    return (
      <div className="p-6 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">
          Onboarding Already Completed
        </h3>
        <p className="text-yellow-700 mb-4">
          You have already connected your wallet and completed onboarding.
        </p>
        <button
          onClick={() => navigate('/dashboard')}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Go to Dashboard
        </button>
      </div>
    )
  }

  const handleWalletConnected = (walletAddress: string) => {
    console.log('Connecting wallet:', walletAddress)
    
    // Update user with the wallet address
    updateUser({ account_address: walletAddress })
    
    // Navigate to dashboard after onboarding is complete
    navigate('/dashboard')
  }

  const handleOptIn = () => {
    updateUser({ opt_in: true })
  }

  // Demo function to simulate removing wallet (for testing)
  const handleRemoveWallet = () => {
    updateUser({ account_address: '' })
  }

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Complete Your Onboarding</h2>
      
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded">
        <h3 className="font-semibold text-blue-800 mb-2">Current Status:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• User: {user?.name || 'Unknown'}</li>
          <li>• Email: {user?.email || 'Unknown'}</li>
          <li>• Role: {user?.role || 'Unknown'}</li>
          <li>• Account Address: {user?.account_address || 'Not connected'}</li>
          <li>• Opt-in: {user?.opt_in ? 'Yes' : 'No'}</li>
          <li>• Requires Onboarding: {requiresOnboarding ? 'Yes' : 'No'}</li>
          <li>• Onboarding Completed: {isOnboardingCompleted ? 'Yes' : 'No'}</li>
        </ul>
      </div>

      <p className="text-gray-600 mb-6">
        Please connect your wallet to continue using the platform.
      </p>
      
      <div className="space-y-4">
        <button
          onClick={() => handleWalletConnected('3ZM6HDEYYGULW4XVFXTT3EU3SFZECMODSLJYMGDMYOB7K5BAYWCAPAJL6E')}
          className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          Connect Wallet (Demo)
        </button>
        
        <button
          onClick={handleOptIn}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Complete Opt-in
        </button>

        {/* Demo buttons for testing */}
        <div className="border-t pt-4 mt-6">
          <p className="text-sm text-gray-500 mb-2">Demo Actions:</p>
          <div className="space-y-2">
            <button
              onClick={handleRemoveWallet}
              className="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-sm"
            >
              Remove Wallet (Test)
            </button>
            <button
              onClick={logout}
              className="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
      
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-800">
          <strong>Note:</strong> Once you connect your wallet, you won't be able to access this onboarding page again unless you remove your wallet address.
        </p>
      </div>
    </div>
  )
}

export default OnboardingFlowDemo
