import { Eye, EyeOff } from 'lucide-react'
import { useState } from 'react'

interface FormInputProps {
  label: string
  type: string
  value: string
  onChange: (value: string) => void
  error?: string
  placeholder?: string
}

const FormInput = ({ label, type, value, onChange, error, placeholder }: FormInputProps) => {
  const [showPassword, setShowPassword] = useState(false)
  const inputType = type === 'password' && showPassword ? 'text' : type

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-primary-text">{label}</label>
      <div className="relative">
        <input
          type={inputType}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`w-full px-4 py-2 bg-alt-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-button-bg ${
            error ? 'border-button-danger' : 'border-border-primary'
          }`}
        />
        {type === 'password' && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-primary-text hover:text-button-bg"
          >
            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
          </button>
        )}
      </div>
      {error && <p className="text-button-danger text-sm">{error}</p>}
    </div>
  )
}

export default FormInput
