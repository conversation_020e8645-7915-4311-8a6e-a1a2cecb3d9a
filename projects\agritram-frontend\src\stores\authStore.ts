import { create } from 'zustand';

type UserType = 'buyer' | 'seller' | null;

interface AuthState {
  isAuthenticated: boolean;
  userType: UserType;
  userName: string | null;
  loading: boolean;
  walletAddress: string | null;
  login: (userType: UserType, userName: string, walletAddress: string) => void;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  isAuthenticated: false,
  userType: null,
  userName: null,
  loading: true,
  walletAddress: null,

  login: (userType, userName, walletAddress) => {
    localStorage.setItem('userType', userType || '');
    localStorage.setItem('userName', userName);
    localStorage.setItem('walletAddress', walletAddress);
    
    set({
      isAuthenticated: true,
      userType,
      userName,
      walletAddress,
      loading: false,
    });
  },

  logout: () => {
    localStorage.removeItem('userType');
    localStorage.removeItem('userName');
    localStorage.removeItem('walletAddress');
    
    set({
      isAuthenticated: false,
      userType: null,
      userName: null,
      walletAddress: null,
    });
  },

  checkAuth: async () => {
    set({ loading: true });
    
    // Simulate a small delay to check auth
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const userType = localStorage.getItem('userType') as UserType;
    const userName = localStorage.getItem('userName');
    const walletAddress = localStorage.getItem('walletAddress');
    
    if (userType && userName && walletAddress) {
      set({
        isAuthenticated: true,
        userType,
        userName,
        walletAddress,
      });
    }
    
    set({ loading: false });
  },
}));