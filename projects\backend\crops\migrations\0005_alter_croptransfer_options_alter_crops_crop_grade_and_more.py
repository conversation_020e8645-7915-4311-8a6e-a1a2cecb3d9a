# Generated by Django 5.2 on 2025-04-25 11:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("crops", "0004_remove_croptransfer_crop_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="croptransfer",
            options={"ordering": ["-timestamp"]},
        ),
        migrations.AlterField(
            model_name="crops",
            name="crop_grade",
            field=models.CharField(
                choices=[("A", "Grade A"), ("B", "Grade B"), ("C", "Grade C")],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name="crops",
            name="crop_id",
            field=models.BigAutoField(primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name="crops",
            name="fertilizers_used",
            field=models.ManyToManyField(
                blank=True, related_name="crops", to="crops.fertilizer"
            ),
        ),
        migrations.AlterField(
            model_name="croptransfer",
            name="crop",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="transfers",
                to="crops.crops",
            ),
        ),
        migrations.AlterField(
            model_name="croptransfer",
            name="from_user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sent_transfers",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="croptransfer",
            name="status",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("COMPLETED", "Completed"),
                    ("CANCELLED", "Cancelled"),
                ],
                default="PENDING",
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name="croptransfer",
            name="to_user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="received_transfers",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="croptransfer",
            name="transaction_id",
            field=models.UUIDField(editable=False, primary_key=True, serialize=False),
        ),
    ]
