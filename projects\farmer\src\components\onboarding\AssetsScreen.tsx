import { useToast } from '@/components/ui/use-toast'
import { ellipseAddress } from '@/utils/ellipseAddress'
import {
  VITE_ALGORAND_ASSET_ID_KCT,
  VITE_ALGORAND_ASSET_ID_KTT,
  VITE_ALGORAND_KMD_PASSWORD,
  VITE_ALGORAND_KMD_PORT,
  VITE_ALGORAND_KMD_SERVER,
  VITE_ALGORAND_KMD_TOKEN,
  VITE_ALGORAND_KMD_WALLET,
} from '@/utils/variable'
import { useWallet } from '@txnlab/use-wallet-react'
import algosdk from 'algosdk'
import { Coins, Loader2 } from 'lucide-react'
import { useEffect, useState } from 'react'
import { getAlgodConfigFromViteEnvironment } from '../../utils/network/getAlgoClientConfigs'

interface AssetScreenProps {
  handleNext: () => void
  handleBack: () => void
  isLoading: boolean
  setIsLoading: (value: boolean) => void
  selectedAccount: string
  isOptedIn: boolean
  setIsOptedIn: (value: boolean) => void
}

/**
 * React component for managing Algorand asset opt-in within a wallet interface.
 *
 * Displays available assets, checks opt-in status for two specific Algorand assets, and facilitates the opt-in process by constructing, signing, and submitting grouped asset opt-in transactions. Handles both KMD-managed and wallet-based signing, provides user feedback, and manages navigation between steps.
 *
 * @param selectedAccount - The currently selected account address for asset opt-in.
 */
export default function AssetsScreen({
  handleNext,
  handleBack,
  setIsOptedIn,
  isLoading,
  setIsLoading,
  selectedAccount,
  isOptedIn,
}: AssetScreenProps) {
  const { activeAddress, activeWallet, signTransactions } = useWallet()
  const { toast } = useToast()
  const [algodClient, setAlgodClient] = useState<algosdk.Algodv2 | null>(null)
  const [kmdClient, setKmdClient] = useState<algosdk.Kmd | null>(null)
  const [privateKey, setPrivateKey] = useState<Uint8Array | null>(null)
  const [kmdAddresses, setKmdAddresses] = useState<string[]>([])
  const [useKmd, setUseKmd] = useState<boolean>(false)
  const kmdConfig = {
    server: VITE_ALGORAND_KMD_SERVER || 'http://localhost',
    port: VITE_ALGORAND_KMD_PORT || 4002,
    token: VITE_ALGORAND_KMD_TOKEN || 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
    wallet: VITE_ALGORAND_KMD_WALLET || 'unencrypted-default-wallet',
    password: VITE_ALGORAND_KMD_PASSWORD || '',
  }

  // Initialize Algorand and KMD clients
  useEffect(() => {
    try {
      // Initialize Algod client
      const algodConfig = getAlgodConfigFromViteEnvironment()
      const algod = new algosdk.Algodv2(algodConfig.token as string, algodConfig.server, algodConfig.port)
      setAlgodClient(algod)

      // Initialize KMD client

      const kmd = new algosdk.Kmd(kmdConfig.token as string, kmdConfig.server, kmdConfig.port)
      setKmdClient(kmd)

      // Get addresses from KMD
      const getKmdAddresses = async () => {
        try {
          // Initialize wallet handle
          const walletResponse = await kmd.initWalletHandle(kmdConfig.wallet, kmdConfig.password)
          const walletHandle = walletResponse.wallet_handle_token

          // List keys in wallet
          const keysResponse = await kmd.listKeys(walletHandle)

          // Release wallet handle
          await kmd.releaseWalletHandle(walletHandle)

          setKmdAddresses(keysResponse.addresses)

          // Debug
          console.log('KMD addresses:', keysResponse.addresses)
        } catch (error) {
          console.error('Error getting KMD addresses:', error)
        }
      }

      getKmdAddresses()
    } catch (error) {
      console.error('Error initializing clients:', error)
      toast({ title: 'Error!', description: 'Error initializing clients. Check your environment variables.' })
    }
  }, [kmdConfig.password, kmdConfig.port, kmdConfig.server, kmdConfig.token, kmdConfig.wallet, toast])

  // Check if active address is in KMD wallet
  useEffect(() => {
    if (activeAddress && kmdAddresses.length > 0) {
      const addressInKmd = kmdAddresses.includes(activeAddress)
      setUseKmd(addressInKmd)

      // Debug
      console.log('Active address in KMD:', addressInKmd)

      if (addressInKmd) {
        // Get private key for the active address
        const getPrivateKey = async () => {
          if (!kmdClient) return

          try {
            // Initialize wallet handle
            const walletResponse = await kmdClient.initWalletHandle(kmdConfig.wallet, kmdConfig.password)
            const walletHandle = walletResponse.wallet_handle_token

            // Export key
            const exportResponse = await kmdClient.exportKey(walletHandle, kmdConfig.password, activeAddress)

            // Release wallet handle
            await kmdClient.releaseWalletHandle(walletHandle)

            setPrivateKey(exportResponse.private_key)
            console.log('Private key retrieved successfully')
          } catch (error) {
            console.error('Error getting private key:', error)
            setPrivateKey(null)
          }
        }

        getPrivateKey()
      }
    }
  }, [activeAddress, kmdAddresses, kmdClient, kmdConfig.password, kmdConfig.wallet])

  // Check if user has already opted in to the asset
  useEffect(() => {
    const checkOptInStatus = async () => {
      if (!activeAddress || !algodClient) {
        setIsOptedIn(false)
        return
      }

      try {
        const accountInfo = await algodClient.accountInformation(activeAddress).do()
        const assets = (accountInfo['assets'] || []).map((a) => ({
          'asset-id': a.assetId,
          amount: a.amount,
          'is-frozen': a.isFrozen,
        }))
        const asset1 = assets.find((a) => a['asset-id'] === VITE_ALGORAND_ASSET_ID_KCT)
        const asset2 = assets.find((a) => a['asset-id'] === VITE_ALGORAND_ASSET_ID_KTT)

        setIsOptedIn(!!asset1)
        setIsOptedIn(!!asset2)
      } catch (error) {
        console.error('Error checking opt-in status:', error)
        setIsOptedIn(false)
      }
    }

    checkOptInStatus()
  }, [activeAddress, setIsOptedIn, algodClient])

  const handleAssetOptIn = async () => {
    setIsLoading(true)
    try {
      if (!activeAddress) {
        toast({ title: 'Error!', description: 'Please connect your wallet first' })
        return
      }

      if (!algodClient) {
        toast({ title: 'Error!', description: 'Algod client not initialized. Check your environment variables.' })
        return
      }

      if (isOptedIn) {
        toast({ title: 'Info', description: 'Assets already opted in' })
        handleNext()
        return
      }

      const params = await algodClient.getTransactionParams().do()

      const txn1 = algosdk.makeAssetTransferTxnWithSuggestedParamsFromObject({
        sender: activeAddress,
        receiver: activeAddress,
        assetIndex: parseInt(VITE_ALGORAND_ASSET_ID_KTT),
        amount: 0,
        suggestedParams: params,
      })

      const txn2 = algosdk.makeAssetTransferTxnWithSuggestedParamsFromObject({
        sender: activeAddress,
        receiver: activeAddress,
        assetIndex: parseInt(VITE_ALGORAND_ASSET_ID_KCT),
        amount: 0,
        suggestedParams: params,
      })

      // Group the transactions
      const txns = [txn1, txn2]
      algosdk.assignGroupID(txns)

      try {
        let signedTxns

        if (useKmd && privateKey) {
          toast({ title: 'Info', description: 'Signing transactions with KMD...' })
          signedTxns = txns.map((txn) => algosdk.signTransaction(txn, privateKey))

          // Submit the transactions
          toast({ title: 'Info', description: 'Submitting transactions...' })
          const { txid } = await algodClient.sendRawTransaction(signedTxns.map((stxn) => stxn.blob)).do()

          // Wait for confirmation
          toast({ title: 'Info', description: 'Waiting for confirmation...' })
          await algosdk.waitForConfirmation(algodClient, txid, 5)
        } else if (signTransactions) {
          // Sign with wallet
          toast({ title: 'Info', description: 'Please sign the transactions with your wallet...' })
          const encodedTxns = txns.map((txn) => algosdk.encodeUnsignedTransaction(txn))
          signedTxns = await signTransactions(encodedTxns)

          // Submit the transactions
          toast({ title: 'Info', description: 'Submitting transactions...' })
          const { txid } = await algodClient.sendRawTransaction(signedTxns.filter((tx): tx is Uint8Array => tx !== null)).do()

          // Wait for confirmation
          toast({ title: 'Info', description: 'Waiting for confirmation...' })
          await algosdk.waitForConfirmation(algodClient, txid, 5)
        } else {
          throw new Error('No signing method available')
        }

        toast({ title: 'Success!', description: 'Successfully opted in to both assets!' })
        // setIsOptedIn(true) TODO::
        handleNext()
      } catch (signError) {
        console.error('Error signing transactions:', signError)
        toast({
          title: 'Error!',
          description: `Error signing transactions: ${signError instanceof Error ? signError.message : String(signError)}`,
        })
      }
    } catch (error) {
      console.error('Asset opt-in failed:', error)
      toast({
        title: 'Error!',
        description: `Asset opt-in failed: ${error instanceof Error ? error.message : String(error)}`,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-primary-text mb-4">Asset Opt-in</h2>
        <p className="text-primary-text opacity-60">Select the farm tokens and assets you'd like to use with FarmChain.</p>
      </div>

      <div className="bg-alt-bg shadow-md p-6 rounded-lg space-y-6">
        <div className="space-y-4">
          <div className="flex items-start gap-4">
            <Coins className="h-6 w-6 text-link-text mt-1" />
            <div>
              <h3 className="text-primary-text font-semibold mb-2">Available Assets</h3>
              <p className="text-primary-text opacity-60">Each asset requires a small balance reservation of 0.1 ALGO.</p>
            </div>
          </div>

          <div className="space-y-3">
            {[
              { id: 'KTT', name: 'Stable Token', desc: 'For tokenization' },
              { id: 'KCT', name: 'Investment Token', desc: 'For Investments and Loans' },
            ].map((asset) => (
              <label key={asset.id} className="flex items-start gap-3 p-3 bg-alt-bg rounded-lg border border-border-primary">
                <div>
                  <div className="flex items-center gap-2">
                    <span className="text-primary-text font-medium">{asset.name}</span>
                    <span className="text-sm text-primary-text opacity-60 px-2 py-0.5 bg-alt-bg rounded">{asset.id}</span>
                  </div>
                  <p className="text-sm text-primary-text opacity-60 mt-1">{asset.desc}</p>
                </div>
              </label>
            ))}
          </div>

          <div className="border-t border-border-primary pt-4">
            {isOptedIn ? <span className="text-primary-text font-semibold mb-5 text-center">Asset already opted in</span> : <></>}
            <h3 className="text-primary-text font-semibold mb-3">Transaction Summary:</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-primary-text opacity-60">Selected Assets:</span>
                <span className="text-primary-text">2</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-primary-text opacity-60">Account:</span>
                <span className="text-primary-text font-mono">
                  {ellipseAddress(activeWallet?.accounts?.find((a: { address: string }) => a.address === selectedAccount)?.address)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-primary-text/10 text-primary-text opacity-60 hover:text-primary-text hover:border-primary-text  duration-300"
        >
          Back
        </button>
        <button
          onClick={isOptedIn ? handleNext : handleAssetOptIn}
          disabled={isLoading}
          className="bg-button-bg text-button-text px-6 py-2 rounded-lg hover:bg-button-bg-hover transition-colors duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
              <Loader2 className="animate-spin h-5 w-5 mr-2" />
              Processing...
            </>
          ) : isOptedIn ? (
            <>Next</>
          ) : (
            <>
              <Coins className="h-5 w-5 mr-2" />
              Opt-in to Assets
            </>
          )}
        </button>
      </div>
    </div>
  )
}
