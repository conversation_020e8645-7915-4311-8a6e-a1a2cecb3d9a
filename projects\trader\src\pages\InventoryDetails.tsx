import apiClient from '@/services/apiClient'
import { Batch } from '@/utils/types'
import { useEffect, useState } from 'react'
import DataGrid from '../components/DataGrid/DataGrid'

const InventoryDetails = () => {
  const [cropStatus, setCropStatus] = useState<Batch[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Replace 'your_token' with the actual token
    apiClient
      .get('/inventory/crop-status/')
      .then((response) => {
        setCropStatus(response.data)
        setLoading(false)
      })
      .catch((error) => {
        // Handle error (e.g., not authenticated)
        console.error(error)
        setLoading(false)
      })
  }, [])

  if (loading) return <div className="min-h-screen bg-main-bg flex items-center justify-center text-primary-text">Loading...</div>

  return (
    <div className="min-h-screen bg-main-bg">
      <main className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-primary-text">Crop Batches</h2>
        </div>

        <DataGrid batches={cropStatus} />
      </main>
    </div>
  )
}

export default InventoryDetails
