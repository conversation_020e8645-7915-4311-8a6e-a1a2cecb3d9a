import type { Config } from 'tailwindcss'
import tailwindcssAnimate from 'tailwindcss-animate'

export default {
  darkMode: ['class'],
  content: ['./pages/**/*.{ts,tsx}', './components/**/*.{ts,tsx}', './app/**/*.{ts,tsx}', './src/**/*.{ts,tsx}'],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        'main-bg': '#FFF5EA', // Main Background
        'alt-bg': '#FFFFFF', // Alternative Section Background
        'footer-bg': '#FFFFFF', // Footer Background
        'pop-over-bg': '#000000', // Card Background

        // Dark Theme Colors
        'dark-bg': '#212121', // Dark background
        'dark-card': '#303030', // Dark card background
        'dark-border': '#424242', // Dark border color
        'dark-text': '#BDBDBD', // Dark text color
        'dark-text-hover': '#FFFFFF', // Dark text hover color

        // Text Colors
        'primary-text': '#000000', // Primary Text
        'secondary-text': '#2D4D31', // Secondary Text
        'link-text': '#D36C3C', // Link Text
        'link-text-hover': '#E65100', // Link Text Hover
        'white-text': '#FFFFFF', // White Text

        // Button Colors
        'button-bg': '#2D4D31', // Button Background
        'button-bg-hover': 'rgba(45, 77, 49, 0.8)', // Button Background
        'button-text': '#FFFFFF', // Button Text
        'button-danger': '#EF4444', // Danger button (red-500)
        'button-danger-hover': '#DC2626', // Danger button hover (red-600)

        // Border Colors
        'border-primary': '#2D4D31', // Primary Border Color
        'border-dotted': 'rgba(45, 77, 49, 0.7)', // Border with 70% opacity
        'border-error': '#EF4444', // Error Border Color
        'border-success': '#00E676', // Success Border Color
        'border-warning': '#FBBF24', // Warning Border Color

        // Password Strength Colors
        'password-weak': '#EF4444', // Red for weak passwords
        'password-medium': '#EAB308', // Yellow for medium passwords
        'password-strong': '#00E676', // Green for strong passwords
        'password-inactive': 'rgba(128, 128, 128, 0.5)', // Half-white for inactive indicators

        // Toast Colors
        'toast-bg': '#FFFFFF', // Toast Background Color
        'toast-text': '#000000', // Toast Text Color

        // Status Colors
        'status-active-bg': '#E8F5E9', // Light green background for active status
        'status-active-text': '#2E7D32', // Dark green text for active status
        'status-active-border': 'rgba(46, 125, 50, 0.2)', // Border for active status
        'status-completed-bg': '#E3F2FD', // Light blue background for completed status
        'status-completed-text': '#1565C0', // Dark blue text for completed status
        'status-completed-border': 'rgba(21, 101, 192, 0.2)', // Border for completed status
        'status-pending-bg': '#FFF3E0', // Light orange background for pending status
        'status-pending-text': '#E65100', // Dark orange text for pending status
        'status-pending-border': 'rgba(230, 81, 0, 0.2)', // Border for pending status
        'status-shipped-bg': '#E3F2FD', // Light blue background for shipped status
        'status-shipped-text': '#1565C0', // Dark blue text for shipped status
        'status-shipped-border': 'rgba(21, 101, 192, 0.2)', // Border for shipped status
        'status-delivered-bg': '#E8F5E9', // Light green background for delivered status
        'status-delivered-text': '#2E7D32', // Dark green text for delivered status
        'status-delivered-border': 'rgba(46, 125, 50, 0.2)', // Border for delivered status

        // Accent Colors
        accent: '#D36C3C', // Primary accent color
        'accent-hover': '#E65100', // Accent hover state
        'accent-light': '#FFE0B2', // Light accent for backgrounds
        'accent-dark': '#BF360C', // Dark accent for text

        // Card Colors
        'card-bg': '#FFFFFF', // Card background
        'card-hover': '#F5F5F5', // Card hover state
        'card-border': '#E0E0E0', // Card border color

        // Chart Colors
        'chart-green': '#4CAF50', // Green for positive values
        'chart-red': '#F44336', // Red for negative values
        'chart-yellow': '#FFC107', // Yellow for neutral values
        'chart-blue': '#2196F3', // Blue for information
        'chart-purple': '#9C27B0', // Purple for additional data
        'chart-orange': '#FF9800', // Orange for warnings

        // Background Colors
        'primary-bg': '#FFFFFF', // Primary background
        'secondary-bg': '#F5F5F5', // Secondary background
        'tertiary-bg': '#EEEEEE', // Tertiary background

        // Shadcn/ui compatible colors
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderStyle: {
        dotted: 'dotted',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(-10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        fadeIn: 'fadeIn 0.5s ease-out',
      },
    },
  },
  plugins: [tailwindcssAnimate],
} satisfies Config
