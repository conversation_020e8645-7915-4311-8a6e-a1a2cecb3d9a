{"name": "AgriUSDCoin", "structs": {}, "methods": [{"name": "create", "args": [], "returns": {"type": "byte[]"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": false, "desc": "Create the Agri USD Coin ASA.", "events": [], "recommendations": {}}, {"name": "get_asset_id", "args": [], "returns": {"type": "uint64"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": true, "events": [], "recommendations": {}}, {"name": "get_minted_tokens", "args": [], "returns": {"type": "uint64"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": true, "events": [], "recommendations": {}}, {"name": "get_burnt_tokens", "args": [], "returns": {"type": "uint64"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": true, "events": [], "recommendations": {}}, {"name": "mint_tokens", "args": [{"type": "uint64", "name": "amount"}, {"type": "account", "name": "receiver"}], "returns": {"type": "byte[]"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": false, "events": [], "recommendations": {}}, {"name": "burn_tokens", "args": [{"type": "uint64", "name": "amount"}, {"type": "account", "name": "address"}], "returns": {"type": "byte[]"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": false, "events": [], "recommendations": {}}, {"name": "transfer_tokens", "args": [{"type": "uint64", "name": "amount"}, {"type": "account", "name": "receiver"}, {"type": "account", "name": "account"}], "returns": {"type": "byte[]"}, "actions": {"create": [], "call": ["NoOp"]}, "readonly": false, "events": [], "recommendations": {}}], "arcs": [22, 28], "networks": {}, "state": {"schema": {"global": {"ints": 3, "bytes": 0}, "local": {"ints": 0, "bytes": 0}}, "keys": {"global": {"minted_tokens": {"keyType": "AVMString", "valueType": "AVMUint64", "key": "bWludGVkX3Rva2Vucw=="}, "burnt_tokens": {"keyType": "AVMString", "valueType": "AVMUint64", "key": "YnVybnRfdG9rZW5z"}, "asset": {"keyType": "AVMString", "valueType": "AVMUint64", "key": "YXNzZXQ="}}, "local": {}, "box": {}}, "maps": {"global": {}, "local": {}, "box": {}}}, "bareActions": {"create": ["NoOp"], "call": []}, "sourceInfo": {"approval": {"sourceInfo": [{"pc": [325], "errorMessage": "ASA already created"}, {"pc": [126, 165, 198, 231, 248, 265, 282], "errorMessage": "OnCompletion is not NoOp"}, {"pc": [319], "errorMessage": "Only the creator can create the ASA"}, {"pc": [311], "errorMessage": "can only call when creating"}, {"pc": [129, 168, 201, 234, 251, 268, 285], "errorMessage": "can only call when not creating"}, {"pc": [323, 404, 425, 470, 516], "errorMessage": "check self.asset exists"}, {"pc": [414, 499], "errorMessage": "check self.burnt_tokens exists"}, {"pc": [409, 451, 546], "errorMessage": "check self.minted_tokens exists"}], "pcOffsetMethod": "none"}, "clear": {"sourceInfo": [], "pcOffsetMethod": "none"}}, "source": {"approval": "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", "clear": "I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"}, "byteCode": {"approval": "CiADAAEEJgQFYXNzZXQEFR98dQ1taW50ZWRfdG9rZW5zDGJ1cm50X3Rva2VuczEYQAAJKiJnKyJnKCJnMRtBAO6CBwSCE63mBFuiKoQEUo+dywSamI+VBHrKjfoELZoN5wTDcvl5NhoAjgcAngCNAHwAawBKACkAAiJDMRkURDEYRDYaATYaAhfAHDYaAxfAHIgBZ0kVFlcGAkxQKUxQsCNDMRkURDEYRDYaATYaAhfAHIgBFkkVFlcGAkxQKUxQsCNDMRkURDEYRDYaATYaAhfAHIgAykkVFlcGAkxQKUxQsCNDMRkURDEYRIgArRYpTFCwI0MxGRREMRhEiACXFilMULAjQzEZFEQxGESIAIEWKUxQsCNDMRkURDEYRIgAGUkVFlcGAkxQKUxQsCNDMRlA/0UxGBREI0MxADIJEkQiKGVEFESxMgAyCkcDsiyyK7IqsimACEFHUkktVVNEsiWADUFncmkgVVNEIENvaW6yJoECsiOB////////////AbIigQOyELIBs7QXKLQ8Z4kiKGVEiSIqZUSJIitlRImKAgGxMgAiKGVEi/4XSbISTLIRi/+yFCSyEEyyAbO0FyIqZURPAggqTGeJigIBsTIAMgoiKGVEi/4Xi/+yE0myEkyyEUyyFCSyEEyyAbO0FyIrZURPAggrTGeJigMBsTIAIihlRIv9F4v/shNJshJMshGL/rIUJLIQTLIBs7QXIiplRE8CCCpMZ4k=", "clear": "CoEBQw=="}, "compilerInfo": {"compiler": "puya", "compilerVersion": {"major": 4, "minor": 7, "patch": 0}}, "events": [], "templateVariables": {}}