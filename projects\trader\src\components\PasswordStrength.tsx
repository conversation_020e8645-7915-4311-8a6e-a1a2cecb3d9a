import clsx from 'clsx'
import { useMemo } from 'react'

interface PasswordStrengthProps {
  password: string
}

export const PasswordStrength = ({ password }: PasswordStrengthProps) => {
  const strength = useMemo(() => {
    if (!password) return 0
    let score = 0
    if (password.length >= 8) score++
    if (/[A-Z]/.test(password)) score++
    if (/[a-z]/.test(password)) score++
    if (/[0-9]/.test(password)) score++
    if (/[^A-Za-z0-9]/.test(password)) score++
    return score
  }, [password])

  return (
    <div className="mt-2">
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((level) => (
          <div
            key={level}
            className={clsx('h-1 flex-1 rounded-full transition-all duration-200', {
              'bg-password-weak': strength >= level && strength < 3,
              'bg-password-medium': strength >= level && strength === 3,
              'bg-password-strong': strength >= level && strength > 3,
              'bg-password-inactive': strength < level,
            })}
          />
        ))}
      </div>
      <p className="text-xs text-primary-text opacity-60 mt-1">
        {strength === 0 && 'Enter password'}
        {strength === 1 && 'Very weak'}
        {strength === 2 && 'Weak'}
        {strength === 3 && 'Medium'}
        {strength === 4 && 'Strong'}
        {strength === 5 && 'Very strong'}
      </p>
    </div>
  )
}
