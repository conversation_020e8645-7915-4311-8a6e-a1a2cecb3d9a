# Generated by Django 5.2 on 2025-04-25 09:19

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Coordinates",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("lat", models.FloatField()),
                ("long", models.FloatField()),
            ],
            options={
                "verbose_name": "Coordinate",
                "verbose_name_plural": "Coordinates",
            },
        ),
        migrations.CreateModel(
            name="Fertilizer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="GrowthPeriod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("start_date", models.DateField()),
                ("harvest_date", models.DateField()),
            ],
        ),
        migrations.CreateModel(
            name="Location",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("address", models.CharField(max_length=255)),
                (
                    "coordinates",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location",
                        to="crops.coordinates",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Crops",
            fields=[
                (
                    "crop_id",
                    models.PositiveBigIntegerField(
                        editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    "cost",
                    models.PositiveBigIntegerField(
                        validators=[django.core.validators.MinValueValidator(0)]
                    ),
                ),
                (
                    "crop_grade",
                    models.CharField(
                        choices=[("A", "Grade A"), ("B", "Grade B"), ("C", "Grade C")],
                        max_length=50,
                    ),
                ),
                (
                    "quantity",
                    models.PositiveBigIntegerField(
                        validators=[django.core.validators.MinValueValidator(0)]
                    ),
                ),
                ("soil_type", models.CharField(max_length=100)),
                ("irrigation_type", models.CharField(max_length=100)),
                (
                    "certification",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "farmer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="registered_crops",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "fertilizers_used",
                    models.ManyToManyField(related_name="crops", to="crops.fertilizer"),
                ),
                (
                    "growth_period",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="crop",
                        to="crops.growthperiod",
                    ),
                ),
                (
                    "location",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="crop",
                        to="crops.location",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Crops",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CropTransfer",
            fields=[
                (
                    "transaction_id",
                    models.CharField(editable=False, primary_key=True, serialize=False),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("COMPLETED", "Completed"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                (
                    "crop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfers",
                        to="crops.crops",
                    ),
                ),
                (
                    "from_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "to_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="received_transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("crop", "from_user", "to_user")},
            },
        ),
    ]
