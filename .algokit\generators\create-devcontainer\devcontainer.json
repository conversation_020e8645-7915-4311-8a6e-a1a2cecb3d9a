{"forwardPorts": [4001, 4002, 8980, 5173], "portsAttributes": {"4001": {"label": "algod"}, "4002": {"label": "kmd"}, "8980": {"label": "indexer"}, "5173": {"label": "vite"}}, "postCreateCommand": "mkdir -p ~/.config/algokit && pipx install algokit && sudo chown -R codespace:codespace ~/.config/algokit", "postStartCommand": "for i in {1..5}; do algokit localnet status > /dev/null 2>&1 && break || sleep 30; algokit localnet reset; done"}