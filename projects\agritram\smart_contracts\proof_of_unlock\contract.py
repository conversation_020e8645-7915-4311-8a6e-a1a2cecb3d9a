from algopy import ARC4Contract, Asset, Global, Txn, UInt64, arc4, gtxn, itxn, log, op


class Milestone(arc4.Struct):
    percentage: arc4.UInt64
    buyer_approved: arc4.Bool
    seller_approved: arc4.Bool


class ProofOfUnlock(ARC4Contract):
    buyer: arc4.Address  # buyer = GlobalState(arc4.Address, default=arc4.Address())
    seller: arc4.Address  # seller = GlobalState(arc4.Address, default=arc4.Address())
    total_amount: arc4.UInt64
    # total_amount = GlobalState(arc4.UInt64, default=arc4.UInt64(0))
    milestones: arc4.DynamicArray[Milestone]  # milestones = GlobalState(
    #     arc4.DynamicArray[Milestone], default=arc4.DynamicArray[Milestone]()
    # )
    released: arc4.UInt64  # released = GlobalState(arc4.UInt64, default=arc4.UInt64(0))
    withdrawn: (
        arc4.UInt64
    )  # withdrawn = GlobalState(arc4.UInt64, default=arc4.UInt64(0))

    @arc4.abimethod
    def create_contract(
        self,
        buyer: arc4.Address,
        seller: arc4.Address,
        total_amount: arc4.UInt64,
        asset: Asset,
        milestone_percentages: arc4.DynamicArray[arc4.UInt64],
    ) -> None:
        log("create_contract called", buyer, seller, total_amount, asset, milestone_percentages)
        """Initialize the contract with trade parameters"""
        # Verify percentage sum equals 100%
        total_percent = UInt64(0)
        for percentage in milestone_percentages:
            total_percent += percentage.native
        assert total_percent == 100, "Total percentages must equal 100%"

        # Initialize state
        self.buyer = buyer
        self.seller = seller
        self.total_amount = total_amount
        self.released = arc4.UInt64(0)
        self.withdrawn = arc4.UInt64(0)
        log("State after storing contract data", self.buyer, self.seller, self.total_amount, self.released, self.withdrawn)

        itxn.AssetTransfer(
            asset_receiver=Global.current_application_address,
            xfer_asset=asset,
            asset_amount=0,
            fee=Global.min_txn_fee,
        ).submit()

        # Create milestones
        milestones = arc4.DynamicArray[Milestone]()
        for percentage in milestone_percentages:
            milestones.append(
                Milestone(
                    percentage=percentage,
                    buyer_approved=arc4.Bool(False),
                    seller_approved=arc4.Bool(False),
                )
            )
        self.milestones = milestones.copy()
        log("Milestones after storing", self.milestones)

    @arc4.abimethod
    def fund_escrow(self, payment: gtxn.AssetTransferTransaction) -> None:
        log("fund_escrow called", payment.asset_sender, payment.asset_receiver, payment.asset_amount)
        """Fund the escrow account with total price"""
        # Verify transaction group structure
        assert Txn.group_index == 1, "Must be second transaction in group"
        # assert payment.type_enum() == TealType.Payment, "Invalid payment txn"

        # Validate payment details
        assert payment.asset_sender == self.buyer.native, "Invalid buyer"
        assert (
            payment.asset_receiver == Global.current_application_address
        ), "Invalid receiver"
        assert payment.asset_amount == self.total_amount.native, "Incorrect amount"
        log("Escrow funded", payment.asset_sender, payment.asset_amount)

    @arc4.abimethod
    def approve_milestone(self, milestone_index: arc4.UInt64) -> None:
        log("approve_milestone called", milestone_index, Txn.sender)
        """Approve a milestone by buyer or seller"""
        idx = milestone_index.native
        milestones = self.milestones.copy()
        assert idx < milestones.length, "Invalid milestone index"

        # Get current milestone
        milestone = milestones[idx].copy()
        assert not (
            milestone.buyer_approved and milestone.seller_approved
        ), "Already approved"

        # Update approval status
        if Txn.sender == self.buyer.native:
            milestone.buyer_approved = arc4.Bool(True)
            milestone.seller_approved = arc4.Bool(True)
        else:
            assert False, "Unauthorized"

        # Store updated milestone
        milestones[idx] = milestone.copy()
        self.milestones = milestones.copy()
        log("Milestone after approval", idx, milestone, self.milestones)

        # Update released amount if both approved
        if milestone.buyer_approved and milestone.seller_approved:
            percentage = milestone.percentage.native
            current_released = self.released.native
            additional = (self.total_amount.native * percentage) // 100
            self.released = arc4.UInt64(current_released + additional)
            log("Released updated", self.released)
        

    @arc4.abimethod
    def release_funds(self, asset: Asset) -> None:
        log("release_funds called", asset, Txn.sender)
        """Release available funds by buyer"""
        if Txn.sender != self.buyer.native:
            log("Withdraw failed: Only buyer can release", Txn.sender)
            assert False, "Only buyer can release"

        available = self.released.native - self.withdrawn.native
        if available <= 0:
            log("Withdraw failed: No funds available", available)
            assert False, "No funds available"


        # Update withdrawn amount
        self.withdrawn = arc4.UInt64(self.withdrawn.native + available)
        log("Withdrawn updated", self.withdrawn)

        # Log successful withdrawal
        log("Withdraw success", self.seller.native, available)

        # Send payment
        itxn.AssetTransfer(
            asset_receiver=self.seller.native,
            asset_amount=available,
            xfer_asset=asset,
            fee=Global.min_txn_fee,
        ).submit()

    @arc4.abimethod(readonly=True)
    def get_balance(self, asset: Asset) -> arc4.UInt64:
        log("get_balance called", asset)
        """Get current escrow asset balance"""
        app_address = op.Global.current_application_address
        asset_balance, exists = op.AssetHoldingGet.asset_balance(app_address, asset)
        # Explicitly convert to arc4.UInt64 to ensure type consistency
        return arc4.UInt64(asset_balance) if exists else arc4.UInt64(0)
