import apiClient from '@/services/apiClient'
import { ellipseAddress } from '@/utils/ellipseAddress'
import { AnimatePresence, motion } from 'framer-motion'
import { CheckCircle2, Clock, Eye, FileText, Filter, MoreVertical, Package, Search, Truck } from 'lucide-react'
import { useEffect, useState } from 'react'

interface Crop {
  crop_id: number
  crop_name: string
  quantity: number
  unit: string
}

interface Milestone {
  id: number
  name: string
  description: string
  amount: string
  status: 'released' | 'pending' | 'shipped'
  completed_date: string | null
}

interface Document {
  filename: string
  fileType: string
  hash: string
}

interface Shipment {
  tx_id: string
  contract_address: string
  buyer: string
  total_amount: number
  release_amount: number
  status: 'pending' | 'shipped' | 'delivered'
  created_at: string
  last_updated: string
  crops: Crop[]
  milestones: Milestone[]
  documents: Document[]
}

const Shipments = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [shipments, setShipments] = useState<Shipment[]>([])

  useEffect(() => {
    const fetchProofOfUnlock = async () => {
      try {
        setIsLoading(true)
        const response = await apiClient.get<Shipment[]>('/proof-of-unlock/proof-of-unlock/user/')
        setShipments(response.data)
      } catch (error) {
        console.error('Error fetching proof of unlock:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchProofOfUnlock()
  }, [])

  const updateMilestone = async (tx_id: string, milestone_id: number, status: 'pending' | 'released' | 'shipped') => {
    try {
      await apiClient.put('/proof-of-unlock/milestones/update/', {
        milestone: { id: milestone_id, status: status },
        tx_id: tx_id,
      })

      // Update the UI state
      setShipments((prevShipments) =>
        prevShipments.map((shipment) => {
          if (shipment.tx_id === tx_id) {
            return {
              ...shipment,
              milestones: shipment.milestones.map((milestone) =>
                milestone.id === milestone_id ? { ...milestone, status: status, completed_date: new Date().toISOString() } : milestone,
              ),
            }
          }
          return shipment
        }),
      )

      // Update selected shipment if it's the one being modified
      setSelectedShipment((prev) => {
        if (prev && prev.tx_id === tx_id) {
          return {
            ...prev,
            milestones: prev.milestones.map((milestone) =>
              milestone.id === milestone_id ? { ...milestone, status: status, completed_date: new Date().toISOString() } : milestone,
            ),
          }
        }
        return prev
      })
    } catch (error) {
      console.error('Error updating milestone:', error)
    }
  }

  // Filter shipments based on search query and status
  const filteredShipments = shipments.filter((shipment) => {
    // Get the primary crop name for search
    const primaryCropName = shipment.crops?.[0]?.crop_name || ''

    const matchesSearch =
      primaryCropName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      shipment.tx_id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      shipment.contract_address?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = selectedStatus === 'all' || shipment.status === selectedStatus

    return matchesSearch && matchesStatus
  })

  const handleViewShipment = (shipment: Shipment) => {
    setIsLoading(true)
    setSelectedShipment(shipment)
    setTimeout(() => setIsLoading(false), 500) // Simulate loading state
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = 'flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200'

    switch (status) {
      case 'pending':
        return (
          <div className={`${baseClasses} bg-status-pending-bg/20 text-status-pending-text border border-status-pending-border`}>
            <Clock className="w-3.5 h-3.5" />
            <span>Pending</span>
          </div>
        )
      case 'shipped':
        return (
          <div className={`${baseClasses} bg-status-shipped-bg/20 text-status-shipped-text border border-status-shipped-border`}>
            <Truck className="w-3.5 h-3.5" />
            <span>Shipped</span>
          </div>
        )
      case 'delivered':
        return (
          <div className={`${baseClasses} bg-status-delivered-bg/20 text-status-delivered-text border border-status-delivered-border`}>
            <CheckCircle2 className="w-3.5 h-3.5" />
            <span>Delivered</span>
          </div>
        )
      case 'completed':
        return (
          <div className={`${baseClasses} bg-status-completed-bg/20 text-status-completed-text border border-status-completed-border`}>
            <CheckCircle2 className="w-3.5 h-3.5" />
            <span>Completed</span>
          </div>
        )
      case 'active':
        return (
          <div className={`${baseClasses} bg-status-active-bg/20 text-status-active-text border border-status-active-border`}>
            <Truck className="w-3.5 h-3.5" />
            <span>Active</span>
          </div>
        )
      default:
        return (
          <div className={`${baseClasses} bg-secondary-bg/20 text-secondary-text border border-card-border`}>
            <span>{status}</span>
          </div>
        )
    }
  }

  return (
    <div className="flex flex-col lg:flex-row gap-6 bg-main-bg min-h-screen p-6">
      <div className="lg:w-2/3">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-card-bg p-6 rounded-xl shadow-lg mb-6 border border-card-border"
        >
          <div className="relative mb-6">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-secondary-text w-4 h-4" />
            <input
              type="text"
              placeholder="Search by product, buyer, or transaction ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 pr-4 py-3 w-full rounded-xl border border-card-border text-sm focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent bg-alt-bg text-primary-text placeholder:text-secondary-text transition-all duration-200"
            />
          </div>

          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setSelectedStatus('all')}
              className={`px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 hover:scale-105 active:scale-95 ${
                selectedStatus === 'all'
                  ? 'bg-accent text-button-text shadow-lg shadow-accent/20'
                  : 'bg-alt-bg text-secondary-text hover:bg-card-bg hover:text-primary-text border border-card-border'
              }`}
            >
              All Shipments
            </button>
            <button
              onClick={() => setSelectedStatus('pending')}
              className={`px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 hover:scale-105 active:scale-95 ${
                selectedStatus === 'pending'
                  ? 'bg-status-pending-bg text-status-pending-text border border-status-pending-border'
                  : 'bg-alt-bg text-secondary-text hover:bg-card-bg hover:text-primary-text border border-card-border'
              }`}
            >
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>Pending</span>
              </div>
            </button>
            <button
              onClick={() => setSelectedStatus('shipped')}
              className={`px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 hover:scale-105 active:scale-95 ${
                selectedStatus === 'shipped'
                  ? 'bg-status-active-bg text-status-active-text border border-status-active-border'
                  : 'bg-alt-bg text-secondary-text hover:bg-card-bg hover:text-primary-text border border-card-border'
              }`}
            >
              <div className="flex items-center gap-2">
                <Truck className="w-4 h-4" />
                <span>Shipped</span>
              </div>
            </button>
            <button
              onClick={() => setSelectedStatus('delivered')}
              className={`px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 hover:scale-105 active:scale-95 ${
                selectedStatus === 'delivered'
                  ? 'bg-status-completed-bg text-status-completed-text border border-status-completed-border'
                  : 'bg-alt-bg text-secondary-text hover:bg-card-bg hover:text-primary-text border border-card-border'
              }`}
            >
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-4 h-4" />
                <span>Delivered</span>
              </div>
            </button>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-card-bg rounded-xl shadow-lg overflow-hidden border border-card-border"
        >
          {filteredShipments.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-card-border">
                    <th className="py-4 px-6 text-left text-sm font-semibold text-secondary-text bg-alt-bg">Product</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-secondary-text bg-alt-bg">Quantity</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-secondary-text bg-alt-bg">Status</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-secondary-text bg-alt-bg">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredShipments.map((shipment, idx) => (
                    <motion.tr
                      key={shipment.tx_id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: idx * 0.05 }}
                      className={`transition-all duration-200 ${
                        idx !== filteredShipments.length - 1 ? 'border-b border-card-border' : ''
                      } hover:bg-alt-bg`}
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-alt-bg">
                            <Package className="w-5 h-5 text-accent" />
                          </div>
                          <div>
                            <p className="font-medium text-primary-text">
                              {shipment.crops.length > 0 ? shipment.crops[0].crop_name : 'Unknown Product'}
                            </p>
                            <p className="text-xs text-secondary-text mt-0.5">TX: {ellipseAddress(shipment.tx_id)}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        {shipment.crops.length > 0 ? (
                          <div>
                            <p className="text-sm font-medium text-primary-text">
                              {shipment.crops[0].quantity} {shipment.crops[0].unit}
                            </p>
                            {shipment.crops.length > 1 && (
                              <p className="text-xs text-secondary-text mt-1">
                                +{shipment.crops.length - 1} more {shipment.crops.length - 1 === 1 ? 'item' : 'items'}
                              </p>
                            )}
                          </div>
                        ) : (
                          <p className="text-sm font-medium text-primary-text">Not specified</p>
                        )}
                      </td>
                      <td className="py-4 px-6">{getStatusBadge(shipment.status)}</td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleViewShipment(shipment)}
                            className="flex items-center gap-1.5 text-sm font-medium text-accent bg-alt-bg hover:bg-accent hover:text-button-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm"
                          >
                            <Eye className="w-4 h-4" />
                            <span>View Details</span>
                          </button>
                          <button className="p-2 rounded-lg bg-alt-bg hover:bg-card-bg text-secondary-text hover:text-primary-text transition-all duration-200">
                            <MoreVertical className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="py-16 text-center">
              <Filter className="w-12 h-12 text-secondary-text mx-auto mb-4" />
              <p className="text-lg font-medium text-primary-text mb-2">No shipments found</p>
              <p className="text-secondary-text max-w-md mx-auto">Try adjusting your search or filter to find what you're looking for.</p>
            </div>
          )}
        </motion.div>
      </div>

      <div className="lg:w-1/3">
        <AnimatePresence mode="wait">
          {isLoading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="bg-card-bg rounded-xl shadow-lg p-12 text-center flex flex-col items-center justify-center h-full border border-card-border"
            >
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-accent border-t-transparent"></div>
              <p className="mt-4 text-secondary-text">Loading shipment details...</p>
            </motion.div>
          ) : selectedShipment ? (
            <motion.div
              key="details"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="bg-card-bg rounded-xl shadow-lg overflow-hidden border border-card-border"
            >
              <div className="p-6 border-b border-card-border">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-lg font-semibold text-primary-text mb-1">Shipment Details</h2>
                    <p className="text-sm text-secondary-text">ID: {ellipseAddress(selectedShipment.tx_id)}</p>
                  </div>
                  {getStatusBadge(selectedShipment.status)}
                </div>
              </div>

              <div className="p-6">
                {/* Summary Card */}
                <div className="mb-6 bg-alt-bg p-4 rounded-lg border border-card-border">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                    <div>
                      <h2 className="text-lg font-semibold text-primary-text">{selectedShipment.crops[0]?.crop_name || 'Shipment'}</h2>
                      <p className="text-sm text-secondary-text">Created on {new Date(selectedShipment.created_at).toLocaleDateString()}</p>
                    </div>
                    <div className="mt-2 md:mt-0">
                      <div className="flex items-center gap-2">
                        <div className="text-right">
                          <p className="text-sm text-secondary-text">Total Value</p>
                          <p className="text-lg font-semibold text-accent">KTT {selectedShipment.total_amount}</p>
                        </div>
                        <div className="h-10 w-0.5 bg-card-border"></div>
                        <div className="text-right">
                          <p className="text-sm text-secondary-text">Released</p>
                          <p className="text-lg font-semibold text-chart-green">KTT {selectedShipment.release_amount}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    <div className="px-3 py-1.5 bg-secondary-bg rounded-lg">
                      <p className="text-xs text-secondary-text">Status</p>
                      <div className="mt-0.5">{getStatusBadge(selectedShipment.status)}</div>
                    </div>
                    <div className="px-3 py-1.5 bg-secondary-bg rounded-lg">
                      <p className="text-xs text-secondary-text">Items</p>
                      <p className="text-sm font-medium text-primary-text">{selectedShipment.crops.length}</p>
                    </div>
                    <div className="px-3 py-1.5 bg-secondary-bg rounded-lg">
                      <p className="text-xs text-secondary-text">Documents</p>
                      <p className="text-sm font-medium text-primary-text">{selectedShipment.documents.length}</p>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <h3 className="text-sm font-medium text-secondary-text mb-2">Product Information</h3>
                  <div className="bg-secondary-bg p-4 rounded-lg">
                    {selectedShipment.crops.map((crop, index) => (
                      <div key={index} className={`flex items-center gap-3 ${index > 0 ? 'mt-4' : 'mb-2'}`}>
                        <div className="p-2 rounded-lg bg-accent text-button-text">
                          <Package className="w-4 h-4" />
                        </div>
                        <div>
                          <p className="font-medium text-primary-text">{crop.crop_name}</p>
                          <p className="text-sm text-secondary-text">
                            {crop.quantity} {crop.unit}
                          </p>
                        </div>
                      </div>
                    ))}
                    <div className="mt-4 pt-3 border-t border-card-border">
                      <p className="text-sm text-secondary-text">Transaction ID: {ellipseAddress(selectedShipment.tx_id)}</p>
                      <p className="text-sm text-secondary-text mt-1">
                        Contract Address: {ellipseAddress(selectedShipment.contract_address)}
                      </p>
                      <p className="text-sm text-secondary-text mt-1">Buyer Address: {ellipseAddress(selectedShipment.buyer)}</p>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <h3 className="text-sm font-medium text-secondary-text mb-2">Shipping Information</h3>
                  <div className="bg-secondary-bg p-4 rounded-lg">
                    <p className="text-sm font-medium text-primary-text mb-1">Created At</p>
                    <p className="text-sm text-secondary-text mb-3">
                      {new Date(selectedShipment.created_at).toLocaleDateString()}{' '}
                      {new Date(selectedShipment.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>

                    <p className="text-sm font-medium text-primary-text mb-1">Last Updated</p>
                    <p className="text-sm text-secondary-text mb-3">
                      {new Date(selectedShipment.last_updated).toLocaleDateString()}{' '}
                      {new Date(selectedShipment.last_updated).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>

                    <p className="text-sm font-medium text-primary-text mb-1">Status</p>
                    <div className="mt-1">{getStatusBadge(selectedShipment.status)}</div>
                  </div>
                </div>

                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-secondary-text">Milestones</h3>
                    <div className="text-xs text-secondary-text">
                      {selectedShipment.milestones.filter((m) => m.status === 'released').length} of {selectedShipment.milestones.length}{' '}
                      completed
                    </div>
                  </div>

                  <div className="bg-secondary-bg p-4 rounded-lg">
                    {/* Progress bar */}
                    <div className="mb-4">
                      <div className="h-2 bg-card-border rounded-full overflow-hidden">
                        <div
                          className="h-full bg-accent"
                          style={{
                            width: `${
                              (selectedShipment.milestones.filter((m) => m.status === 'released').length /
                                selectedShipment.milestones.length) *
                              100
                            }%`,
                          }}
                        ></div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {selectedShipment.milestones.map((milestone, index) => (
                        <div key={index} className="relative pl-6">
                          {/* Milestone connector line */}
                          {index < selectedShipment.milestones.length - 1 && (
                            <div
                              className={`absolute left-[0.5rem] top-6 bottom-0 w-0.5 ${
                                milestone.status === 'released' && selectedShipment.milestones[index + 1].status === 'released'
                                  ? 'bg-chart-green'
                                  : 'bg-card-border'
                              }`}
                              style={{ transform: 'translateX(-50%)' }}
                            ></div>
                          )}

                          {/* Milestone status indicator */}
                          <div className="absolute left-0 top-1">
                            {milestone.status === 'released' ? (
                              <div className="w-4 h-4 rounded-full bg-chart-green flex items-center justify-center">
                                <CheckCircle2 className="w-2.5 h-2.5 text-button-text" />
                              </div>
                            ) : (
                              <div className="w-4 h-4 rounded-full border-2 border-accent bg-secondary-bg"></div>
                            )}
                          </div>

                          {/* Milestone content */}
                          <div className={`${milestone.status === 'released' ? 'opacity-100' : 'opacity-80'}`}>
                            <div className="flex justify-between items-start">
                              <p className={`font-medium ${milestone.status === 'released' ? 'text-primary-text' : 'text-secondary-text'}`}>
                                {milestone.name}
                              </p>
                              <p className={`text-sm font-medium ${milestone.status === 'released' ? 'text-chart-green' : 'text-accent'}`}>
                                KTT {milestone.amount}
                              </p>
                            </div>
                            <p className="text-sm text-secondary-text mt-1">{milestone.description}</p>
                            {milestone.completed_date && (
                              <p className="text-xs text-secondary-text mt-1">
                                Completed on {new Date(milestone.completed_date).toLocaleDateString()}{' '}
                                {new Date(milestone.completed_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </p>
                            )}
                            {milestone.status !== 'released' && <p className="text-xs text-secondary-text mt-1">Pending</p>}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-secondary-text">Documents</h3>
                    <span className="text-xs px-2 py-0.5 bg-card-border rounded-full text-secondary-text">
                      {selectedShipment.documents.length} files
                    </span>
                  </div>

                  {selectedShipment.documents.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedShipment.documents.map((document, index) => (
                        <div
                          key={index}
                          className="flex items-center p-3 bg-secondary-bg rounded-lg cursor-pointer transition-all hover:bg-card-bg hover:shadow-md group"
                        >
                          <div className="p-2 bg-card-border rounded-lg group-hover:bg-alt-bg mr-3">
                            <FileText className="w-4 h-4 text-accent" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-primary-text font-medium truncate">{document.filename}</p>
                            <p className="text-xs text-secondary-text">{document.fileType}</p>
                          </div>
                          <div className="ml-2">
                            <button className="p-1.5 rounded-full hover:bg-alt-bg text-secondary-text hover:text-primary-text transition-colors">
                              <Eye className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="bg-secondary-bg p-8 rounded-lg text-center">
                      <FileText className="w-8 h-8 text-secondary-text mx-auto mb-2" />
                      <p className="text-sm text-secondary-text">No documents available</p>
                    </div>
                  )}
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  {selectedShipment.milestones.length >= 2 &&
                    selectedShipment.milestones[0].status === 'released' &&
                    selectedShipment.milestones[1].status === 'pending' && (
                      <button
                        className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-chart-green text-button-text rounded-lg hover:bg-[#4ade80] transition-all hover:shadow-lg hover:shadow-chart-green/20 active:scale-[0.98]"
                        onClick={() => {
                          updateMilestone(selectedShipment.tx_id, selectedShipment.milestones[1].id, 'shipped')
                        }}
                      >
                        <CheckCircle2 className="w-4 h-4" />
                        <span className="font-medium">Mark as Delivered</span>
                      </button>
                    )}

                  <button className="flex items-center justify-center gap-2 px-4 py-3 bg-secondary-bg text-secondary-text rounded-lg hover:bg-card-bg hover:text-accent transition-all active:scale-[0.98]">
                    <FileText className="w-4 h-4" />
                    <span className="font-medium">Upload Document</span>
                  </button>
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="empty"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="bg-card-bg rounded-xl shadow-lg p-12 text-center flex flex-col items-center justify-center h-full border border-card-border"
            >
              <Truck className="w-12 h-12 text-secondary-text mb-4" />
              <h3 className="text-xl font-semibold text-primary-text mb-2">Select a Shipment</h3>
              <p className="text-secondary-text max-w-md mx-auto">
                Choose a shipment from the list to view details, update status, and manage documents.
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default Shipments
