# Generated by Django 5.2 on 2025-04-22 08:49

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("is_active", models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=False)),
                (
                    "date_joined",
                    models.DateTimeField(
                        db_column="account_creation_date",
                        default=django.utils.timezone.now,
                    ),
                ),
                ("last_login", models.DateTimeField(blank=True, null=True)),
                ("password", models.CharField(max_length=255)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("farmer", "Farmer"),
                            ("trader", "Trader"),
                            ("manufacturer", "Manufacturer"),
                            ("bank", "Bank"),
                            ("admin", "Admin"),
                        ],
                        default="farmer",
                        max_length=50,
                    ),
                ),
                (
                    "account_address",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("opt_in", models.BooleanField(default=False)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "indexes": [
                    models.Index(fields=["email"], name="user_user_email_5f6a77_idx")
                ],
            },
        ),
    ]
