import { useEffect, useState } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from './stores/authStore';

// Pages
import Login from './pages/Login';
import BuyerDashboard from './pages/buyer/Dashboard';
import SellerDashboard from './pages/seller/Dashboard';
import Transactions from './pages/buyer/Transactions';
import Inventory from './pages/seller/Inventory';
import Escrow from './pages/buyer/Escrow';
import Shipments from './pages/seller/Shipments';
import Documentation from './pages/Documentation';
import NotFound from './pages/NotFound';

// Components
import LoadingScreen from './components/common/LoadingScreen';
import ProtectedRoute from './components/auth/ProtectedRoute';

function App() {
  const location = useLocation();
  const { isAuthenticated, userType, loading, checkAuth } = useAuthStore();
  const [appLoading, setAppLoading] = useState(true);

  useEffect(() => {
    const initApp = async () => {
      await checkAuth();
      setAppLoading(false);
    };

    initApp();
  }, [checkAuth]);

  if (appLoading || loading) {
    return <LoadingScreen />;
  }

  // Determine the dashboard route based on user type
  const dashboardRoute = userType === 'seller' ? '/seller/dashboard' : '/dashboard';

  return (
    <Routes>
      <Route 
        path="/login" 
        element={
          isAuthenticated ? 
            <Navigate to={dashboardRoute} replace /> : 
            <Login />
        } 
      />
      
      {/* Buyer Routes */}
      <Route path="/dashboard" element={
        <ProtectedRoute isAllowed={isAuthenticated && userType === 'buyer'}>
          <BuyerDashboard />
        </ProtectedRoute>
      } />
      <Route path="/transactions" element={
        <ProtectedRoute isAllowed={isAuthenticated && userType === 'buyer'}>
          <Transactions />
        </ProtectedRoute>
      } />
      <Route path="/escrow" element={
        <ProtectedRoute isAllowed={isAuthenticated && userType === 'buyer'}>
          <Escrow />
        </ProtectedRoute>
      } />

      {/* Seller Routes */}
      <Route path="/seller/dashboard" element={
        <ProtectedRoute isAllowed={isAuthenticated && userType === 'seller'}>
          <SellerDashboard />
        </ProtectedRoute>
      } />
      <Route path="/inventory" element={
        <ProtectedRoute isAllowed={isAuthenticated && userType === 'seller'}>
          <Inventory />
        </ProtectedRoute>
      } />
      <Route path="/shipments" element={
        <ProtectedRoute isAllowed={isAuthenticated && userType === 'seller'}>
          <Shipments />
        </ProtectedRoute>
      } />

      {/* Common Routes */}
      <Route path="/documentation" element={
        <ProtectedRoute isAllowed={isAuthenticated}>
          <Documentation />
        </ProtectedRoute>
      } />

      {/* Default redirects */}
      <Route 
        path="/" 
        element={
          isAuthenticated ? 
            <Navigate to={dashboardRoute} replace /> : 
            <Navigate to="/login" replace />
        } 
      />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;