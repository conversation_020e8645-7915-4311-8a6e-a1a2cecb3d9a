from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    InventoryQuantity,
    InventoryCropStatus
)
from django.db.models import Manager, QuerySet
from user.serializers import UserSerializer
from crops.serializers import CropsSerializer
User = get_user_model()

class InventoryQuantitySerializer(serializers.ModelSerializer):

    class Meta:
        model = InventoryQuantity
        fields = [
            "id",
            "total_quantity_to_date",
            "total_quantity_batches",
            "total_quantity_in_storage",
            "ready_to_sell_quantity",
            "sold_quantity",
        ]
        
class TraderInventoryQuantitySerializer(serializers.ModelSerializer):
    trader = UserSerializer()
    class Meta:
        model = InventoryQuantity
        fields = [
            "id",
            "trader",
            "total_quantity_batches",
            "total_quantity_in_storage",
            "ready_to_sell_quantity",
            "sold_quantity",
        ]


class CropsWithStatusSerializer(serializers.ModelSerializer):
    crop = CropsSerializer()

    class Meta:
        model = InventoryCropStatus
        fields = ["crop", "status", "created_at", "updated_at"]

    def to_representation(self, instance):
        crop_data = super().to_representation(instance)["crop"]
        crop_data["status"] = instance.status
        crop_data["created_at"] = instance.created_at
        crop_data["updated_at"] = instance.updated_at
        return crop_data



