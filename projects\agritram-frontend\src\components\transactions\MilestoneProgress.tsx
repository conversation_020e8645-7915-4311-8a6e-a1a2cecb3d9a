import { motion } from 'framer-motion';
import { CheckCircle2, Clock, AlertCircle } from 'lucide-react';
import { Milestone } from '../../stores/transactionStore';

interface MilestoneProgressProps {
  milestones: Milestone[];
}

const MilestoneProgress = ({ milestones }: MilestoneProgressProps) => {
  // Sort milestones by ID to ensure correct order
  const sortedMilestones = [...milestones].sort((a, b) => a.id - b.id);
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'verified':
      case 'released':
        return <CheckCircle2 className="w-5 h-5 text-success-500" />;
      case 'disputed':
        return <AlertCircle className="w-5 h-5 text-error-500" />;
      default:
        return <Clock className="w-5 h-5 text-neutral-400" />;
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'verified':
      case 'released':
        return 'bg-success-500';
      case 'disputed':
        return 'bg-error-500';
      default:
        return 'bg-neutral-300';
    }
  };
  
  return (
    <div className="py-4">
      <div className="relative flex items-center justify-between">
        {/* Progress line */}
        <div className="absolute left-0 right-0 h-1 bg-neutral-200"></div>
        
        {/* Milestones */}
        {sortedMilestones.map((milestone, index) => (
          <div key={milestone.id} className="relative flex flex-col items-center z-10">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-center w-8 h-8 rounded-full bg-white border-2 border-neutral-200"
            >
              {getStatusIcon(milestone.status)}
            </motion.div>
            
            {/* Progress fill */}
            {index < sortedMilestones.length - 1 && (
              <div className="absolute left-4 w-full h-1">
                <motion.div
                  initial={{ width: '0%' }}
                  animate={{ width: milestone.status === 'pending' ? '0%' : '100%' }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className={`h-full ${getStatusColor(milestone.status)}`}
                />
              </div>
            )}
            
            <div className="mt-2 text-center">
              <p className="text-xs font-medium text-neutral-900">{milestone.name}</p>
              <p className="text-xs text-neutral-500">${milestone.amount}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MilestoneProgress;