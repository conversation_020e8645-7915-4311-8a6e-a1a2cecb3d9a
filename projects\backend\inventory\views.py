from django.db.models.signals import post_save
from django.dispatch import receiver
from crops.models import CropTransfer, Crops
from .models import InventoryQuantity, InventoryCropStatus
from user.models import RoleChoices
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .serializers import CropsWithStatusSerializer, InventoryQuantitySerializer, TraderInventoryQuantitySerializer
from rest_framework import status
from django.db.models import F


@receiver(post_save, sender=CropTransfer)
def update_inventory(sender, instance, **kwargs):
    print("Updating inventory")
    trader = instance.to_user
    print(trader)
    print(trader.role)
    print(RoleChoices.TRADER)
    if trader.role != RoleChoices.TRADER:
        print("Trader is not a trader")
        return
    crop = instance.crop
    quantity = crop.quantity
    unit = crop.unit

    if unit != "tons":
        quantity = quantity / 1000
        print(quantity)

    inventory, create = InventoryQuantity.objects.get_or_create(
        trader=trader,
        defaults={
            "total_quantity_to_date": quantity,
            "total_quantity_in_storage": quantity,
            "total_quantity_batches": 1,
            "storage_batches": 1,
            "ready_to_sell_quantity": 0,
            "sold_quantity": 0,
            "ready_to_sell_batches": 0,
            "sold_batches": 0,
        },
    )
    inventorycropstatus, created = InventoryCropStatus.objects.get_or_create(
        trader=trader,
        crop=crop,
        defaults={
            "status": "storage",
        },
    )
    if create:
        inventory.save()
    else:
        inventory.total_quantity_to_date += quantity
        inventory.total_quantity_in_storage += quantity
        inventory.total_quantity_batches += 1
        inventory.storage_batches += 1
        inventory.save()
    if created:
        inventorycropstatus.save()
    else:
        pass


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_inventory(request):
    """
    Handles GET requests to retrieve all inventory data for the authenticated user (trader).
    Returns a list of inventory records.
    """
    inventory = get_object_or_404(InventoryQuantity, trader=request.user)
    serializer = InventoryQuantitySerializer(inventory)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_entire_inventory(request):
    inventory = InventoryQuantity.objects.all()
    serializer = TraderInventoryQuantitySerializer(inventory, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_inventory_crop_status(request):
    inventory_status_qs = InventoryCropStatus.objects.filter(
        trader=request.user
    ).select_related("crop")
    serializer = CropsWithStatusSerializer(inventory_status_qs, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)

@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_trader_inventory_crop_status(request, trader_id):
    inventory_status_qs = InventoryCropStatus.objects.filter(
        trader=trader_id, status="ready"
    ).select_related("crop")
    serializer = CropsWithStatusSerializer(inventory_status_qs, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def update_crop_status(request, crop_id):
    crop = get_object_or_404(Crops, crop_id=crop_id)
    crop_status = request.data.get("status")
    InventoryCropStatus.objects.filter(crop=crop, trader=request.user).update(
        status=crop_status
    )
    if crop_status == "storage":
        InventoryQuantity.objects.filter(trader=request.user).update(
            total_quantity_in_storage=F("total_quantity_in_storage") + crop.quantity
        )
        InventoryQuantity.objects.filter(trader=request.user).update(
            ready_to_sell_quantity=F("ready_to_sell_quantity") - crop.quantity
        )
        InventoryQuantity.objects.filter(trader=request.user).update(
            storage_batches=F("storage_batches") + 1
        )
        InventoryQuantity.objects.filter(trader=request.user).update(
            ready_to_sell_batches=F("ready_to_sell_batches") - 1
        )
    elif crop_status == "ready":
        InventoryQuantity.objects.filter(trader=request.user).update(
            total_quantity_in_storage=F("total_quantity_in_storage") - crop.quantity
        )
        InventoryQuantity.objects.filter(trader=request.user).update(
            ready_to_sell_quantity=F("ready_to_sell_quantity") + crop.quantity
        )
        InventoryQuantity.objects.filter(trader=request.user).update(
            storage_batches=F("storage_batches") - 1
        )
        InventoryQuantity.objects.filter(trader=request.user).update(
            ready_to_sell_batches=F("ready_to_sell_batches") + 1
        )
    return Response(
        {"message": "Crop status updated successfully"}, status=status.HTTP_200_OK
    )
