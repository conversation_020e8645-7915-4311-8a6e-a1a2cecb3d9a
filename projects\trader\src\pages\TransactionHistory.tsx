import apiClient from '@/services/apiClient'
import { ellipseAddress } from '@/utils/ellipseAddress'
import {
  AlertCircle,
  ArrowUpDown,
  CheckCircle2,
  ChevronRight,
  Clock,
  CreditCard,
  ExternalLink,
  FileText,
  Cast as Gas,
  History,
  Link2,
  Search,
  User,
  XCircle,
} from 'lucide-react'
import React, { useEffect, useState } from 'react'

type Transaction = {
  transaction_id: string
  algorand_tx_id: string
  from_address: string
  to_address: string
  amount_usd: number
  amount_stablecoin_ktt: number
  amount_stablecoin_kct: number
  transaction_type: string
  status: string
  timestamp: string
  gas_fee: string
  related_docs?: { type: string; value: string; link: string }[]
  history?: {
    date: string
    action: string
    actor: string
  }[]
}

function TransactionHistory() {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<keyof Transaction>('timestamp')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const [transactions, setTransactions] = useState<Transaction[]>([])

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        const response = await apiClient.get('/transaction/get-all-transactions/') // Replace with your API endpoint
        const result = response.data // Access the data directly

        if (result.status === 'success') {
          setTransactions(result.data)
        } else {
          console.error('Failed to fetch transactions:', result)
        }
      } catch (error) {
        console.error('Error fetching transactions:', error)
      }
    }

    fetchTransactions()
  }, [])

  const toggleRow = (transactionId: string) => {
    const newExpandedRows = new Set(expandedRows)
    if (expandedRows.has(transactionId)) {
      newExpandedRows.delete(transactionId)
    } else {
      newExpandedRows.add(transactionId)
    }
    setExpandedRows(newExpandedRows)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-status-completed-text'
      case 'PENDING':
        return 'text-status-pending-text'
      case 'FAILED':
        return 'text-button-danger'
      default:
        return 'text-secondary-text'
    }
  }

  const getTypeColor = (status: string) => {
    switch (status) {
      case 'MINT':
        return 'bg-accent text-button-text'
      case 'TRANSFER':
      case 'RECEIVE':
        return 'bg-status-active-bg text-status-active-text'
      case 'BURN':
        return 'bg-button-danger text-button-text'
      case 'PLEDGE':
        return 'bg-status-pending-bg text-status-pending-text'
      case 'CONVERT_KTT_TO_KCT':
      case 'CONVERT_KCT_TO_KTT':
        return 'bg-status-completed-bg text-status-completed-text'
      default:
        return 'bg-accent-light text-accent-dark'
    }
  }

  const sortTransactions = (a: Transaction, b: Transaction) => {
    const aValue = a[sortField] ?? ''
    const bValue = b[sortField] ?? ''

    if (sortDirection === 'asc') {
      return aValue < bValue ? -1 : 1
    }
    return aValue > bValue ? -1 : 1
  }

  const handleSort = (field: keyof Transaction) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const filteredTransactions = transactions
    .filter(
      (tx) =>
        tx.transaction_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tx.transaction_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tx.status.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    .sort(sortTransactions)

  return (
    <div className="min-h-screen bg-main-bg text-primary-text p-6">
      <div className="max-w-7xl mx-auto">
        <div className="bg-card-bg rounded-xl p-6 shadow-lg border border-card-border">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-primary-text">Transactions</h1>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-text" size={18} />
              <input
                type="text"
                placeholder="Search transactions..."
                className="bg-card-bg text-primary-text pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent border border-card-border transition-colors"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-card-border bg-accent-light/5">
                  <th className="px-4 py-3 text-left"></th>
                  <th className="px-4 py-3 text-left">
                    <button
                      className="flex items-center gap-2 hover:text-accent transition-colors"
                      onClick={() => handleSort('transaction_id')}
                    >
                      ID <ArrowUpDown size={16} />
                    </button>
                  </th>
                  <th className="px-4 py-3 text-left">
                    <button className="flex items-center gap-2 hover:text-accent transition-colors" onClick={() => handleSort('timestamp')}>
                      Date <ArrowUpDown size={16} />
                    </button>
                  </th>
                  <th className="px-4 py-3 text-left">Type</th>
                  <th className="px-4 py-3 text-right">Amount</th>
                  <th className="px-4 py-3 text-right">Status</th>
                </tr>
              </thead>
              <tbody>
                {filteredTransactions.map((tx) => (
                  <React.Fragment key={tx.transaction_id}>
                    <tr
                      className="border-b border-card-border cursor-pointer hover:bg-accent-light/5 transition-all duration-200"
                      onClick={() => toggleRow(tx.transaction_id)}
                    >
                      <td className="px-4 py-3">
                        <div
                          className={`transform transition-transform duration-200 ${
                            expandedRows.has(tx.transaction_id) ? 'rotate-90' : ''
                          }`}
                        >
                          <ChevronRight size={20} className={expandedRows.has(tx.transaction_id) ? 'text-accent' : 'text-secondary-text'} />
                        </div>
                      </td>
                      <td className="px-4 py-3 font-mono text-primary-text">{tx.transaction_id}</td>
                      <td className="px-4 py-3 text-secondary-text">{tx.timestamp}</td>
                      <td className="px-4 py-3">
                        <span className={`px-3 py-1 rounded-full ${getTypeColor(tx.transaction_type)}`}>
                          {tx.transaction_type.replace(/_/g, ' ')}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right font-medium">${Number(tx.amount_usd).toFixed(2)}</td>
                      <td className="px-4 py-3 text-right">
                        <span className={`flex items-center gap-2 justify-end ${getStatusColor(tx.status)}`}>
                          {tx.status === 'COMPLETED' ? (
                            <CheckCircle2 size={16} />
                          ) : tx.status === 'PENDING' ? (
                            <Clock size={16} />
                          ) : tx.status === 'FAILED' ? (
                            <XCircle size={16} />
                          ) : (
                            <AlertCircle size={16} />
                          )}
                          {tx.status}
                        </span>
                      </td>
                    </tr>
                    <tr className="border-b border-card-border">
                      <td colSpan={6} className="px-0 transition-all duration-300 ease-in-out">
                        <div
                          className={`overflow-hidden transition-all duration-300 ease-in-out ${
                            expandedRows.has(tx.transaction_id) ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                          }`}
                        >
                          <h3 className="text-accent font-semibold mb-3 flex items-center gap-2 pt-3">
                            <Link2 size={16} /> Transaction Details
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-3">
                            <div className="space-y-4">
                              <div>
                                <label className="text-secondary-text text-sm">Algorand Transaction ID</label>
                                <p className="font-mono p-2 rounded mt-1 flex items-center justify-between bg-card-bg border border-card-border">
                                  {ellipseAddress(tx.algorand_tx_id)}
                                  <a
                                    href={`https://lora.algokit.io/localnet/transaction/${tx.algorand_tx_id}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-accent hover:text-accent-hover transition-colors"
                                  >
                                    <ExternalLink size={16} />
                                  </a>
                                </p>
                              </div>

                              {tx.from_address && (
                                <div>
                                  <label className="text-secondary-text text-sm flex items-center gap-2">
                                    <User size={16} />
                                    From Address
                                  </label>
                                  <p className="font-mono bg-card-bg border border-card-border p-2 rounded mt-1 flex items-center justify-between">
                                    {ellipseAddress(tx.from_address)}
                                    <a
                                      href={`https://lora.algokit.io/localnet/account/${tx.from_address}`}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-accent hover:text-accent-hover transition-colors"
                                    >
                                      <ExternalLink size={16} />
                                    </a>
                                  </p>
                                </div>
                              )}

                              {tx.to_address && (
                                <div>
                                  <label className="text-secondary-text text-sm flex items-center gap-2">
                                    <User size={16} />
                                    To Address
                                  </label>
                                  <p className="font-mono bg-card-bg border border-card-border p-2 rounded mt-1 flex items-center justify-between">
                                    {ellipseAddress(tx.to_address)}
                                    <a
                                      href={`https://lora.algokit.io/localnet/account/${tx.to_address}`}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-accent hover:text-accent-hover transition-colors"
                                    >
                                      <ExternalLink size={16} />
                                    </a>
                                  </p>
                                </div>
                              )}

                              {tx.history && (
                                <div>
                                  <h3 className="text-accent font-semibold mb-3 flex items-center gap-2">
                                    <History size={16} /> Transaction History
                                  </h3>
                                  <div className="space-y-3">
                                    {tx.history?.map((event, index) => (
                                      <div key={index} className="text-sm flex items-start gap-3">
                                        <Clock size={14} className="text-secondary-text mt-1" />
                                        <div>
                                          <p className="text-secondary-text">{event.date}</p>
                                          <p className="text-primary-text">{event.action}</p>
                                          <p className="text-accent">{event.actor}</p>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>

                            <div className="space-y-4">
                              <div>
                                <label className="text-secondary-text text-sm flex items-center gap-2">
                                  <CreditCard size={16} />
                                  Amount (USD)
                                </label>
                                <p className="text-xl font-bold text-accent">${Number(tx.amount_usd).toFixed(2)}</p>
                              </div>

                              <div>
                                <label className="text-secondary-text text-sm">KTT Amount</label>
                                <p className="font-semibold text-primary-text">{Number(tx.amount_stablecoin_ktt).toFixed(2)} KTT</p>
                              </div>

                              <div>
                                <label className="text-secondary-text text-sm">KCT Amount</label>
                                <p className="font-semibold text-primary-text">{Number(tx.amount_stablecoin_kct).toFixed(2)} KCT</p>
                              </div>

                              <div>
                                <label className="text-secondary-text text-sm flex items-center gap-2">
                                  <Gas size={16} />
                                  Gas Fee
                                </label>
                                <p className="font-semibold text-primary-text">{Number(tx.gas_fee).toFixed(3)} KTT</p>
                              </div>

                              <div>
                                <label className="text-secondary-text text-sm flex items-center gap-2">
                                  <Clock size={16} />
                                  Timestamp
                                </label>
                                <p className="font-semibold text-primary-text">{tx.timestamp}</p>
                              </div>

                              {tx.related_docs && (
                                <div className="mb-6">
                                  <h3 className="text-accent font-semibold mb-3 flex items-center gap-2">
                                    <FileText size={16} /> Related Documents
                                  </h3>
                                  <ul className="space-y-2">
                                    {tx.related_docs?.map((doc, index) => (
                                      <li key={index} className="text-sm">
                                        <a href="#" className="text-secondary-text hover:text-accent transition-colors duration-200">
                                          {doc.value}
                                        </a>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TransactionHistory
