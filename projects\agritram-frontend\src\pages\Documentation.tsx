import { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, ChevronDown, CheckCircle2, LockKeyhole, Wallet, Clock, ShieldCheck } from 'lucide-react';
import AppLayout from '../components/layout/AppLayout';

// FAQ data structure
interface FAQ {
  question: string;
  answer: string;
}

// Docs section data structure
interface DocSection {
  title: string;
  icon: JSX.Element;
  content: string;
  faqs: FAQ[];
}

const Documentation = () => {
  const [expandedSection, setExpandedSection] = useState<string | null>('smart-contracts');
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null);
  
  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };
  
  // Toggle FAQ expansion
  const toggleFaq = (faqId: string) => {
    setExpandedFaq(expandedFaq === faqId ? null : faqId);
  };
  
  // Documentation sections
  const docSections: DocSection[] = [
    {
      title: 'Smart Contracts',
      icon: <LockKeyhole className="w-5 h-5" />,
      content: `
        Our platform uses secure, audited smart contracts to facilitate agricultural trade. These contracts are deployed on the blockchain and provide a trustless mechanism for buyers and sellers to transact.
        
        The main smart contract types include:
        
        - **EscrowContract**: Handles fund locking and milestone-based releases
        - **TradeVerification**: Manages product quality verification and shipment confirmation
        - **DisputeResolution**: Provides a mechanism for resolving disputes between parties
      `,
      faqs: [
        {
          question: 'How are smart contracts secured?',
          answer: 'Our smart contracts undergo rigorous security audits by reputable firms. We use industry-standard security patterns like reentrancy guards, input validation, and access controls. Additionally, all contracts have emergency pause functionality in case vulnerabilities are discovered.'
        },
        {
          question: 'What happens if there\'s a dispute?',
          answer: 'If a dispute arises, our DisputeResolution contract initiates a arbitration process. This involves a panel of neutral validators who review evidence submitted by both parties and vote on a resolution. The outcome is enforced automatically by the smart contract.'
        },
        {
          question: 'How are funds locked in escrow?',
          answer: 'Funds are locked in the EscrowContract when a buyer initiates a transaction. The contract holds these funds and only releases them when predefined conditions (milestones) are met and verified by both parties. This ensures that sellers receive payment only after delivering on their commitments.'
        }
      ]
    },
    {
      title: 'Milestone System',
      icon: <Clock className="w-5 h-5" />,
      content: `
        The milestone system is a core component of our platform that enables secure, incremental progress tracking and payment release. Each trade is divided into specific milestones that must be completed sequentially.
        
        Standard milestones include:
        
        1. **Contract Signing**: Initial agreement and deposit
        2. **Quality Verification**: Product quality inspection and approval
        3. **Shipping Confirmation**: Confirmation that products have been shipped
        4. **Delivery Verification**: Confirmation of successful delivery
        5. **Final Payment**: Release of remaining funds
        
        Each milestone can be customized based on the specific requirements of the trade.
      `,
      faqs: [
        {
          question: 'Can milestone requirements be customized?',
          answer: 'Yes, milestone requirements can be fully customized during the contract creation process. Buyers and sellers can agree on specific verification mechanisms, documentation requirements, and timeline for each milestone.'
        },
        {
          question: 'How are milestones verified?',
          answer: 'Milestones are verified through a combination of on-chain and off-chain processes. Digital documentation can be hashed and stored on the blockchain for verification. For physical verification, trusted third-party verifiers can be designated to confirm milestone completion.'
        },
        {
          question: 'What happens if a milestone is not completed on time?',
          answer: 'If a milestone isn\'t completed by its deadline, the contract enters a grace period where the responsible party can still complete it. If the grace period expires, either party can initiate a dispute or, depending on the contract terms, trigger an automatic resolution such as partial refund or penalty payment.'
        }
      ]
    },
    {
      title: 'Wallet Security',
      icon: <Wallet className="w-5 h-5" />,
      content: `
        Wallet security is paramount for any blockchain-based platform. Our system supports integration with various wallet types and employs multiple security measures to protect user funds.
        
        Key security features include:
        
        - **Multi-signature requirements** for large transactions
        - **Cold storage integration** for funds awaiting milestone completion
        - **Transaction limits** to prevent unauthorized large withdrawals
        - **Whitelisted addresses** for regular trading partners
        
        We recommend using hardware wallets for the highest level of security when interacting with the platform.
      `,
      faqs: [
        {
          question: 'What wallet types are supported?',
          answer: 'We support a wide range of wallets including MetaMask, WalletConnect, Ledger, Trezor, and other Ethereum-compatible wallets. This gives users flexibility to choose the security level and interface they prefer.'
        },
        {
          question: 'How does multi-signature protection work?',
          answer: 'Multi-signature protection requires multiple private keys to authorize a transaction. For high-value trades, our platform can require signatures from multiple authorized representatives from your organization, providing an additional layer of security against unauthorized transactions.'
        },
        {
          question: 'What should I do if I suspect unauthorized access?',
          answer: 'If you suspect unauthorized access, immediately access the emergency pause function in your account settings. This will temporarily freeze all transactions associated with your account. Then contact our support team who can help investigate and resolve the issue.'
        }
      ]
    },
    {
      title: 'Verification Systems',
      icon: <CheckCircle2 className="w-5 h-5" />,
      content: `
        Our verification systems ensure the authenticity and quality of agricultural products traded on the platform. We leverage blockchain technology to create immutable records of verification results.
        
        Verification methods include:
        
        - **Digital Certificates**: Quality certifications stored on-chain
        - **IoT Integration**: Real-time monitoring of product conditions
        - **Third-party Verification**: Independent quality assessment
        - **Photo and Video Documentation**: Visual evidence of product quality
        
        All verification data is cryptographically secured and linked to specific transactions.
      `,
      faqs: [
        {
          question: 'How are third-party verifiers selected?',
          answer: 'Third-party verifiers are selected from a network of trusted partners with expertise in agricultural product assessment. They undergo rigorous vetting and must maintain high ratings from users to remain in the network. Parties can also mutually agree on specific verifiers for their transactions.'
        },
        {
          question: 'Can IoT devices be integrated with the verification system?',
          answer: 'Yes, our platform supports integration with IoT devices that monitor critical factors like temperature, humidity, and location. These devices can provide real-time data that is cryptographically signed and stored on the blockchain, creating an immutable record of product conditions throughout the supply chain.'
        },
        {
          question: 'What happens if verification fails?',
          answer: 'If verification fails, the milestone cannot be completed, and the transaction will not proceed to the next stage. Both parties will be notified, and they can either renegotiate the terms, arrange for new verification, or initiate a dispute resolution process if they cannot reach an agreement.'
        }
      ]
    },
    {
      title: 'Security Protocols',
      icon: <ShieldCheck className="w-5 h-5" />,
      content: `
        Our platform incorporates multiple layers of security to protect user data, transactions, and funds. We follow industry best practices for blockchain security and regularly update our protocols.
        
        Key security measures include:
        
        - **End-to-end encryption** for all communications
        - **Multi-factor authentication** for account access
        - **Regular security audits** by third-party experts
        - **Automated monitoring** for suspicious activities
        - **Detailed audit trails** for all system interactions
        
        We maintain a bug bounty program to encourage responsible disclosure of security vulnerabilities.
      `,
      faqs: [
        {
          question: 'How often are security audits performed?',
          answer: 'We conduct comprehensive security audits quarterly with renowned blockchain security firms. Additionally, we perform continuous automated security scanning and code reviews. The results of major audits are published on our security page for transparency.'
        },
        {
          question: 'What encryption standards are used?',
          answer: 'We use industry-standard encryption protocols including TLS 1.3 for all data in transit and AES-256 for sensitive data at rest. Public-key cryptography is used for blockchain interactions, and all private communications between parties use end-to-end encryption.'
        },
        {
          question: 'How are private keys protected?',
          answer: 'Private keys are never stored on our servers. Our system is non-custodial, meaning users maintain control of their private keys at all times. For added security, we support hardware wallet integration, allowing keys to remain in secure, offline devices.'
        }
      ]
    }
  ];
  
  return (
    <AppLayout title="Documentation">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow-sm overflow-hidden sticky top-6">
            <div className="p-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Contents</h2>
            </div>
            
            <nav className="p-4">
              <ul className="space-y-2">
                {docSections.map((section, index) => (
                  <li key={index}>
                    <motion.button
                      whileHover={{ x: 4 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => toggleSection(`section-${index}`)}
                      className={`flex items-center w-full px-3 py-2 rounded-lg text-left ${
                        expandedSection === `section-${index}`
                          ? 'bg-primary-50 text-primary-700'
                          : 'text-neutral-700 hover:bg-neutral-50'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        {section.icon}
                        <span className="text-sm font-medium">{section.title}</span>
                      </div>
                    </motion.button>
                  </li>
                ))}
              </ul>
            </nav>
          </div>
        </div>
        
        <div className="lg:col-span-3 space-y-6">
          {docSections.map((section, sectionIndex) => (
            <motion.div
              key={sectionIndex}
              id={`section-${sectionIndex}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-xl shadow-sm overflow-hidden"
            >
              <motion.button
                whileHover={{ backgroundColor: '#f9fafb' }}
                onClick={() => toggleSection(`section-${sectionIndex}`)}
                className="flex justify-between items-center w-full p-6 text-left border-b border-neutral-200"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-primary-50 text-primary-600">
                    {section.icon}
                  </div>
                  <h2 className="text-xl font-semibold text-neutral-900">{section.title}</h2>
                </div>
                
                <ChevronDown
                  className={`w-5 h-5 text-neutral-500 transition-transform ${
                    expandedSection === `section-${sectionIndex}` ? 'transform rotate-180' : ''
                  }`}
                />
              </motion.button>
              
              {expandedSection === `section-${sectionIndex}` && (
                <div className="p-6">
                  <div className="prose max-w-none mb-8">
                    {section.content.split('\n').map((line, i) => (
                      <p key={i} className="mb-4 text-neutral-700">
                        {line.trim().startsWith('- ') ? (
                          <span className="block ml-4">
                            • {line.slice(2)}
                          </span>
                        ) : line.trim().startsWith('1.') || line.trim().startsWith('2.') || line.trim().startsWith('3.') || line.trim().startsWith('4.') || line.trim().startsWith('5.') ? (
                          line
                        ) : line.includes('**') ? (
                          <span dangerouslySetInnerHTML={{ 
                            __html: line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') 
                          }} />
                        ) : (
                          line
                        )}
                      </p>
                    ))}
                  </div>
                  
                  <div className="border-t border-neutral-200 pt-6">
                    <h3 className="text-lg font-semibold text-neutral-900 mb-4">Frequently Asked Questions</h3>
                    
                    <div className="space-y-4">
                      {section.faqs.map((faq, faqIndex) => (
                        <div
                          key={faqIndex}
                          className="border border-neutral-200 rounded-lg overflow-hidden"
                        >
                          <motion.button
                            whileHover={{ backgroundColor: '#f9fafb' }}
                            onClick={() => toggleFaq(`faq-${sectionIndex}-${faqIndex}`)}
                            className="flex justify-between items-center w-full p-4 text-left"
                          >
                            <h4 className="text-base font-medium text-neutral-900">{faq.question}</h4>
                            <ChevronDown
                              className={`w-5 h-5 text-neutral-500 transition-transform ${
                                expandedFaq === `faq-${sectionIndex}-${faqIndex}` ? 'transform rotate-180' : ''
                              }`}
                            />
                          </motion.button>
                          
                          {expandedFaq === `faq-${sectionIndex}-${faqIndex}` && (
                            <div className="p-4 bg-neutral-50 border-t border-neutral-200">
                              <p className="text-neutral-700">{faq.answer}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </AppLayout>
  );
};

export default Documentation;