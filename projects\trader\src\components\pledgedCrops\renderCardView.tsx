import { Calendar, Leaf, MapPin } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { Crop } from '../../utils/types'

interface CardViewProps {
  filteredCrops: Crop[]
  formatDate: (date: Date) => string
}
export function RenderCardView({ filteredCrops, formatDate }: CardViewProps) {
  const navigate = useNavigate()
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {filteredCrops.map((crop) => (
        <div
          key={crop.crop_id}
          className="bg-card-bg rounded-lg shadow-md overflow-hidden hover:shadow-lg hover:ring-2 hover:ring-accent/20 focus:ring-2 focus:ring-accent focus:bg-accent-light/10 transition-all duration-300 cursor-pointer border border-card-border"
          onClick={() => navigate(`/pledged-crops/${crop.crop_id}`)}
        >
          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-xl font-semibold text-primary-text mb-1">{crop.crop_id}</h2>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-accent-light text-accent-dark">
                    {crop.crop_grade || 'Grade A'}
                  </span>
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-status-active-bg text-status-active-text">Active</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center text-secondary-text hover:text-accent transition-colors">
                <Calendar className="h-4 w-4 mr-2 text-accent" />
                <span>{formatDate(new Date(crop.created_at))}</span>
              </div>
              <div className="flex items-center text-secondary-text hover:text-accent transition-colors">
                <Leaf className="h-4 w-4 mr-2 text-accent" />
                <span>
                  {crop.quantity} {crop.unit}
                </span>
              </div>
              <div className="flex items-center text-secondary-text hover:text-accent transition-colors">
                <MapPin className="h-4 w-4 mr-2 text-accent" />
                <span className="truncate">{crop.location.address}</span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
