from rest_framework import serializers
from django.contrib.auth import get_user_model

from .models import ProofOfUnlock, ProofOfUnlockCrop, Milestone, Document
from crops.models import Crops

User = get_user_model()

class ProofOfUnlockCropSerializer(serializers.ModelSerializer):
    crop_id = serializers.PrimaryKeyRelatedField(
        queryset=Crops.objects.all(), source='crop', write_only=False
    )

    class Meta:
        model = ProofOfUnlockCrop
        fields = ['crop_id', 'quantity', 'unit']


class MilestoneSerializer(serializers.ModelSerializer):
    class Meta:
        model = Milestone
        fields = ['id', 'name', 'description', 'amount', 'status', 'completed_date']


class DocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Document
        fields = ['id', 'filename', 'file_type', 'file_hash']


class ProofOfUnlockSerializer(serializers.ModelSerializer):
    buyer = serializers.SlugRelatedField(
        slug_field="account_address", queryset=User.objects.all()
    )
    seller = serializers.SlugRelatedField(
        slug_field="account_address", queryset=User.objects.all()
    )
    crops = ProofOfUnlockCropSerializer(source='transaction_crops', many=True)
    milestones = MilestoneSerializer(many=True, required=False)
    documents = DocumentSerializer(many=True, required=False)

    class Meta:
        model = ProofOfUnlock
        fields = [
            'tx_id', 'contract_address', 'buyer', 'seller', 'total_amount',
            'release_amount', 'status', 'created_at', 'last_updated',
            'crops', 'milestones', 'documents',
        ]

    def create(self, validated_data):
        crop_data = validated_data.pop('transaction_crops', [])
        milestone_data = validated_data.pop('milestones', [])
        document_data = validated_data.pop('documents', [])

        # Create transaction
        proof_of_unlock = ProofOfUnlock.objects.create(**validated_data)

        # Create related crops
        for item in crop_data:
            ProofOfUnlockCrop.objects.create(transaction=proof_of_unlock, **item)

        # Create milestones
        for m in milestone_data:
            Milestone.objects.create(transaction=proof_of_unlock, **m)

        # Create documents
        for d in document_data:
            Document.objects.create(transaction=proof_of_unlock, **d)

        return proof_of_unlock

    def update(self, instance, validated_data):
        # Update basic fields
        for attr, value in validated_data.items():
            if attr not in ('transaction_crops', 'milestones', 'documents'):
                setattr(instance, attr, value)
        instance.save()

        # Note: handling nested updates can be added here if needed
        return instance

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        # Order milestones by id ascending
        milestones_qs = instance.milestones.order_by('id')
        ret['milestones'] = MilestoneSerializer(milestones_qs, many=True).data
        return ret
