from django.contrib import admin
from .models import Crops, Coordinates, Location, GrowthPeriod, Fertilizer

@admin.register(Crops)
class CropsAdmin(admin.ModelAdmin):
    list_display = ('crop_id', 'crop_grade', 'quantity', 'soil_type', 'created_at')
    list_filter = ('crop_grade', 'soil_type', 'irrigation_type')
    search_fields = ('crop_id', 'soil_type')

@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ('address',)

@admin.register(Coordinates)
class CoordinatesAdmin(admin.ModelAdmin):
    list_display = ('lat', 'long')

@admin.register(GrowthPeriod)
class GrowthPeriodAdmin(admin.ModelAdmin):
    list_display = ('start_date', 'harvest_date')

@admin.register(Fertilizer)
class FertilizerAdmin(admin.ModelAdmin):
    list_display = ('name',)
    