import apiClient from './apiClient'

interface LoginResponse {
  token: string
}

export const login = async (email: string, password: string): Promise<LoginResponse | undefined> => {
  try {
    const response = await apiClient.post<LoginResponse>('/user/login/', {
      email,
      password,
    })

    if (response.data && response.data.token) {
      const { token } = response.data
      localStorage.setItem('token', token)
      return response.data
    } else {
      throw new Error('Login response data is undefined or missing token.')
    }
  } catch (error) {
    console.error('Error during login:', error)
    throw error
  }
}

export const registerUser = async (data: { name: string; email: string; password: string }) => {
  return apiClient.post('/user/register/', {
    name: data.name,
    email: data.email,
    password: data.password,
    role: 'farmer',
  })
}
