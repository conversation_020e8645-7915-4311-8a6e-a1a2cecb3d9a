import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Card } from '@/components/ui/card'
import apiClient from '@/services/apiClient'
import { useEffect, useState } from 'react'

interface NewsItem {
  id: number
  title: string
  date: string
  description: string
  article_link: string
}

export const NewsFeed = () => {
  const [news, setNews] = useState<NewsItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setIsLoading(true)
        const { data } = await apiClient.get('/news/all/')
        setNews(data.data) // Access the data array inside the response
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch news')
      } finally {
        setIsLoading(false)
      }
    }

    fetchNews()
  }, [])

  if (isLoading) {
    return (
      <Card className="bg-alt-bg p-6 shadow-md border-none">
        <p className="text-primary-text">Loading...</p>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="bg-alt-bg p-6 shadow-md border-none">
        <p className="text-red-500">Error: {error}</p>
      </Card>
    )
  }

  return (
    <Card className="bg-alt-bg p-6 shadow-md border-none">
      <h3 className="text-primary-text text-lg font-medium mb-4">Latest News</h3>
      <Accordion type="single" collapsible className="w-full">
        {news.map((item) => (
          <AccordionItem key={item.id} value={`item-${item.id}`} className="border-primary-text">
            <AccordionTrigger className="text-primary-text hover:text-primary-text/80 hover:no-underline">{item.title}</AccordionTrigger>
            <AccordionContent className="text-secondary-text">
              <p className="text-sm mb-2">{item.date}</p>
              {item.description} {/* Changed from item.content to item.description */}
              <p className="text-sm mt-2">
                <a
                  href={item.article_link}
                  className="text-link-text font-bold hover:no-underline hover:text-link-text/80"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Read more
                </a>
              </p>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </Card>
  )
}
