import { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Wallet, TrendingUp, ArrowRightLeft, Calendar, LayoutDashboard } from 'lucide-react';
import AppLayout from '../../components/layout/AppLayout';
import StatCard from '../../components/dashboard/StatCard';
import { useTransactionStore, Transaction } from '../../stores/transactionStore';
import { useNavigate } from 'react-router-dom';

// Mock chart data
const mockChartOptions = {
  chart: {
    id: 'transactions',
    toolbar: {
      show: false
    }
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
  },
  colors: ['#10B981', '#F59E0B'],
  stroke: {
    curve: 'smooth',
    width: 3
  },
  markers: {
    size: 5
  },
  grid: {
    borderColor: '#f1f1f1'
  }
};

const mockChartSeries = [
  {
    name: 'Purchases',
    data: [15000, 18000, 22000, 19000, 26000, 30000]
  },
  {
    name: 'Escrow Funds',
    data: [12000, 14000, 18000, 15000, 22000, 25000]
  }
];

const BuyerDashboard = () => {
  const navigate = useNavigate();
  const { transactions, fetchTransactions } = useTransactionStore();
  
  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);
  
  // Get active transactions
  const activeTransactions = transactions.filter(tx => tx.status === 'active');
  
  // Get upcoming milestones
  const upcomingMilestones = transactions
    .flatMap(tx => tx.milestones
      .filter(milestone => milestone.status === 'pending')
      .map(milestone => ({ ...milestone, transactionId: tx.id, productName: tx.productName }))
    )
    .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
    .slice(0, 3);
  
  // Calculate total funds in escrow
  const totalEscrow = transactions.reduce((sum, tx) => sum + tx.totalAmount - tx.releaseAmount, 0);
  
  // Calculate total funds released
  const totalReleased = transactions.reduce((sum, tx) => sum + tx.releaseAmount, 0);
  
  // Navigate to transaction details
  const handleTransactionClick = (transaction: Transaction) => {
    navigate(`/transactions?id=${transaction.id}`);
  };
  
  return (
    <AppLayout title="Buyer Dashboard">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Escrow"
          value={`$${totalEscrow.toLocaleString()}`}
          icon={<Wallet className="w-5 h-5" />}
          trend={{ value: 12.5, isPositive: true }}
        />
        <StatCard
          title="Active Contracts"
          value={activeTransactions.length}
          icon={<LayoutDashboard className="w-5 h-5" />}
          trend={{ value: 8.2, isPositive: true }}
        />
        <StatCard
          title="Total Released"
          value={`$${totalReleased.toLocaleString()}`}
          icon={<ArrowRightLeft className="w-5 h-5" />}
          trend={{ value: 5.1, isPositive: true }}
        />
        <StatCard
          title="Pending Milestones"
          value={upcomingMilestones.length}
          icon={<Calendar className="w-5 h-5" />}
          trend={{ value: 2.3, isPositive: false }}
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2 bg-white p-6 rounded-xl shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-neutral-900">Transaction Overview</h2>
            <div className="flex items-center gap-2">
              <span className="text-sm text-neutral-500">Last 6 months</span>
              <TrendingUp className="w-4 h-4 text-primary-500" />
            </div>
          </div>
          
          <div className="h-80">
            {/* We'd use react-apexcharts here */}
            <div className="text-center text-neutral-500 h-full flex items-center justify-center">
              <p>Transaction chart would render here</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-xl shadow-sm">
          <h2 className="text-lg font-semibold text-neutral-900 mb-6">Upcoming Milestones</h2>
          
          {upcomingMilestones.length > 0 ? (
            <div className="space-y-4">
              {upcomingMilestones.map((milestone) => (
                <motion.div
                  key={`${milestone.transactionId}-${milestone.id}`}
                  whileHover={{ x: 4 }}
                  className="p-4 border border-neutral-100 rounded-lg"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-neutral-900">{milestone.name}</p>
                      <p className="text-sm text-neutral-500">{milestone.productName}</p>
                    </div>
                    <p className="text-sm font-medium text-primary-600">${milestone.amount}</p>
                  </div>
                  
                  <div className="mt-2 flex items-center">
                    <Calendar className="w-4 h-4 text-neutral-400 mr-1" />
                    <p className="text-xs text-neutral-500">
                      Due: {new Date(milestone.dueDate).toLocaleDateString()}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-neutral-500">
              <p>No upcoming milestones</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-xl shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold text-neutral-900">Active Transactions</h2>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/transactions')}
            className="text-sm font-medium text-primary-600 hover:text-primary-700"
          >
            View all
          </motion.button>
        </div>
        
        {activeTransactions.length > 0 ? (
          <div className="space-y-4">
            {activeTransactions.map((transaction) => (
              <motion.div
                key={transaction.id}
                whileHover={{ x: 4 }}
                className="p-4 border border-neutral-100 rounded-lg cursor-pointer"
                onClick={() => handleTransactionClick(transaction)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium text-neutral-900">{transaction.productName}</p>
                    <p className="text-sm text-neutral-500">{transaction.quantity} units</p>
                  </div>
                  <p className="font-medium text-primary-600">${transaction.totalAmount.toLocaleString()}</p>
                </div>
                
                <div className="mt-4 flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary-500 mr-2"></div>
                    <p className="text-xs text-neutral-500">
                      Last updated: {new Date(transaction.lastUpdated).toLocaleDateString()}
                    </p>
                  </div>
                  <p className="text-xs font-medium text-primary-600">
                    {transaction.milestones.filter(m => ['completed', 'verified', 'released'].includes(m.status)).length} / {transaction.milestones.length} milestones
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="py-8 text-center text-neutral-500">
            <p>No active transactions</p>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default BuyerDashboard;