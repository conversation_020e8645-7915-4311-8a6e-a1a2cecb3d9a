#pragma version 10
#pragma typetrack false

// smart_contracts.supply_chain.contract.SupplyChain.__algopy_entrypoint_with_init() -> uint64:
main:
    intcblock 1 0 4
    bytecblock "product_count" 0x151f7c75
    txn ApplicationID
    bnz main_after_if_else@2
    // smart_contracts/supply_chain/contract.py:36
    // self.product_count = UInt64(0)
    bytec_0 // "product_count"
    intc_1 // 0
    app_global_put

main_after_if_else@2:
    // smart_contracts/supply_chain/contract.py:32
    // class SupplyChain(ARC4Contract):
    txn NumAppArgs
    bz main_bare_routing@12
    pushbytess 0x813520f5 0x51257a47 0x0c4cc514 0xbf6cdf64 0x4a0c1694 0x33b3499e 0xb53e2593 // method "register_crop((address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string))uint64", method "opt_in_asa(uint64,account)void", method "transfer_asa(uint64,account,uint64)void", method "get_product_info(uint64)(address,uint64,string,uint64,(string,(string,string)),(string,string),string,string,string[],string)", method "get_product_count()uint64", method "delete_application()void", method "update_application()void"
    txna ApplicationArgs 0
    match main_register_crop_route@5 main_opt_in_asa_route@6 main_transfer_asa_route@7 main_get_product_info_route@8 main_get_product_count_route@9 main_delete_application_route@10 main_update_application_route@11

main_after_if_else@14:
    // smart_contracts/supply_chain/contract.py:32
    // class SupplyChain(ARC4Contract):
    intc_1 // 0
    return

main_update_application_route@11:
    // smart_contracts/supply_chain/contract.py:126
    // @arc4.abimethod(allow_actions=["UpdateApplication"])
    txn OnCompletion
    intc_2 // UpdateApplication
    ==
    assert // OnCompletion is not UpdateApplication
    txn ApplicationID
    assert // can only call when not creating
    callsub update_application
    intc_0 // 1
    return

main_delete_application_route@10:
    // smart_contracts/supply_chain/contract.py:117
    // @arc4.abimethod(allow_actions=["DeleteApplication"])
    txn OnCompletion
    pushint 5 // DeleteApplication
    ==
    assert // OnCompletion is not DeleteApplication
    txn ApplicationID
    assert // can only call when not creating
    callsub delete_application
    intc_0 // 1
    return

main_get_product_count_route@9:
    // smart_contracts/supply_chain/contract.py:110
    // @arc4.abimethod(readonly=True)
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    callsub get_product_count
    itob
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_0 // 1
    return

main_get_product_info_route@8:
    // smart_contracts/supply_chain/contract.py:104
    // @arc4.abimethod(readonly=True)
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/supply_chain/contract.py:32
    // class SupplyChain(ARC4Contract):
    txna ApplicationArgs 1
    btoi
    // smart_contracts/supply_chain/contract.py:104
    // @arc4.abimethod(readonly=True)
    callsub get_product_info
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_0 // 1
    return

main_transfer_asa_route@7:
    // smart_contracts/supply_chain/contract.py:89
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/supply_chain/contract.py:32
    // class SupplyChain(ARC4Contract):
    txna ApplicationArgs 1
    btoi
    txna ApplicationArgs 2
    btoi
    txnas Accounts
    txna ApplicationArgs 3
    // smart_contracts/supply_chain/contract.py:89
    // @arc4.abimethod
    callsub transfer_asa
    intc_0 // 1
    return

main_opt_in_asa_route@6:
    // smart_contracts/supply_chain/contract.py:75
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/supply_chain/contract.py:32
    // class SupplyChain(ARC4Contract):
    txna ApplicationArgs 1
    btoi
    txna ApplicationArgs 2
    btoi
    txnas Accounts
    // smart_contracts/supply_chain/contract.py:75
    // @arc4.abimethod
    callsub opt_in_asa
    intc_0 // 1
    return

main_register_crop_route@5:
    // smart_contracts/supply_chain/contract.py:38
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/supply_chain/contract.py:32
    // class SupplyChain(ARC4Contract):
    txna ApplicationArgs 1
    // smart_contracts/supply_chain/contract.py:38
    // @arc4.abimethod
    callsub register_crop
    itob
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_0 // 1
    return

main_bare_routing@12:
    // smart_contracts/supply_chain/contract.py:32
    // class SupplyChain(ARC4Contract):
    txn OnCompletion
    bnz main_after_if_else@14
    txn ApplicationID
    !
    assert // can only call when creating
    intc_0 // 1
    return


// smart_contracts.supply_chain.contract.SupplyChain.register_crop(info: bytes) -> uint64:
register_crop:
    // smart_contracts/supply_chain/contract.py:38-42
    // @arc4.abimethod
    // def register_crop(
    //     self,
    //     info: ProductInfo,
    // ) -> UInt64:
    proto 1 1
    // smart_contracts/supply_chain/contract.py:55-65
    // itxn_result = itxn.AssetConfig(
    //     total=(info.quantity.native),
    //     decimals=0,
    //     unit_name="CROP",
    //     asset_name=op.itob(self.product_count),
    //     manager=Global.current_application_address,
    //     reserve=Global.current_application_address,
    //     freeze=Global.current_application_address,
    //     clawback=Global.current_application_address,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/supply_chain/contract.py:64
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/supply_chain/contract.py:56
    // total=(info.quantity.native),
    frame_dig -1
    pushint 42 // 42
    extract_uint64
    // smart_contracts/supply_chain/contract.py:59
    // asset_name=op.itob(self.product_count),
    intc_1 // 0
    bytec_0 // "product_count"
    app_global_get_ex
    assert // check self.product_count exists
    itob
    // smart_contracts/supply_chain/contract.py:60
    // manager=Global.current_application_address,
    global CurrentApplicationAddress
    // smart_contracts/supply_chain/contract.py:61-63
    // reserve=Global.current_application_address,
    // freeze=Global.current_application_address,
    // clawback=Global.current_application_address,
    dupn 3
    itxn_field ConfigAssetClawback
    itxn_field ConfigAssetFreeze
    itxn_field ConfigAssetReserve
    itxn_field ConfigAssetManager
    itxn_field ConfigAssetName
    // smart_contracts/supply_chain/contract.py:58
    // unit_name="CROP",
    pushbytes "CROP"
    itxn_field ConfigAssetUnitName
    // smart_contracts/supply_chain/contract.py:57
    // decimals=0,
    intc_1 // 0
    itxn_field ConfigAssetDecimals
    itxn_field ConfigAssetTotal
    // smart_contracts/supply_chain/contract.py:55
    // itxn_result = itxn.AssetConfig(
    pushint 3 // acfg
    itxn_field TypeEnum
    itxn_field Fee
    // smart_contracts/supply_chain/contract.py:55-65
    // itxn_result = itxn.AssetConfig(
    //     total=(info.quantity.native),
    //     decimals=0,
    //     unit_name="CROP",
    //     asset_name=op.itob(self.product_count),
    //     manager=Global.current_application_address,
    //     reserve=Global.current_application_address,
    //     freeze=Global.current_application_address,
    //     clawback=Global.current_application_address,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    itxn CreatedAssetID
    // smart_contracts/supply_chain/contract.py:67
    // box_key = op.itob(itxn_result.created_asset.id)
    dup
    itob
    // smart_contracts/supply_chain/contract.py:69
    // op.Box.put(box_key, info.bytes)
    frame_dig -1
    box_put
    // smart_contracts/supply_chain/contract.py:71
    // self.product_count = self.product_count + 1
    intc_1 // 0
    bytec_0 // "product_count"
    app_global_get_ex
    assert // check self.product_count exists
    intc_0 // 1
    +
    bytec_0 // "product_count"
    swap
    app_global_put
    // smart_contracts/supply_chain/contract.py:73
    // return itxn_result.created_asset.id
    retsub


// smart_contracts.supply_chain.contract.SupplyChain.opt_in_asa(product_id: uint64, framer_address: bytes) -> void:
opt_in_asa:
    // smart_contracts/supply_chain/contract.py:75-80
    // @arc4.abimethod
    // def opt_in_asa(
    //     self,
    //     product_id: UInt64,
    //     framer_address: Account,
    // ) -> None:
    proto 2 0
    // smart_contracts/supply_chain/contract.py:81-86
    // itxn.AssetTransfer(
    //     asset_receiver=framer_address,
    //     xfer_asset=product_id,
    //     asset_amount=0,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/supply_chain/contract.py:85
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/supply_chain/contract.py:84
    // asset_amount=0,
    intc_1 // 0
    itxn_field AssetAmount
    frame_dig -2
    itxn_field XferAsset
    frame_dig -1
    itxn_field AssetReceiver
    // smart_contracts/supply_chain/contract.py:81
    // itxn.AssetTransfer(
    intc_2 // axfer
    itxn_field TypeEnum
    itxn_field Fee
    // smart_contracts/supply_chain/contract.py:81-86
    // itxn.AssetTransfer(
    //     asset_receiver=framer_address,
    //     xfer_asset=product_id,
    //     asset_amount=0,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    // smart_contracts/supply_chain/contract.py:87
    // return
    retsub


// smart_contracts.supply_chain.contract.SupplyChain.transfer_asa(product_id: uint64, framer_address: bytes, quantity: bytes) -> void:
transfer_asa:
    // smart_contracts/supply_chain/contract.py:89-95
    // @arc4.abimethod
    // def transfer_asa(
    //     self,
    //     product_id: UInt64,
    //     framer_address: Account,
    //     quantity: arc4.UInt64,
    // ) -> None:
    proto 3 0
    // smart_contracts/supply_chain/contract.py:96-101
    // itxn.AssetTransfer(
    //     asset_receiver=framer_address,
    //     xfer_asset=product_id,
    //     asset_amount=quantity.native,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/supply_chain/contract.py:100
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/supply_chain/contract.py:99
    // asset_amount=quantity.native,
    frame_dig -1
    btoi
    itxn_field AssetAmount
    frame_dig -3
    itxn_field XferAsset
    frame_dig -2
    itxn_field AssetReceiver
    // smart_contracts/supply_chain/contract.py:96
    // itxn.AssetTransfer(
    intc_2 // axfer
    itxn_field TypeEnum
    itxn_field Fee
    // smart_contracts/supply_chain/contract.py:96-101
    // itxn.AssetTransfer(
    //     asset_receiver=framer_address,
    //     xfer_asset=product_id,
    //     asset_amount=quantity.native,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    // smart_contracts/supply_chain/contract.py:102
    // return
    retsub


// smart_contracts.supply_chain.contract.SupplyChain.get_product_info(product_id: uint64) -> bytes:
get_product_info:
    // smart_contracts/supply_chain/contract.py:104-105
    // @arc4.abimethod(readonly=True)
    // def get_product_info(self, product_id: UInt64) -> ProductInfo:
    proto 1 1
    // smart_contracts/supply_chain/contract.py:106
    // box_key = op.itob(product_id)
    frame_dig -1
    itob
    // smart_contracts/supply_chain/contract.py:107
    // box_data = op.Box.get(box_key)[0]  # Get first element of the tuple (the bytes)
    box_get
    pop
    // smart_contracts/supply_chain/contract.py:108
    // return ProductInfo.from_bytes(box_data)
    retsub


// smart_contracts.supply_chain.contract.SupplyChain.get_product_count() -> uint64:
get_product_count:
    // smart_contracts/supply_chain/contract.py:115
    // return self.product_count
    intc_1 // 0
    bytec_0 // "product_count"
    app_global_get_ex
    assert // check self.product_count exists
    retsub


// smart_contracts.supply_chain.contract.SupplyChain.delete_application() -> void:
delete_application:
    // smart_contracts/supply_chain/contract.py:124
    // assert Txn.sender == Global.creator_address
    txn Sender
    global CreatorAddress
    ==
    assert
    retsub


// smart_contracts.supply_chain.contract.SupplyChain.update_application() -> void:
update_application:
    // smart_contracts/supply_chain/contract.py:134
    // assert Txn.sender == Global.creator_address
    txn Sender
    global CreatorAddress
    ==
    assert
    retsub
