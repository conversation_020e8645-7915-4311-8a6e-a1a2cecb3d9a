import logging

import algokit_utils

from smart_contracts.artifacts.supply_chain.supply_chain_client import (
   SupplyChainFactory, SupplyChainClient
)

logger = logging.getLogger(__name__)


# define deployment behaviour based on supplied app spec
def deploy() -> None:
    """
    Deploys the SupplyChain smart contract application and funds it with 1 Algo.
    
    Initializes the Algorand client and deployer account from environment variables, deploys the SupplyChain application, and sends 1 Algo to the app address if the deployment was a creation or replacement. Logs the deployed application's address, app ID, and version.
    """
    algorand = algokit_utils.AlgorandClient.from_environment()
    deployer_ = algorand.account.from_environment("DEPLOYER")

    factory = algorand.client.get_typed_app_factory(
        SupplyChainFactory, default_sender=deployer_.address
    )

    app_client: SupplyChainClient
    result: algokit_utils.AppFactoryDeployResult
    app_client, result = factory.deploy(
        on_update=algokit_utils.OnUpdate.AppendApp,
        on_schema_break=algokit_utils.OnSchemaBreak.AppendApp,
    )

    if result.operation_performed in [
        algokit_utils.OperationPerformed.Create,
        algokit_utils.OperationPerformed.Replace,
    ]:
        algorand.send.payment(
            algokit_utils.PaymentParams(
                amount=algokit_utils.AlgoAmount(algo=1),
                sender=deployer_.address,
                receiver=app_client.app_address,
            )
        )

    logger.info(f"Deployed app at {app_client.app_address}")
    logger.info(f"Deployed app id {result.app.app_id}")
    logger.info(f"Deployed app version {result.app.version}")
    