# Generated by Django 5.2 on 2025-04-22 08:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("transactions", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="monthlyexpense",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="monthly_expenses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="transaction",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="transactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="monthlyexpense",
            index=models.Index(
                fields=["user", "year", "month"], name="transaction_user_id_762924_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="monthlyexpense",
            unique_together={("user", "year", "month", "transaction_type")},
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["user", "timestamp"], name="transaction_user_id_4c240e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["user", "transaction_type"],
                name="transaction_user_id_98a6b3_idx",
            ),
        ),
    ]
