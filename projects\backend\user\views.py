from django.conf import settings
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ValidationError
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from rest_framework import status
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.decorators import (
    api_view,
    authentication_classes,
    permission_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from agritram.permissions import CustomPermission

from .models import User
from .serializers import UserSerializer


@api_view(["POST"])
def register(request):
    """
    Register a new user and send an activation email.
    """
    if request.method == "POST":
        serializer = UserSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            # Generate token and UID for activation link
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))

            # Construct activation URL
            activation_url = f"{settings.FRONTEND_URL}/activate-account/{uid}/{token}/"

            # Prepare email context
            context = {
                "activation_url": activation_url,
                "user": user,
            }
            subject = "Activate Your Account"
            message = render_to_string("emails/activate_account_email.html", context)
            plain_message = f"Hi {user.name},\n\nThank you for registering. Please click the link below to activate your account:\n{activation_url}\n\nIf you did not register, please ignore this email."

            # Send email
            try:
                send_mail(
                    subject,
                    plain_message,
                    settings.EMAIL_HOST_USER,
                    [user.email],
                    html_message=message,
                )
            except Exception as e:
                print(e)
                return Response(
                    {
                        "error": "Failed to send activation email. Please try again later."
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            return Response(
                {
                    "user": serializer.data,
                    "message": "Registration successful. An activation link has been sent to your email.",
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def activate_account(request):
    """
    Activate the user's account.
    """
    uidb64 = request.data.get("uid")
    token = request.data.get("token")

    if not uidb64 or not token:
        return Response(
            {"error": "Invalid activation link."}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        return Response(
            {"error": "Invalid activation link."}, status=status.HTTP_400_BAD_REQUEST
        )

    if default_token_generator.check_token(user, token):
        user.is_active = True
        user.save()
        return Response(
            {"message": "Account activated successfully."}, status=status.HTTP_200_OK
        )
    else:
        return Response(
            {"error": "Activation link is invalid or has expired."},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["POST"])
def login(request):
    """
    Log in a user and return a token.
    """
    email = request.data.get("email")
    password = request.data.get("password")
    if not email or not password:
        return Response(
            {"error": "Email and password are required"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        user = User.objects.get(email=email)
        if not user.is_active:
            return Response(
                {"error": "User account is inactive"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # if not user.active:
        #     return Response({"error": "User account is inactive"}, status=status.HTTP_403_FORBIDDEN)
        if user.check_password(password):
            user.last_login = timezone.now()
            user.save()
            Token.objects.filter(user=user).delete()
            token, created = Token.objects.get_or_create(user=user)
            return Response(
                {
                    "token": token.key,
                    "message": "Login successful",
                    "user": UserSerializer(user).data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "Invalid password"}, status=status.HTTP_400_BAD_REQUEST
            )
    except User.DoesNotExist:
        return Response(
            {"error": "User does not exist"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET", "PUT"])
@permission_classes([IsAuthenticated, CustomPermission])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def update_user(request):
    """
    Update an existing user.
    """
    user = request.user

    if request.method == "GET":
        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    if request.method == "PUT":
        if not request.data:
            return Response(
                {"detail": "No data provided to update."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = UserSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["PUT"])
@permission_classes([IsAuthenticated, CustomPermission])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def reset_password(request):
    """
    Allow authenticated users to change their password.
    """
    user = request.user
    old_password = request.data.get("old_password")
    new_password = request.data.get("new_password")

    if not old_password or not new_password:
        return Response(
            {"error": "Old and new passwords are required."},
            status=status.HTTP_400_BAD_REQUEST,
        )

    if not user.check_password(old_password):
        return Response(
            {"error": "Old password is incorrect."}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        validate_password(new_password, user)
    except ValidationError as e:
        return Response({"error": e.messages}, status=status.HTTP_400_BAD_REQUEST)

    user.set_password(new_password)
    user.save()
    return Response(
        {"message": "Password has been changed successfully."},
        status=status.HTTP_200_OK,
    )


@api_view(["POST"])
def forgot_password(request):
    """
    Handle forgotten passwords by sending a reset link.
    """
    email = request.data.get("email")
    if not email:
        return Response(
            {"error": "Email is required."}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        return Response(
            {"error": "User with this email does not exist."},
            status=status.HTTP_400_BAD_REQUEST,
        )

    token = default_token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))

    # Construct password reset URL
    reset_url = f"{settings.FRONTEND_URL}/reset-password/{uid}/{token}/"

    # Send email
    context = {
        "reset_url": reset_url,
        "user": user,
    }
    subject = "Password Reset Request"
    message = render_to_string("emails/reset_password_email.html", context)
    plain_message = f"Hi {user.name},\n\nYou requested a password reset. Click the link below to reset your password:\n{reset_url}\n\nIf you did not request this, please ignore this email."
    try:
        send_mail(
            subject,
            plain_message,
            settings.EMAIL_HOST_USER,
            [user.email],
            html_message=message,
        )
    except Exception as e:
        print(e)
        return Response(
            {"error": "Failed to send email. Please try again later."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    return Response(
        {"message": "Password reset link has been sent to your email."},
        status=status.HTTP_200_OK,
    )


@api_view(["POST"])
def reset_password_confirm(request):
    """
    Confirm the password reset by setting the new password.
    """
    uidb64 = request.data.get("uid")
    token = request.data.get("token")
    new_password = request.data.get("password")

    if not uidb64 or not token or not new_password:
        return Response({"error": "Invalid data."}, status=status.HTTP_400_BAD_REQUEST)

    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    if user is not None and default_token_generator.check_token(user, token):
        try:
            validate_password(new_password, user)
        except ValidationError as e:
            return Response({"error": e.messages}, status=status.HTTP_400_BAD_REQUEST)

        user.set_password(new_password)
        user.save()
        return Response(
            {"message": "Password has been reset successfully."},
            status=status.HTTP_200_OK,
        )
    else:
        return Response(
            {"error": "Invalid token or user ID."}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def logout(request):
    """
    Log out a user by deleting their token.
    """
    user = request.user
    Token.objects.filter(user=user).delete()
    return Response({"message": "Logout successful"}, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_user_name(request):
    """
    Get the authenticated user's name.
    """
    user = request.user
    return Response({"name": user.name}, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def is_connected_wallet(request):
    """
    Check if the user is connected to a wallet.
    """
    user = request.user
    return Response(
        {
            "is_connected": user.wallet_address is not None,
            "wallet_address": user.wallet_address,
        },
        status=status.HTTP_200_OK,
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def connect_account(request):
    """
    Connect the user to a wallet.
    """
    user = request.user
    account_address = request.data.get("account_address")
    if not account_address:
        return Response(
            {"error": "Wallet address is required."}, status=status.HTTP_400_BAD_REQUEST
        )
    try:
        user.account_address = account_address
        user.save()
    except Exception as e:
        print(e)
        return Response(
            {"error": "Failed to connect wallet. Please try again later."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
    return Response(
        {"message": "Wallet connected successfully."}, status=status.HTTP_200_OK
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def opt_in_account(request):
    """
    Opt-in to a smart contract.
    """
    user = request.user
    try:
        user.opt_in = True
        user.save()
    except Exception as e:
        print(e)
        return Response(
            {"error": "Failed to opt-in. Please try again later."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
    return Response({"message": "Opt-in successful."}, status=status.HTTP_200_OK)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def opt_in_asset(request):
    pass
