from django.core.management.base import BaseCommand
from user.models import User


class Command(BaseCommand):
    help = 'Create multiple users from a predefined list'

    def handle(self, *args, **kwargs):
        users = [
            {"name": "<PERSON>", "email": "<EMAIL>",
                "password": "password123"},
            {"name": "<PERSON>", "email": "<EMAIL>",
                "password": "password123"},
            {"name": "<PERSON>", "email": "<EMAIL>",
                "password": "password123"}
        ]


        for user_data in users:
            user = User.objects.create(
                name=user_data["name"],
                email=user_data["email"],
                password=user_data["password"],
                is_active=True,
                is_staff=False,
                is_superuser=False,
            )
            # Ensures the password is hashed
            user.set_password(user_data["password"])
            user.save()
            self.stdout.write(self.style.SUCCESS(
                f'Successfully created user {user.name}'))
