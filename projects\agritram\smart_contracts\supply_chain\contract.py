from algopy import Account, ARC4Contract, Global, Txn, UInt64, arc4, itxn, op


class Coordinates(arc4.Struct):
    lat: arc4.String
    long: arc4.String


class Location(arc4.Struct):
    address: arc4.String
    coordinates: Coordinates


class GrowthPeriod(arc4.Struct):
    startDate: arc4.String
    harvestDate: arc4.String


class ProductInfo(arc4.Struct):
    farmer_address: arc4.Address
    cost: arc4.UInt64
    crop_greade: arc4.String
    quantity: arc4.UInt64
    location: Location
    growth_period: GrowthPeriod
    soil_type: arc4.String
    irrigation_type: arc4.String
    fertilizers_used: arc4.DynamicArray[arc4.String]
    certification: arc4.String


class SupplyChain(ARC4Contract):
    product_count: UInt64

    def __init__(self) -> None:
        self.product_count = UInt64(0)

    @arc4.abimethod
    def register_crop(
        self,
        info: ProductInfo,
    ) -> UInt64:
        # Create ASA representing this crop
        """
        Registers a new crop by creating an Algorand Standard Asset (ASA) and storing its product information.
        
        Creates an ASA representing the crop with properties derived from the provided `ProductInfo`. The ASA is configured with zero decimals and assigned to the contract's address. The product information is stored in a box indexed by the ASA asset ID. Increments the product count and returns the new ASA asset ID.
        
        Args:
            info: Detailed information about the crop to register.
        
        Returns:
            The asset ID of the newly created ASA representing the crop.
        """
        itxn_result = itxn.AssetConfig(
            total=(info.quantity.native),
            decimals=0,
            unit_name="CROP",
            asset_name=op.itob(self.product_count),
            manager=Global.current_application_address,
            reserve=Global.current_application_address,
            freeze=Global.current_application_address,
            clawback=Global.current_application_address,
            fee=Global.min_txn_fee,
        ).submit()

        box_key = op.itob(itxn_result.created_asset.id)

        op.Box.put(box_key, info.bytes)

        self.product_count = self.product_count + 1

        return itxn_result.created_asset.id

    @arc4.abimethod
    def opt_in_asa(
        self,
        product_id: UInt64,
        framer_address: Account,
    ) -> None:
        itxn.AssetTransfer(
            asset_receiver=framer_address,
            xfer_asset=product_id,
            asset_amount=0,
            fee=Global.min_txn_fee,
        ).submit()
        return

    @arc4.abimethod
    def transfer_asa(
        self,
        product_id: UInt64,
        framer_address: Account,
        quantity: arc4.UInt64,
    ) -> None:
        itxn.AssetTransfer(
            asset_receiver=framer_address,
            xfer_asset=product_id,
            asset_amount=quantity.native,
            fee=Global.min_txn_fee,
        ).submit()
        return

    @arc4.abimethod(readonly=True)
    def get_product_info(self, product_id: UInt64) -> ProductInfo:
        box_key = op.itob(product_id)
        box_data = op.Box.get(box_key)[0]  # Get first element of the tuple (the bytes)
        return ProductInfo.from_bytes(box_data)

    @arc4.abimethod(readonly=True)
    def get_product_count(self) -> UInt64:
        """
        Returns the total number of registered products in the supply chain.
        """
        return self.product_count

    @arc4.abimethod(allow_actions=["DeleteApplication"])
    def delete_application(self) -> None:
        """
        Deletes the application if the transaction sender is the creator.
        
        The application can only be deleted by its creator.
        """
        assert Txn.sender == Global.creator_address

    @arc4.abimethod(allow_actions=["UpdateApplication"])
    def update_application(self) -> None:
        """
        Allows the application to be updated only by its creator.
        
        Raises:
            AssertionError: If the transaction sender is not the creator address.
        """
        assert Txn.sender == Global.creator_address
