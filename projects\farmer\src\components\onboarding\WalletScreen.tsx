import { useWallet } from '@txnlab/use-wallet-react'
import { Shield, Wallet } from 'lucide-react'
import { Dispatch, SetStateAction } from 'react'
import WalletModal from '../../components/ConnectWallet'

interface WalletScreenProps {
  handleNext: () => void
  handleBack: () => void
  isLoading: boolean
  openWalletModal: boolean
  toggleWalletModal: () => void
  setIsLoading: Dispatch<SetStateAction<boolean>>
}

/**
 * Displays the onboarding screen for connecting a MyAlgo wallet in the FarmChain application.
 *
 * Guides users through the process of installing, setting up, and connecting their wallet, and provides controls to proceed or go back in the onboarding flow.
 */
export default function WalletScreen({
  handleNext,
  handleBack,
  isLoading,
  setIsLoading,
  openWalletModal,
  toggleWalletModal,
}: WalletScreenProps) {
  const { activeAddress } = useWallet()

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <WalletModal
        isOpen={openWalletModal}
        onClose={toggleWalletModal}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        handleNext={handleNext}
      />
      <div className="text-center">
        <h2 className="text-3xl font-bold text-primary-text mb-4">Connect Your Wallet</h2>
        <p className="text-primary-text opacity-60">
          To securely store your digital assets and interact with FarmChain, you'll need to connect your MyAlgo wallet.
        </p>
      </div>

      <div className="bg-alt-bg p-6 rounded-lg space-y-6 shadow-md">
        <div className="flex items-start gap-4">
          <Shield className="h-6 w-6 text-link-text mt-1" />
          <div>
            <h3 className="text-primary-text font-semibold mb-2">Why do I need a wallet?</h3>
            <p className="text-primary-text opacity-60">Your wallet acts as your secure digital identity on FarmChain. It allows you to:</p>
            <ul className="list-disc list-inside text-primary-text opacity-60 mt-2 space-y-1">
              <li>Store and manage your farm tokens</li>
              <li>Sign transactions securely</li>
              <li>Access financial services</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-border-primary pt-6">
          <h3 className="text-primary-text font-semibold mb-4">Connection Steps:</h3>
          <ol className="space-y-4">
            <li className="flex items-start gap-3">
              <span className="bg-button-bg text-button-text w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                1
              </span>
              <div>
                <p className="text-primary-text">Install MyAlgo Wallet</p>
                <p className="text-primary-text opacity-60 text-sm">
                  If you haven't already, install the MyAlgo wallet extension from the official website
                </p>
              </div>
            </li>
            <li className="flex items-start gap-3">
              <span className="bg-button-bg text-button-text w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                2
              </span>
              <div>
                <p className="text-primary-text">Create or Import Wallet</p>
                <p className="text-primary-text opacity-60 text-sm">Set up a new wallet or import an existing one using your seed phrase</p>
              </div>
            </li>
            <li className="flex items-start gap-3">
              <span className="bg-button-bg text-button-text w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                3
              </span>
              <div>
                <p className="text-primary-text">Connect to FarmChain</p>
                <p className="text-primary-text opacity-60 text-sm">Click the connect button below and approve the connection request</p>
              </div>
            </li>
          </ol>
        </div>
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-border-primary text-primary-text opacity-60 hover:text-primary-text hover:opacity-100 hover:border-border-primary duration-300"
        >
          Back
        </button>
        <button
          onClick={toggleWalletModal}
          className="bg-button-bg text-button-text px-6 py-2 rounded-lg hover:bg-button-bg-hover duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed border border-border-dotted border-dotted"
        >
          <Wallet className="h-5 w-5 mr-2" />
          {activeAddress ? 'Change Wallet' : 'Connect Wallet'}
        </button>
        {activeAddress && (
          <button
            onClick={handleNext}
            className="px-6 py-2 rounded-lg border border-border-primary text-primary-text opacity-60 hover:text-primary-text hover:opacity-100 hover:border-border-primary duration-300"
          >
            Next
          </button>
        )}
      </div>
    </div>
  )
}
