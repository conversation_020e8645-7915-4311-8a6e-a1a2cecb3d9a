import logging
from http import client
from pydoc import cli
import algokit_utils
import pytest
from algokit_utils import (
    AlgoAmount,
    AlgorandClient,
    AppFactoryDeployResult,
    SendSingleTransactionResult,
    SigningAccount,
)

from smart_contracts.artifacts.supply_chain.supply_chain_client import (
    Coordinates,
    GrowthPeriod,
    ProductInfo,
    RegisterCropArgs,
    SupplyChainClient,
    SupplyChainFactory,
    Location,
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@pytest.fixture(scope="session")
def factory_supply_chain(algorand_client: AlgorandClient, deployer: SigningAccount) -> SupplyChainFactory:
    return algorand_client.client.get_typed_app_factory(
        SupplyChainFactory,
        default_sender=deployer.address,
    )

@pytest.fixture(scope="function")
def deploy_supply_chain(factory_supply_chain: SupplyChainFactory) -> tuple[SupplyChainClient, AppFactoryDeployResult]:
    result = factory_supply_chain.deploy(
        on_update=algokit_utils.OnUpdate.ReplaceApp,
        on_schema_break=algokit_utils.OnSchemaBreak.ReplaceApp,
    )
    assert result is not None, "Deploy result should not be None"
    client, deploy_result = result
    logger.info(f"App ID: {deploy_result.app.app_id}")
    logger.info(f"App Address: {deploy_result.app.app_address}")
    return result

@pytest.fixture(scope="function")
def supply_chain_payment(
    algorand_client: AlgorandClient,
    deployer: SigningAccount,
    deploy_supply_chain: tuple[SupplyChainClient, AppFactoryDeployResult],
) -> SendSingleTransactionResult:
    _, deploy_result = deploy_supply_chain
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_address, "App address not found"
    
    payment_result = algorand_client.send.payment(
        algokit_utils.PaymentParams(
            amount=AlgoAmount(algo=100),
            sender=deployer.address,
            receiver=deploy_result.app.app_address,
        )
    )
    assert payment_result is not None, "Payment result should not be None"
    return payment_result

def test_deploy_and_register_crop(
    deployer: SigningAccount,
    deploy_supply_chain: tuple[SupplyChainClient, AppFactoryDeployResult],
    supply_chain_payment: SendSingleTransactionResult,
) -> None:
    # Arrange
    client, deploy_result = deploy_supply_chain
    
    # Verify the app was deployed successfully
    assert deploy_result.app is not None, "App deployment failed"
    assert deploy_result.app.app_id > 0, "Invalid app ID"
    assert deploy_result.app.app_address, "App address not found"
    
    # Verify the payment transaction
    assert supply_chain_payment.transaction is not None, "Payment transaction failed"
    assert supply_chain_payment.tx_id is not None, "Payment transaction ID not found"
    
    # Create crop registration data
    crop = RegisterCropArgs(
        info=ProductInfo(
            farmer_address=deployer.address,
            cost=100_000_000,  # 100 Algos in microAlgos
            crop_greade="A",
            quantity=100_000_000,
            location=Location(
                address="123 Farm Street, Agricultural Zone",
                coordinates=Coordinates(
                    lat="12.23453",
                    long="-9.87654",
                )
            ),
            growth_period=GrowthPeriod(
                startDate="12/12/2022",
                harvestDate="19/12/2022",
            ),
            soil_type="Clay",
            irrigation_type="Full",
            fertilizers_used=["Organic Fertilizer"],
            certification="A Grade",
        )
    )

    # Act: Register the crop
    register_result = client.send.register_crop(
        args=crop,
        params=algokit_utils.CommonAppCallParams(
            sender=deployer.address,
            signer=deployer.signer,
            note="Registering new crop in supply chain",
        ),
    )
    
    # Assert: Verify the registration transaction
    assert register_result.transaction is not None, "Crop registration transaction failed"
    assert register_result.tx_id is not None, "Crop registration transaction ID not found"
    
    # Check the return value if available
    if register_result.abi_return is not None:
        logger.info(f"Crop registration return value: {register_result.abi_return}")
