import { ReactNode } from 'react'

interface AuthLayoutProps {
  children: ReactNode
  title: string
  subtitle: string
}

const AuthLayout = ({ children, title, subtitle }: AuthLayoutProps) => {
  return (
    <div className="min-h-screen bg-main-bg flex items-center justify-center p-4">
      <div className="w-full max-w-md animate-fadeIn">
        <div className="bg-alt-bg rounded-lg shadow-xl p-8">
          <h1 className="text-3xl font-bold text-center text-primary-text mb-2">{title}</h1>
          <p className="text-secondary-foreground text-center mb-8">{subtitle}</p>
          {children}
        </div>
      </div>
    </div>
  )
}

export default AuthLayout
