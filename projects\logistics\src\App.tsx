import { Toaster as Sonner } from '@/components/ui/sonner'
import { TooltipProvider } from '@/components/ui/tooltip'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { SupportedWallet, WalletId, WalletManager, WalletProvider } from '@txnlab/use-wallet-react'
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom'
import Toaster from './components/ui/toaster'
import ActivateAccount from './pages/ActivateAccount'
import ForgotPassword from './pages/ForgotPassword'
import Login from './pages/Login'
import NotFound from './pages/NotFound'
import { OnboardingFlow } from './pages/OnboardingFlow'
import Register from './pages/Register'
import ResetPassword from './pages/ResetPassword'
// import AssetOptIn from './pages/test'

import CryptoTransfer from './pages/CryptoTransfer'
import DashboardLayout from './pages/Dashboard'
import { KCTToKTTConversion } from './pages/KCTToKTTConversion'
import { KTTToKCTConversion } from './pages/KTTToKCTConversion'
import PaymentInterface from './pages/PaymentInterface'
import { PledgePage } from './pages/PledgePage'
import { TokenBurn } from './pages/TokenBurn'
import TransactionHistory from './pages/TransactionHistory'
import { getAlgodConfigFromViteEnvironment, getKmdConfigFromViteEnvironment } from './utils/network/getAlgoClientConfigs'
import { VITE_ALGOD_NETWORK } from './utils/variable'
const queryClient = new QueryClient()

let supportedWallets: SupportedWallet[]
if (VITE_ALGOD_NETWORK === '') {
  const kmdConfig = getKmdConfigFromViteEnvironment()
  supportedWallets = [
    {
      id: WalletId.KMD,
      options: {
        baseServer: kmdConfig.server,
        token: String(kmdConfig.token),
        port: String(kmdConfig.port),
      },
    },
  ]
} else {
  supportedWallets = [
    { id: WalletId.DEFLY },
    { id: WalletId.PERA },
    { id: WalletId.EXODUS },
    // If you are interested in WalletConnect v2 provider
    // refer to https://github.com/TxnLab/use-wallet for detailed integration instructions
  ]
}

/**
 * The main application component that sets up routing, wallet management, and global providers.
 *
 * Wraps the app with React Query, tooltip, notification, routing, and wallet context providers, and defines all application routes.
 */
export default function App() {
  const algodConfig = getAlgodConfigFromViteEnvironment()

  const walletManager = new WalletManager({
    wallets: supportedWallets,
    defaultNetwork: algodConfig.network,
    networks: {
      [algodConfig.network]: {
        algod: {
          baseServer: algodConfig.server,
          port: algodConfig.port,
          token: String(algodConfig.token),
        },
      },
    },
    options: {
      resetNetwork: true,
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <WalletProvider manager={walletManager}>
            <Routes>
              <Route path="/" element={<Navigate to="/login" replace />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/activate-account/:uid/:token" element={<ActivateAccount />} />
              <Route path="/reset-password/:uid/:token" element={<ResetPassword />} />
              <Route path="/onboarding" element={<OnboardingFlow />} />
              {/* <Route path="/test" element={<TransactionDetails />} /> */}
              <Route path="/dashboard" element={<DashboardLayout />} />
              <Route path="/buy" element={<PaymentInterface />} />
              <Route path="/burn" element={<TokenBurn />} />
              <Route path="/convert" element={<KTTToKCTConversion />} />
              <Route path="/sell" element={<KCTToKTTConversion />} />
              <Route path="/transactions" element={<TransactionHistory />} />
              <Route path="/transfer" element={<CryptoTransfer />} />
              <Route path="/pledge" element={<PledgePage />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </WalletProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  )
}
