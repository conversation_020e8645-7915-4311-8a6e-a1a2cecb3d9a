{"name": "sasya-frontend", "version": "0.1.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "moham<PERSON><PERSON><PERSON><PERSON>ju<PERSON>@gmail.com"}, "private": true, "type": "module", "engines": {"node": ">=20.0", "npm": ">=9.0"}, "devDependencies": {"@algorandfoundation/algokit-client-generator": "^5.0.0", "@eslint/js": "^9.9.0", "@playwright/test": "^1.35.0", "@tailwindcss/typography": "^0.5.15", "@types/jest": "29.5.2", "@types/node": "^18.17.14", "@types/react": "^18.2.11", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.14", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.0.19", "playwright": "^1.35.0", "postcss": "^8.4.24", "tailwindcss": "3.3.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6", "typescript-eslint": "^8.0.1", "vite": "^5.0.0", "vite-plugin-node-polyfills": "^0.22.0"}, "dependencies": {"@algorandfoundation/algokit-utils": "^9.0.0", "@blockshake/defly-connect": "^1.2.1", "@daffiwallet/connect": "^1.0.3", "@hookform/resolvers": "^3.9.0", "@perawallet/connect": "^1.4.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@txnlab/use-wallet": "^4.0.0", "@txnlab/use-wallet-react": "^4.0.0", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "algosdk": "^3.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "daisyui": "^4.0.0", "date-fns": "^3.6.0", "decimal.js": "^10.5.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.10.5", "global": "^4.4.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "notistack": "^3.0.1", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tslib": "^2.6.2", "vaul": "^0.9.3", "yup": "^1.6.1", "zod": "^3.23.8", "zustand": "^5.0.5"}, "scripts": {"generate:app-clients": "algokit project link --all", "dev": "npm run generate:app-clients && vite", "build": "npm run generate:app-clients && tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "preview": "vite preview", "build:dev": "vite build --mode development"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"ws@>7.0.0 <7.5.9": "7.5.10"}}