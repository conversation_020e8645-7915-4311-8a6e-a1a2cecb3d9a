import { useWallet } from '@txnlab/use-wallet-react'
import { Shield, Wallet } from 'lucide-react'
import { Dispatch, SetStateAction } from 'react'
import WalletModal from '../../components/ConnectWallet'

interface WalletScreenProps {
  handleNext: () => void
  handleBack: () => void
  isLoading: boolean
  openWalletModal: boolean
  toggleWalletModal: () => void
  setIsLoading: Dispatch<SetStateAction<boolean>>
}

/**
 * Displays the onboarding screen for connecting a MyAlgo wallet to FarmChain.
 *
 * Guides users through the process of installing, setting up, and connecting their wallet, and provides navigation controls for the onboarding flow.
 *
 * @param handleNext - Callback to proceed to the next onboarding step.
 * @param handleBack - Callback to return to the previous onboarding step.
 * @param isLoading - Indicates whether a wallet connection operation is in progress.
 * @param setIsLoading - Function to update the loading state.
 * @param openWalletModal - Controls the visibility of the wallet connection modal.
 * @param toggleWalletModal - Toggles the wallet modal open or closed.
 */
export default function WalletScreen({
  handleNext,
  handleBack,
  isLoading,
  setIsLoading,
  openWalletModal,
  toggleWalletModal,
}: WalletScreenProps) {
  const { activeAddress } = useWallet()

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <WalletModal
        isOpen={openWalletModal}
        onClose={toggleWalletModal}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        handleNext={handleNext}
      />
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">Connect Your Wallet</h2>
        <p className="text-[#BDBDBD]">
          To securely store your digital assets and interact with FarmChain, you'll need to connect your MyAlgo wallet.
        </p>
      </div>

      <div className="bg-[#303030] p-6 rounded-lg space-y-6">
        <div className="flex items-start gap-4">
          <Shield className="h-6 w-6 text-[#00E676] mt-1" />
          <div>
            <h3 className="text-white font-semibold mb-2">Why do I need a wallet?</h3>
            <p className="text-[#BDBDBD]">Your wallet acts as your secure digital identity on FarmChain. It allows you to:</p>
            <ul className="list-disc list-inside text-[#BDBDBD] mt-2 space-y-1">
              <li>Store and manage your farm tokens</li>
              <li>Sign transactions securely</li>
              <li>Access financial services</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-[#424242] pt-6">
          <h3 className="text-white font-semibold mb-4">Connection Steps:</h3>
          <ol className="space-y-4">
            <li className="flex items-start gap-3">
              <span className="bg-[#1B5E20] text-white w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">1</span>
              <div>
                <p className="text-white">Install MyAlgo Wallet</p>
                <p className="text-[#BDBDBD] text-sm">
                  If you haven't already, install the MyAlgo wallet extension from the official website
                </p>
              </div>
            </li>
            <li className="flex items-start gap-3">
              <span className="bg-[#1B5E20] text-white w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">2</span>
              <div>
                <p className="text-white">Create or Import Wallet</p>
                <p className="text-[#BDBDBD] text-sm">Set up a new wallet or import an existing one using your seed phrase</p>
              </div>
            </li>
            <li className="flex items-start gap-3">
              <span className="bg-[#1B5E20] text-white w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">3</span>
              <div>
                <p className="text-white">Connect to FarmChain</p>
                <p className="text-[#BDBDBD] text-sm">Click the connect button below and approve the connection request</p>
              </div>
            </li>
          </ol>
        </div>
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-[#424242] text-[#BDBDBD] hover:text-white hover:border-white transition-colors duration-300"
        >
          Back
        </button>
        <button
          onClick={toggleWalletModal}
          className="bg-[#1B5E20] text-white px-6 py-2 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Wallet className="h-5 w-5 mr-2" />
          {activeAddress ? 'Change Wallet' : 'Connect Wallet'}
        </button>
        {activeAddress && (
          <button
            onClick={handleNext}
            className="px-6 py-2 rounded-lg border border-[#424242] text-[#BDBDBD] hover:text-white hover:border-white transition-colors duration-300"
          >
            Next
          </button>
        )}
      </div>
    </div>
  )
}
