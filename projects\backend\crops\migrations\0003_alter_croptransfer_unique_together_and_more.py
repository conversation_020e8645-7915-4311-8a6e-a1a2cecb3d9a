# Generated by Django 5.2 on 2025-04-25 11:06

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("crops", "0002_remove_crops_cost_remove_crops_farmer_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="croptransfer",
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name="croptransfer",
            name="crop",
        ),
        migrations.RemoveField(
            model_name="croptransfer",
            name="from_user",
        ),
        migrations.RemoveField(
            model_name="croptransfer",
            name="to_user",
        ),
        migrations.AddField(
            model_name="croptransfer",
            name="crop",
            field=models.ManyToManyField(related_name="transfers", to="crops.crops"),
        ),
        migrations.AddField(
            model_name="croptransfer",
            name="from_user",
            field=models.ManyToManyField(
                related_name="sent_transactions", to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="croptransfer",
            name="to_user",
            field=models.ManyToManyField(
                related_name="received_transactions", to=settings.AUTH_USER_MODEL
            ),
        ),
    ]
