import { ProductHistory } from '@/components/pledgedCrops/ProductHistory'
import apiClient from '@/services/apiClient'
import { Crop, OwnershipTransfer } from '@/utils/types'
import { ArrowLeft, BadgeCheck, BarChart3, Clock, Droplet, Loader2, MapPin, Warehouse } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

function formatDate(date: Date) {
  return date.toLocaleDateString()
}

export function CropDetails() {
  const { id } = useParams<{ id: string }>()
  const [selectedCrop, setSelectedCrop] = useState<Crop | null>(null)
  const [history, setHistory] = useState<OwnershipTransfer[]>([])
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    async function fetchCropData() {
      setLoading(true)
      try {
        const [cropResponse, transactionResponse] = await Promise.all([
          apiClient.get<Crop>(`/crops/crops/${id}/`),
          apiClient.get<OwnershipTransfer[]>(`/crops/crops/${id}/transactions/`),
        ])
        setSelectedCrop(cropResponse.data)
        setHistory(transactionResponse.data)
      } catch (error) {
        console.error('Failed to fetch crop data:', error)
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchCropData()
    }
  }, [id])

  // Full-screen loading spinner
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-main-bg">
        <Loader2 className="animate-spin h-12 w-12 text-accent" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-main-bg py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <button
          onClick={() => navigate(-1)}
          className="flex items-center text-secondary-text hover:text-accent mb-6 transition-colors duration-200"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Crops List
        </button>

        <div className="bg-card-bg rounded-lg overflow-hidden shadow-md border border-card-border">
          <div className="p-6 border-b border-card-border bg-accent-light/5">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-primary-text">{selectedCrop?.crop_id}</h1>
              <span className="px-3 py-1 text-sm font-medium rounded-full bg-status-active-bg text-status-active-text">Active</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Location */}
              <div className="bg-card-bg rounded-lg p-4 border border-card-border hover:border-accent/20 transition-colors duration-200">
                <h2 className="text-lg font-semibold text-primary-text mb-4 flex items-center">
                  <MapPin className="h-5 w-5 mr-2 text-accent" />
                  Location Details
                </h2>
                <div className="flex items-center gap-4">
                  <span className="text-secondary-text min-w-[120px]">Address:</span>
                  <span className="text-primary-text">{selectedCrop?.location?.address}</span>
                </div>
              </div>

              {/* Irrigation */}
              <div className="bg-card-bg rounded-lg p-4 border border-card-border hover:border-accent/20 transition-colors duration-200">
                <h2 className="text-lg font-semibold text-primary-text mb-4 flex items-center">
                  <Droplet className="h-5 w-5 mr-2 text-accent" />
                  Irrigation Details
                </h2>
                <div className="flex items-center gap-4 mb-2">
                  <span className="text-secondary-text min-w-[120px]">Quantity:</span>
                  <span className="text-primary-text">
                    {selectedCrop?.quantity} {selectedCrop?.unit}
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-secondary-text min-w-[120px]">Irrigation:</span>
                  <span className="text-primary-text">{selectedCrop?.irrigation_type}</span>
                </div>
              </div>

              {/* Growth Period */}
              <div className="bg-card-bg rounded-lg p-4 border border-card-border hover:border-accent/20 transition-colors duration-200">
                <h2 className="text-lg font-semibold text-primary-text mb-4 flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-accent" />
                  Growth Period
                </h2>
                <div className="flex items-center gap-4 mb-2">
                  <span className="text-secondary-text min-w-[120px]">Start Date:</span>
                  <span className="text-primary-text">
                    {selectedCrop?.growth_period ? formatDate(new Date(selectedCrop.growth_period.start_date)) : ''}
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-secondary-text min-w-[120px]">Harvest Date:</span>
                  <span className="text-primary-text">
                    {selectedCrop?.growth_period ? formatDate(new Date(selectedCrop.growth_period.harvest_date)) : ''}
                  </span>
                </div>
              </div>

              {/* Quality Parameters */}
              <div className="bg-card-bg rounded-lg p-4 border border-card-border hover:border-accent/20 transition-colors duration-200">
                <h2 className="text-lg font-semibold text-primary-text mb-4 flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-accent" />
                  Quality Parameters
                </h2>
                <div className="flex items-center gap-4">
                  <span className="text-secondary-text min-w-[120px]">Grade:</span>
                  <span className="px-3 py-1 text-sm font-medium rounded-full bg-accent-light text-accent-dark">
                    Grade {selectedCrop?.crop_grade}
                  </span>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Fertilizer Details */}
              <div className="bg-card-bg rounded-lg p-4 border border-card-border hover:border-accent/20 transition-colors duration-200">
                <h2 className="text-lg font-semibold text-primary-text mb-4 flex items-center">
                  <Warehouse className="h-5 w-5 mr-2 text-accent" />
                  Fertilizer Details
                </h2>
                <div className="flex items-center gap-4 mb-2">
                  <span className="text-secondary-text min-w-[120px]">Soil Type:</span>
                  <span className="text-primary-text">{selectedCrop?.soil_type}</span>
                </div>
                <div>
                  <span className="text-secondary-text block mb-2 min-w-[120px]">Fertilizer:</span>
                  <ul className="list-disc list-inside text-primary-text space-y-1 ml-6">
                    {selectedCrop?.fertilizers_used?.map((item, index) => (
                      <li key={index} className="hover:text-accent transition-colors duration-200">
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Certifications */}
              {selectedCrop?.certifications &&
                ((Array.isArray(selectedCrop.certifications) && selectedCrop.certifications.length > 0) ||
                  (!Array.isArray(selectedCrop.certifications) && selectedCrop.certifications)) && (
                  <div className="bg-card-bg rounded-lg p-4 border border-card-border hover:border-accent/20 transition-colors duration-200">
                    <h2 className="text-lg font-semibold text-primary-text mb-4 flex items-center">
                      <BadgeCheck className="h-5 w-5 mr-2 text-accent" />
                      Certifications
                    </h2>
                    <div className="flex flex-wrap gap-2">
                      {Array.isArray(selectedCrop.certifications) ? (
                        selectedCrop.certifications.map((cert, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 text-sm font-medium rounded-full bg-accent-light/20 text-accent-dark hover:bg-accent-light/30 transition-colors duration-200"
                          >
                            {cert}
                          </span>
                        ))
                      ) : (
                        <span className="px-3 py-1 text-sm font-medium rounded-full bg-accent-light/20 text-accent-dark hover:bg-accent-light/30 transition-colors duration-200">
                          {selectedCrop.certifications}
                        </span>
                      )}
                    </div>
                  </div>
                )}
            </div>
          </div>
        </div>

        <div className="mt-8">
          <ProductHistory transfers={history} />
        </div>
      </div>
    </div>
  )
}
