## Directory Structure

Your directory structure should look something like this:

```
backend/
├── manage.py
├── user/
│   ├── models.py
│   ├── management/
│   │   ├── commands/
│   │   │   ├── __init__.py
│   │   │   ├── following_data.py
├── post/
│   ├── models.py
├── note/
│   ├── models.py
│   ├── management/
│   │   ├── commands/
│   │   │   ├── __init__.py
│   │   │   ├── populate_notes.py
```

## Prerequisites

- Python 3.x
- Django 3.x or later
- Necessary Django apps: `user`, `post`, `note`, `agency`, `industry`, `updates`

pip install --only-binary :all: psycopg2-binary

## Installation

1. Clone the repository:

   ```sh
   git clone https://github.com/LuminVibe/project-rg.git
   cd project-rg\backend
   ```

2. Create and activate a virtual environment:

   ```sh
   python -m venv .venv
   source venv/bin/activate
   ```

   On Windows use

   ```sh
   .venv\Scripts\activate
   ```

3. Install the dependencies:

   ```sh
   pip install -r requirements.txt
   ```

4. Apply migrations:

   ```sh
   python manage.py makemigrations
   python manage.py migrate
   ```

## Data migration

please the data this is order.

- `auth_permissions`
- `django_content_type`
- `auth_group_permissions`
- `auth_group`

## Usage

To start the development server:

```sh
python manage.py runserver
```

### Cleaning the database

This process is necessary if the same data already exists in the database but is incomplete. If so, we need to clean the database.

```sh
python manage.py flush
```

## Populating the Database

### Populate Users

Please create some users using the API or through the frontend before starting.

### Populate Industry

To populate the database with Industry use the following command:

```sh
python manage.py industries_data
```

### Populate Agency

To populate the database with Agency, use the following command:

```sh
python manage.py agency_data
```

### Populate Posts

To populate the database with posts related to compliance and reporting in food and beverages, use the following command:

```sh
python manage.py post_data
```

### Populate Updates

To populate the database with updates, including references to industries and sub-industries, use the following command:

```sh
python manage.py updates_data
```

### Populate Followings

To populate the database with followings between users and agencies, use the following command:

```sh
python manage.py following_data
```

### Populate Notes

To populate the database with notes related to users and posts, use the following command:

```sh
python manage.py notes_data
```

### Creating Views

#### Updates details

To create the view in the SQL database, please follow these steps:

1. Connect to the SQL database.

The query to create the view is as follows:

```sql
CREATE VIEW updates_with_industry_subindustry AS
SELECT
    uu.id,
    uu.title,
    uu.description,
    uu.date,
    ag.name AS organisation,
    uu.article_link,
    uu.post_id,
    ag.key,
    ARRAY_AGG(DISTINCT pi.industry_id) AS industry_ids,
    ARRAY_AGG(DISTINCT psi.subindustry_id) AS subindustry_ids
FROM public.updates_update AS uu
LEFT JOIN public.post_post AS po ON po.id = uu.post_id
LEFT JOIN public.agency_agency AS ag ON ag.key = po.agency_id
LEFT JOIN public.post_post_industry AS pi ON pi.post_id = po.id
LEFT JOIN public.post_post_subindustry AS psi ON psi.post_id = po.id
GROUP BY uu.id, uu.title, uu.description, uu.date, ag.name, uu.article_link, uu.post_id, ag.key;
```

#### Post Details View

This project creates a view named `post_details` which encapsulates a complex SQL query to join various tables and filter the results based on the presence of industry or subindustry IDs.

```sql
CREATE VIEW post_details AS
SELECT
    post.id,
    post.content,
    post.picture,
    post.created_at,
    post.is_law,
    post.agency_id,
    post.previous_version,
    agency.name AS agency_name,
    agency.location AS agency_location,
    post.article_link,
    ARRAY_AGG(DISTINCT pi.industry_id) AS industry_ids,
    ARRAY_AGG(DISTINCT psi.subindustry_id) AS subindustry_ids,
    MAX(updates.id) AS update_id
FROM public.post_post AS post
LEFT JOIN public.agency_agency AS agency ON agency.key = post.agency_id
LEFT JOIN public.post_post_industry AS pi ON post.id = pi.post_id
LEFT JOIN public.post_post_subindustry AS psi ON post.id = psi.post_id
LEFT JOIN public.updates_update AS updates ON updates.post_id = post.id
GROUP BY post.id, post.content, post.picture, post.created_at, post.is_law, post.agency_id, post.previous_version, agency.name, agency.location, post.article_link;
```

### Function

#### get_post_history_created

This function retrieves the history of a post, including previous versions and their creation dates, in a JSONB format. It uses a recursive common table expression (CTE) to navigate through the history of post versions.

```sql
CREATE OR REPLACE FUNCTION get_post_history_with_dates(p_id INT)
RETURNS TABLE (post_id INT, history_versions JSONB) AS $$
WITH RECURSIVE history AS (
    -- Anchor member: start with the initial post_id
    SELECT
        pph.post_id,
        pph.previous_version_id
    FROM
        public.post_posthistory pph
    WHERE
        pph.post_id = p_id

    UNION ALL

    -- Recursive member: find the previous versions recursively
    SELECT
        pph.post_id,
        pph.previous_version_id
    FROM
        public.post_posthistory pph
    INNER JOIN
        history h ON h.previous_version_id = pph.post_id
),
history_with_dates AS (
    SELECT
        h.previous_version_id,
        pp.created_at
    FROM
        history h
    LEFT JOIN
        public.post_post pp ON h.previous_version_id = pp.id
)
-- Select the original post_id and JSONB array of previous versions with created_at, ordered by created_at DESC
SELECT
    p_id AS post_id,
    jsonb_agg(jsonb_build_object('version_id', previous_version_id, 'created_at', created_at) ORDER BY created_at DESC) AS history_versions
FROM
    history_with_dates;
$$ LANGUAGE sql;
```

#### Usage

To use the `get_post_history_created` function, you can call it in your SQL queries as follows:

```sql
SELECT * FROM public.get_post_history_created(12);
```

### Function: get_post_history

The `get_post_history` function is a PostgreSQL user-defined function designed to retrieve the history of a given post by its ID. The function returns a table containing the post ID and an array of its previous version IDs, enabling users to trace the version history of posts stored in the `post_posthistory` table.

```sql
CREATE OR REPLACE FUNCTION get_post_history_versions(p_id integer)
RETURNS TABLE(post_id integer, history_versions integer[])
LANGUAGE 'sql'
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$
WITH RECURSIVE history AS (
    -- Anchor member: start with the initial post_id
    SELECT
        pph.post_id,
        pph.previous_version_id
    FROM
        public.post_posthistory pph
    WHERE
        pph.post_id = p_id

    UNION ALL

    -- Recursive member: find the previous versions recursively
    SELECT
        pph.post_id,
        pph.previous_version_id
    FROM
        public.post_posthistory pph
    INNER JOIN
        history h ON h.previous_version_id = pph.post_id
)
-- Select the original post_id and array of previous versions
SELECT
    p_id AS post_id,
    array_agg(previous_version_id) AS history_versions
FROM
    history;
$BODY$;

ALTER FUNCTION get_post_history_versions(integer)
    OWNER TO postgres;
```

#### Usage

To use the `get_post_history` function, you can call it in your SQL queries as follows:

```sql
SELECT * FROM public.get_post_history(12);
```

### TRIGGER

#### Creating the Trigger Function

```sql
CREATE OR REPLACE FUNCTION update_previous_version()
            RETURNS TRIGGER AS $$
            BEGIN
                UPDATE post_post
                SET previous_version = TRUE
                WHERE id = NEW.previous_version_id;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
```

```sql
CREATE OR REPLACE FUNCTION add_to_savedposts()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if a record already exists in SavedPosts for this user
    IF NOT EXISTS (
        SELECT 1
        FROM public.user_savedposts
        WHERE user_id = NEW.user_id
    ) THEN
        -- If not, create a new SavedPosts record for this user
        INSERT INTO public.user_savedposts (user_id)
        VALUES (NEW.user_id);
    END IF;

    -- Ensure the post is not already saved by the user
    IF NOT EXISTS (
        SELECT 1
        FROM public.user_savedposts_posts
        INNER JOIN public.user_savedposts ON public.user_savedposts_posts.savedposts_id = public.user_savedposts.id
        WHERE public.user_savedposts.user_id = NEW.user_id
        AND public.user_savedposts_posts.post_id = NEW.post_id
    ) THEN
        -- Add the post to the SavedPosts entry for the user
        INSERT INTO public.user_savedposts_posts (savedposts_id, post_id)
        SELECT id, NEW.post_id
        FROM public.user_savedposts
        WHERE user_id = NEW.user_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

#### Creating the Trigger

```sql
CREATE TRIGGER update_privece_version_trigger
            AFTER INSERT ON public.post_posthistory
            FOR EACH ROW
            EXECUTE FUNCTION update_previous_version();
```

```sql
CREATE TRIGGER note_after_insert
AFTER INSERT ON public.note_note
FOR EACH ROW
EXECUTE FUNCTION add_to_savedposts();
```

### All Commands

```sh
git clone https://github.com/LuminVibe/project-rg.git
cd project-rg\backend

python -m venv .venv

.venv\Scripts\activate

pip install -r requirements.txt

python manage.py makemigrations
python manage.py migrate

python manage.py runserver

python manage.py createsuperuser

python manage.py flush

python manage.py industries_data

python manage.py user_data

python manage.py agency_data

python manage.py post_data

python manage.py post_data_history

python manage.py updates_data

python manage.py following_data

python manage.py notes_data

python manage.py save_posts
```
