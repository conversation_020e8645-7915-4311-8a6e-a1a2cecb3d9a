# Generated by Django 5.2 on 2025-04-22 08:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="News",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("date", models.DateField()),
                ("article_link", models.URLField(max_length=255)),
            ],
            options={
                "verbose_name": "Update",
                "verbose_name_plural": "Updates",
                "ordering": ["-date"],
            },
        ),
    ]
