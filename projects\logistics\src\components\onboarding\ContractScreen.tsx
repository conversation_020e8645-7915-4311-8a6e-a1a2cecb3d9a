import apiClient from '@/services/apiClient'
import { useWallet } from '@txnlab/use-wallet-react'
import { File<PERSON>heck, Loader2 } from 'lucide-react'
import { ellipseAddress } from '../../utils/ellipseAddress'

interface ContractScreenProps {
  handleNext: () => void
  handleBack: () => void
  isLoading: boolean
  setIsLoading: (value: boolean) => void
  selectedAccount: string
}
/**
 * Displays the smart contract approval step in the onboarding process, allowing users to authorize FarmChain to manage farm operations on the blockchain.
 *
 * Renders information about the contract integration, shows the selected wallet account, and provides controls to approve the contract or navigate back.
 *
 * @param selectedAccount - The address of the wallet account selected for contract approval.
 */
export default function ContractScreen({ handleNext, handleBack, isLoading, setIsLoading, selectedAccount }: ContractScreenProps) {
  const { wallets } = useWallet()

  const handleApproveContract = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.post('/user/opt-in-account/', {})
      if (response.status !== 200) {
        throw new Error('Approve smart contract failed')
      }
      handleNext()
    } catch (error) {
      console.error('Contract approval failed:', error)
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">Smart Contract Integration</h2>
        <p className="text-[#BDBDBD]">This step allows FarmChain to help manage your farm operations securely on the blockchain.</p>
      </div>

      <div className="bg-[#303030] p-6 rounded-lg space-y-6">
        <div className="space-y-4">
          <div className="flex items-start gap-4">
            <FileCheck className="h-6 w-6 text-[#00E676] mt-1" />
            <div>
              <h3 className="text-white font-semibold mb-2">What happens in this step?</h3>
              <p className="text-[#BDBDBD]">By approving the smart contract, you're enabling FarmChain to:</p>
              <ul className="list-disc list-inside text-[#BDBDBD] mt-2 space-y-1">
                <li>Track your crop data securely</li>
                <li>Process supply chain transactions</li>
                <li>Manage financial services access</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-[#424242] pt-4">
            <h3 className="text-white font-semibold mb-3">Transaction Details:</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-[#BDBDBD]">Selected Account:</span>
                <span className="text-white font-mono">
                  {ellipseAddress(
                    wallets
                      ?.find((wallet) => wallet.accounts?.some((account) => account.address === selectedAccount))
                      ?.accounts?.find((account) => account.address === selectedAccount)?.address,
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={handleBack}
          className="px-6 py-2 rounded-lg border border-[#424242] text-[#BDBDBD] hover:text-white hover:border-white transition-colors duration-300"
        >
          Back
        </button>
        <button
          onClick={handleApproveContract}
          disabled={isLoading}
          className="bg-[#1B5E20] text-white px-6 py-2 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
              <Loader2 className="animate-spin h-5 w-5 mr-2" />
              Approving...
            </>
          ) : (
            <>
              <FileCheck className="h-5 w-5 mr-2" />
              Approve Contract
            </>
          )}
        </button>
      </div>
    </div>
  )
}
