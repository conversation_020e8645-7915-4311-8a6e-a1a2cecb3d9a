import apiClient from '@/services/apiClient'
import { ellipseAddress } from '@/utils/ellipseAddress'
import { BrowserQRCodeReader } from '@zxing/browser'
import clsx from 'clsx'
import { Decimal } from 'decimal.js'
import { ArrowRight, Copy, Loader2, QrCode, RotateCcw, Send, Wallet } from 'lucide-react'
import React, { useRef, useState } from 'react'
import toast, { Toaster } from 'react-hot-toast'
import { useLocation, useNavigate } from 'react-router-dom'

interface TransferFormData {
  amount: string
  address: string
}

const CryptoTransfer: React.FC = () => {
  const [formData, setFormData] = useState<TransferFormData>({ amount: '', address: '' })
  const [isScanning, setIsScanning] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [errors, setErrors] = useState<Partial<TransferFormData>>({})
  const videoRef = useRef<HTMLVideoElement>(null)
  const location = useLocation()
  const navigate = useNavigate()

  // const [recentTransfers] = useState<RecentTransfer[]>([
  //   {
  //     address: '0x1234...5678',
  //     amount: '0.5',
  //     timestamp: new Date(),
  //     status: 'completed',
  //     hash: '0xabcd...efgh',
  //   },
  // ])

  // Simulated balance and fees
  const balance = parseFloat(location.state?.kttBalance) || 0
  const gasPrice = new Decimal(0.001)

  const validateForm = (): boolean => {
    const newErrors: Partial<TransferFormData> = {}

    if (!formData.amount) {
      newErrors.amount = 'Amount is required'
    } else {
      const amount = new Decimal(formData.amount)
      if (amount.lte(0)) {
        newErrors.amount = 'Amount must be greater than 0'
      } else if (amount.gt(balance.toString())) {
        newErrors.amount = 'Insufficient balance'
      }
    }

    // if (!formData.address) {
    //   newErrors.address = 'Address is required'
    // } else if (!/^0x[a-fA-F0-9]{40}$/.test(formData.address)) {
    //   newErrors.address = 'Invalid address format'
    // }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    const amount = new Decimal(formData.amount)

    if (amount.gt(0.5)) {
      setShowConfirmation(true)
      return
    }

    await processTransfer()
  }

  const processTransfer = async () => {
    setIsProcessing(true)
    setShowConfirmation(false)

    try {
      const responce = await apiClient.post('/transaction/transfer-ktt/', {
        amount: formData.amount,
        reciver: formData.address,
      })
      if (responce.status === 200) {
        toast.success('Transfer completed successfully!')
        setFormData({ amount: '', address: '' })
        navigate(-1)
      }

      toast.error('Transfer failed. Please try again.')
    } catch (error) {
      toast.error('Transfer failed. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleMaxAmount = () => {
    const maxAmount = new Decimal(balance).minus(gasPrice).toString()
    setFormData((prev) => ({
      ...prev,
      amount: maxAmount,
    }))
  }

  const startQRScanner = async () => {
    setIsScanning(true)
    const codeReader = new BrowserQRCodeReader()

    try {
      const videoInputDevices = await BrowserQRCodeReader.listVideoInputDevices()
      if (videoInputDevices.length > 0) {
        const result = await codeReader.decodeOnceFromVideoDevice(undefined, 'qr-video')
        if (result) {
          setFormData((prev) => ({ ...prev, address: result.getText() }))
        }
      }
    } catch (error) {
      toast.error('Failed to access camera')
    } finally {
      setIsScanning(false)
    }
  }

  // const handlePasteAddress = async (e: React.ClipboardEvent) => {
  //   e.preventDefault()
  //   const text = e.clipboardData.getData('text')
  //   const sanitized = text.trim().replace(/[^a-fA-F0-9x]/g, '')
  //   if (sanitized.startsWith('0x') && sanitized.length === 42) {
  //     setFormData((prev) => ({ ...prev, address: sanitized }))
  //   } else {
  //     toast.error('Invalid address format')
  //   }
  // }

  const resetForm = () => {
    setFormData({ amount: '', address: '' })
    setErrors({})
    setShowConfirmation(false)
  }

  return (
    <div className="min-h-screen bg-[#212121] text-white p-6">
      <Toaster position="top-right" />

      <div className="max-w-2xl mx-auto">
        <div className="bg-[#303030] rounded-xl p-6 shadow-lg">
          <h1 className="text-2xl font-bold mb-6 flex items-center gap-2">
            <Send className="text-[#00E676]" />
            Send Cryptocurrency
          </h1>

          <div className="mb-6 p-4 bg-[#1B5E20] rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Wallet className="text-[#00E676]" />
                <span>Available Balance</span>
              </div>
              <span className="text-xl font-bold">{balance.toString()} KTT</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-[#BDBDBD] text-sm mb-2">Amount</label>
              <div className="relative">
                <input
                  type="number"
                  step="0.000001"
                  value={formData.amount}
                  onChange={(e) => setFormData((prev) => ({ ...prev, amount: e.target.value }))}
                  className={clsx(
                    'w-full bg-[#212121] p-3 rounded-lg pr-20',
                    'focus:outline-none focus:ring-2 focus:ring-[#00E676]',
                    errors.amount && 'ring-2 ring-red-500',
                  )}
                  placeholder="0.00"
                />
                <button
                  type="button"
                  onClick={handleMaxAmount}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-[#1B5E20] text-[#00E676] px-3 py-1 rounded-md flex items-center gap-1"
                >
                  Max
                </button>
              </div>
              {errors.amount && <p className="text-red-500 text-sm mt-1">{errors.amount}</p>}
            </div>

            <div>
              <label className="block text-[#BDBDBD] text-sm mb-2">Recipient Address</label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.address}
                  onChange={(e) => setFormData((prev) => ({ ...prev, address: e.target.value }))}
                  // onPaste={handlePasteAddress}
                  className={clsx(
                    'w-full bg-[#212121] p-3 rounded-lg pr-24',
                    'focus:outline-none focus:ring-2 focus:ring-[#00E676]',
                    errors.address && 'ring-2 ring-red-500',
                  )}
                  placeholder="0x..."
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                  <button
                    type="button"
                    onClick={() => startQRScanner()}
                    className="text-[#00E676] hover:text-[#00E676]/80"
                    title="Scan QR Code"
                  >
                    <QrCode size={20} />
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      navigator.clipboard.writeText(formData.address)
                      toast.success('Address copied!')
                    }}
                    className="text-[#00E676] hover:text-[#00E676]/80"
                    title="Copy Address"
                  >
                    <Copy size={20} />
                  </button>
                </div>
              </div>
              {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
            </div>

            {isScanning && (
              <div className="relative">
                <video ref={videoRef} id="qr-video" className="w-full h-64 object-cover rounded-lg" />
                <button
                  type="button"
                  onClick={() => setIsScanning(false)}
                  className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full"
                >
                  ✕
                </button>
              </div>
            )}

            <div className="bg-[#212121] p-4 rounded-lg space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-[#BDBDBD]">Network Fee</span>
                <span>{gasPrice.toString()} KTT</span>
              </div>
              {/* <div className="flex justify-between text-sm">
                <span className="text-[#BDBDBD]">Estimated Time</span>
                <span className="flex items-center gap-1">
                  <Clock size={14} />
                  ~2 minutes
                </span>
              </div> */}
            </div>

            <div className="flex gap-4">
              <button
                type="button"
                onClick={resetForm}
                className="flex-1 bg-[#212121] text-white py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-[#212121]/80"
              >
                <RotateCcw size={18} />
                Reset
              </button>
              <button
                type="submit"
                disabled={isProcessing}
                className="flex-1 bg-[#00E676] text-[#212121] py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-[#00E676]/90 disabled:opacity-50"
              >
                {isProcessing ? <Loader2 className="animate-spin" size={18} /> : <ArrowRight size={18} />}
                {isProcessing ? 'Processing...' : 'Send'}
              </button>
            </div>
          </form>

          {/* Recent Transfers */}
          {/* <div className="mt-8">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <History className="text-[#00E676]" />
              Recent Transfers
            </h2>
            <div className="space-y-3">
              {recentTransfers.map((transfer, index) => (
                <div key={index} className="bg-[#212121] p-4 rounded-lg flex items-center justify-between">
                  <div>
                    <p className="font-mono text-sm">{transfer.address}</p>
                    <p className="text-[#BDBDBD] text-sm">
                      {transfer.amount} ETH • {transfer.timestamp.toLocaleDateString()}
                    </p>
                  </div>
                  <a
                    href={`https://etherscan.io/tx/${transfer.hash}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#00E676] hover:text-[#00E676]/80"
                  >
                    <ExternalLink size={18} />
                  </a>
                </div>
              ))}
            </div>
          </div> */}
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-[#303030] rounded-xl p-6 max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">Confirm Large Transfer</h2>
            <p className="text-[#BDBDBD] mb-4">You are about to transfer {formData.amount} KTT to:</p>
            <p className="font-mono bg-[#212121] p-2 rounded mb-6">{ellipseAddress(formData.address)}</p>
            <div className="flex gap-4">
              <button onClick={() => setShowConfirmation(false)} className="flex-1 bg-[#212121] text-white py-3 rounded-lg">
                Cancel
              </button>
              <button onClick={() => processTransfer()} className="flex-1 bg-[#00E676] text-[#212121] py-3 rounded-lg">
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CryptoTransfer
