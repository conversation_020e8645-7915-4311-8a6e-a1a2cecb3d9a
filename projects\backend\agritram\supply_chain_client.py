# flake8: noqa
# fmt: off
# mypy: ignore-errors
# This file was automatically generated by algokit-client-generator.
# DO NOT MODIFY IT BY HAND.
# requires: algokit-utils@^3.0.0

# common
import dataclasses
import typing
# core algosdk
import algosdk
from algosdk.transaction import OnComplete
from algosdk.atomic_transaction_composer import TransactionSigner
from algosdk.source_map import SourceMap
from algosdk.transaction import Transaction
from algosdk.v2client.models import SimulateTraceConfig
# utils
import algokit_utils
from algokit_utils import AlgorandClient as _AlgoKitAlgorandClient

_APP_SPEC_JSON = r"""{"arcs": [22, 28], "bareActions": {"call": [], "create": ["NoOp"]}, "methods": [{"actions": {"call": ["NoOp"], "create": []}, "args": [{"type": "uint64", "name": "product_id"}, {"type": "string", "name": "crop_greade"}, {"type": "uint64", "name": "quantity"}, {"type": "string", "name": "address"}, {"type": "string", "name": "harvest_location_latitude"}, {"type": "string", "name": "harvest_location_longitude"}, {"type": "string", "name": "cultivation_date"}, {"type": "string", "name": "harvested_date"}, {"type": "string", "name": "soil_type"}, {"type": "string", "name": "irrigation_type"}, {"type": "string", "name": "fertilizers_used"}, {"type": "string", "name": "certification"}], "name": "register_crop", "returns": {"type": "uint64"}, "events": [], "readonly": false, "recommendations": {}}], "name": "SupplyChain", "state": {"keys": {"box": {}, "global": {"product_count": {"key": "cHJvZHVjdF9jb3VudA==", "keyType": "AVMString", "valueType": "AVMUint64"}}, "local": {}}, "maps": {"box": {}, "global": {}, "local": {}}, "schema": {"global": {"bytes": 0, "ints": 1}, "local": {"bytes": 0, "ints": 0}}}, "structs": {}, "byteCode": {"approval": "CiACAAEmAQ1wcm9kdWN0X2NvdW50MRhAAAMoImcxG0EASYAElKOq+TYaAI4BAAIiQzEZFEQxGEQ2GgE2GgI2GgM2GgQ2GgU2GgY2Ggc2Ggg2Ggk2Ggo2Ggs2GgyIABcWgAQVH3x1TFCwI0MxGUD/vzEYFEQjQ4oMASIoZUQWsYv2FyIoZUQWMgpHA7IssiuyKrIpgCJodHRwczovL2V4YW1wbGUuY29tL2lwZnMvY3JvcC1tZXRhsieyJoAEQ1JPULIlIrIjsiKBA7IQIrIBs7Q8SRaL9IACACxQi/UVgSwITIv2UEsBFlcGAlCL9xVPAghJFlcGAk8CTFCL+BVPAghJFlcGAk8CTFCL+RVPAghJFlcGAk8CTFCL+hVPAghJFlcGAk8CTFCL+xVPAghJFlcGAk8CTFCL/BVPAghJFlcGAk8CTFCL/RVPAghJFlcGAk8CTFCL/hVPAggWVwYCUExQi/VQi/dQi/hQi/lQi/pQi/tQi/xQi/1Qi/5Qi/9QTwJMvyIoZUQjCChMZ4k=", "clear": "CoEBQw=="}, "compilerInfo": {"compiler": "puya", "compilerVersion": {"major": 4, "minor": 7, "patch": 0}}, "events": [], "networks": {}, "source": {"approval": "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", "clear": "I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"}, "sourceInfo": {"approval": {"pcOffsetMethod": "none", "sourceInfo": [{"pc": [52], "errorMessage": "OnCompletion is not NoOp"}, {"pc": [115], "errorMessage": "can only call when creating"}, {"pc": [55], "errorMessage": "can only call when not creating"}, {"pc": [124, 133, 391], "errorMessage": "check self.product_count exists"}]}, "clear": {"pcOffsetMethod": "none", "sourceInfo": []}}, "templateVariables": {}}"""
APP_SPEC = algokit_utils.Arc56Contract.from_json(_APP_SPEC_JSON)

def _parse_abi_args(args: object | None = None) -> list[object] | None:
    """Helper to parse ABI args into the format expected by underlying client"""
    if args is None:
        return None

    def convert_dataclass(value: object) -> object:
        if dataclasses.is_dataclass(value):
            return tuple(convert_dataclass(getattr(value, field.name)) for field in dataclasses.fields(value))
        elif isinstance(value, (list, tuple)):
            return type(value)(convert_dataclass(item) for item in value)
        return value

    match args:
        case tuple():
            method_args = list(args)
        case _ if dataclasses.is_dataclass(args):
            method_args = [getattr(args, field.name) for field in dataclasses.fields(args)]
        case _:
            raise ValueError("Invalid 'args' type. Expected 'tuple' or 'TypedDict' for respective typed arguments.")

    return [
        convert_dataclass(arg) if not isinstance(arg, algokit_utils.AppMethodCallTransactionArgument) else arg
        for arg in method_args
    ] if method_args else None

def _init_dataclass(cls: type, data: dict) -> object:
    """
    Recursively instantiate a dataclass of type `cls` from `data`.

    For each field on the dataclass, if the field type is also a dataclass
    and the corresponding data is a dict, instantiate that field recursively.
    """
    field_values = {}
    for field in dataclasses.fields(cls):
        field_value = data.get(field.name)
        # Check if the field expects another dataclass and the value is a dict.
        if dataclasses.is_dataclass(field.type) and isinstance(field_value, dict):
            field_values[field.name] = _init_dataclass(typing.cast(type, field.type), field_value)
        else:
            field_values[field.name] = field_value
    return cls(**field_values)

@dataclasses.dataclass(frozen=True, kw_only=True)
class RegisterCropArgs:
    """Dataclass for register_crop arguments"""
    product_id: int
    crop_greade: str
    quantity: int
    address: str
    harvest_location_latitude: str
    harvest_location_longitude: str
    cultivation_date: str
    harvested_date: str
    soil_type: str
    irrigation_type: str
    fertilizers_used: str
    certification: str

    @property
    def abi_method_signature(self) -> str:
        return "register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64"


class SupplyChainParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def register_crop(
        self,
        args: tuple[int, str, int, str, str, str, str, str, str, str, str, str] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.AppCallMethodCallParams:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.params.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64",
            "args": method_args,
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> algokit_utils.AppCallParams:
        return self.app_client.params.bare.clear_state(
            params,
            
        )


class SupplyChainCreateTransactionParams:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def register_crop(
        self,
        args: tuple[int, str, int, str, str, str, str, str, str, str, str, str] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> algokit_utils.BuiltTransactions:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        return self.app_client.create_transaction.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64",
            "args": method_args,
        }))

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        
    ) -> Transaction:
        return self.app_client.create_transaction.bare.clear_state(
            params,
            
        )


class SupplyChainSend:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    def register_crop(
        self,
        args: tuple[int, str, int, str, str, str, str, str, str, str, str, str] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[int]:
        method_args = _parse_abi_args(args)
        params = params or algokit_utils.CommonAppCallParams()
        response = self.app_client.send.call(algokit_utils.AppClientMethodCallParams(**{
            **dataclasses.asdict(params),
            "method": "register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64",
            "args": method_args,
        }), send_params=send_params)
        parsed_response = response
        return typing.cast(algokit_utils.SendAppTransactionResult[int], parsed_response)

    def clear_state(
        self,
        params: algokit_utils.AppClientBareCallParams | None = None,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAppTransactionResult[algokit_utils.ABIReturn]:
        return self.app_client.send.bare.clear_state(
            params,
            send_params=send_params,
        )


class GlobalStateValue(typing.TypedDict):
    """Shape of global_state state key values"""
    product_count: int

class SupplyChainState:
    """Methods to access state for the current SupplyChain app"""

    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client

    @property
    def global_state(
        self
    ) -> "_GlobalState":
            """Methods to access global_state for the current app"""
            return _GlobalState(self.app_client)

class _GlobalState:
    def __init__(self, app_client: algokit_utils.AppClient):
        self.app_client = app_client
        
        # Pre-generated mapping of value types to their struct classes
        self._struct_classes: dict[str, typing.Type[typing.Any]] = {}

    def get_all(self) -> GlobalStateValue:
        """Get all current keyed values from global_state state"""
        result = self.app_client.state.global_state.get_all()
        if not result:
            return typing.cast(GlobalStateValue, {})

        converted = {}
        for key, value in result.items():
            key_info = self.app_client.app_spec.state.keys.global_state.get(key)
            struct_class = self._struct_classes.get(key_info.value_type) if key_info else None
            converted[key] = (
                _init_dataclass(struct_class, value) if struct_class and isinstance(value, dict)
                else value
            )
        return typing.cast(GlobalStateValue, converted)

    @property
    def product_count(self) -> int:
        """Get the current value of the product_count key in global_state state"""
        value = self.app_client.state.global_state.get_value("product_count")
        if isinstance(value, dict) and "AVMUint64" in self._struct_classes:
            return _init_dataclass(self._struct_classes["AVMUint64"], value)  # type: ignore
        return typing.cast(int, value)

class SupplyChainClient:
    """Client for interacting with SupplyChain smart contract"""

    @typing.overload
    def __init__(self, app_client: algokit_utils.AppClient) -> None: ...
    
    @typing.overload
    def __init__(
        self,
        *,
        algorand: _AlgoKitAlgorandClient,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None: ...

    def __init__(
        self,
        app_client: algokit_utils.AppClient | None = None,
        *,
        algorand: _AlgoKitAlgorandClient | None = None,
        app_id: int | None = None,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> None:
        if app_client:
            self.app_client = app_client
        elif algorand and app_id:
            self.app_client = algokit_utils.AppClient(
                algokit_utils.AppClientParams(
                    algorand=algorand,
                    app_spec=APP_SPEC,
                    app_id=app_id,
                    app_name=app_name,
                    default_sender=default_sender,
                    default_signer=default_signer,
                    approval_source_map=approval_source_map,
                    clear_source_map=clear_source_map,
                )
            )
        else:
            raise ValueError("Either app_client or algorand and app_id must be provided")
    
        self.params = SupplyChainParams(self.app_client)
        self.create_transaction = SupplyChainCreateTransactionParams(self.app_client)
        self.send = SupplyChainSend(self.app_client)
        self.state = SupplyChainState(self.app_client)

    @staticmethod
    def from_creator_and_name(
        creator_address: str,
        app_name: str,
        algorand: _AlgoKitAlgorandClient,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
    ) -> "SupplyChainClient":
        return SupplyChainClient(
            algokit_utils.AppClient.from_creator_and_name(
                creator_address=creator_address,
                app_name=app_name,
                app_spec=APP_SPEC,
                algorand=algorand,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
                ignore_cache=ignore_cache,
                app_lookup_cache=app_lookup_cache,
            )
        )
    
    @staticmethod
    def from_network(
        algorand: _AlgoKitAlgorandClient,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "SupplyChainClient":
        return SupplyChainClient(
            algokit_utils.AppClient.from_network(
                app_spec=APP_SPEC,
                algorand=algorand,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    @property
    def app_id(self) -> int:
        return self.app_client.app_id
    
    @property
    def app_address(self) -> str:
        return self.app_client.app_address
    
    @property
    def app_name(self) -> str:
        return self.app_client.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_client.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_client.algorand

    def clone(
        self,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> "SupplyChainClient":
        return SupplyChainClient(
            self.app_client.clone(
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                approval_source_map=approval_source_map,
                clear_source_map=clear_source_map,
            )
        )

    def new_group(self) -> "SupplyChainComposer":
        return SupplyChainComposer(self)

    @typing.overload
    def decode_return_value(
        self,
        method: typing.Literal["register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64"],
        return_value: algokit_utils.ABIReturn | None
    ) -> int | None: ...
    @typing.overload
    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None: ...

    def decode_return_value(
        self,
        method: str,
        return_value: algokit_utils.ABIReturn | None
    ) -> algokit_utils.ABIValue | algokit_utils.ABIStruct | None | int:
        """Decode ABI return value for the given method."""
        if return_value is None:
            return None
    
        arc56_method = self.app_spec.get_arc56_method(method)
        decoded = return_value.get_arc56_value(arc56_method, self.app_spec.structs)
    
        # If method returns a struct, convert the dict to appropriate dataclass
        if (arc56_method and
            arc56_method.returns and
            arc56_method.returns.struct and
            isinstance(decoded, dict)):
            struct_class = globals().get(arc56_method.returns.struct)
            if struct_class:
                return struct_class(**typing.cast(dict, decoded))
        return decoded


@dataclasses.dataclass(frozen=True)
class SupplyChainBareCallCreateParams(algokit_utils.AppClientBareCallCreateParams):
    """Parameters for creating SupplyChain contract with bare calls"""
    on_complete: typing.Literal[OnComplete.NoOpOC] | None = None

    def to_algokit_utils_params(self) -> algokit_utils.AppClientBareCallCreateParams:
        return algokit_utils.AppClientBareCallCreateParams(**self.__dict__)

class SupplyChainFactory(algokit_utils.TypedAppFactoryProtocol[SupplyChainBareCallCreateParams, None, None]):
    """Factory for deploying and managing SupplyChainClient smart contracts"""

    def __init__(
        self,
        algorand: _AlgoKitAlgorandClient,
        *,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        version: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ):
        self.app_factory = algokit_utils.AppFactory(
            params=algokit_utils.AppFactoryParams(
                algorand=algorand,
                app_spec=APP_SPEC,
                app_name=app_name,
                default_sender=default_sender,
                default_signer=default_signer,
                version=version,
                compilation_params=compilation_params,
            )
        )
        self.params = SupplyChainFactoryParams(self.app_factory)
        self.create_transaction = SupplyChainFactoryCreateTransaction(self.app_factory)
        self.send = SupplyChainFactorySend(self.app_factory)

    @property
    def app_name(self) -> str:
        return self.app_factory.app_name
    
    @property
    def app_spec(self) -> algokit_utils.Arc56Contract:
        return self.app_factory.app_spec
    
    @property
    def algorand(self) -> _AlgoKitAlgorandClient:
        return self.app_factory.algorand

    def deploy(
        self,
        *,
        on_update: algokit_utils.OnUpdate | None = None,
        on_schema_break: algokit_utils.OnSchemaBreak | None = None,
        create_params: SupplyChainBareCallCreateParams | None = None,
        update_params: None = None,
        delete_params: None = None,
        existing_deployments: algokit_utils.ApplicationLookup | None = None,
        ignore_cache: bool = False,
        app_name: str | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
    ) -> tuple[SupplyChainClient, algokit_utils.AppFactoryDeployResult]:
        """Deploy the application"""
        deploy_response = self.app_factory.deploy(
            on_update=on_update,
            on_schema_break=on_schema_break,
            create_params=create_params.to_algokit_utils_params() if create_params else None,
            update_params=update_params,
            delete_params=delete_params,
            existing_deployments=existing_deployments,
            ignore_cache=ignore_cache,
            app_name=app_name,
            compilation_params=compilation_params,
            send_params=send_params,
        )

        return SupplyChainClient(deploy_response[0]), deploy_response[1]

    def get_app_client_by_creator_and_name(
        self,
        creator_address: str,
        app_name: str,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        ignore_cache: bool | None = None,
        app_lookup_cache: algokit_utils.ApplicationLookup | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> SupplyChainClient:
        """Get an app client by creator address and name"""
        return SupplyChainClient(
            self.app_factory.get_app_client_by_creator_and_name(
                creator_address,
                app_name,
                default_sender,
                default_signer,
                ignore_cache,
                app_lookup_cache,
                approval_source_map,
                clear_source_map,
            )
        )

    def get_app_client_by_id(
        self,
        app_id: int,
        app_name: str | None = None,
        default_sender: str | None = None,
        default_signer: TransactionSigner | None = None,
        approval_source_map: SourceMap | None = None,
        clear_source_map: SourceMap | None = None,
    ) -> SupplyChainClient:
        """Get an app client by app ID"""
        return SupplyChainClient(
            self.app_factory.get_app_client_by_id(
                app_id,
                app_name,
                default_sender,
                default_signer,
                approval_source_map,
                clear_source_map,
            )
        )


class SupplyChainFactoryParams:
    """Parameters for creating transactions for SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = SupplyChainFactoryCreateParams(app_factory)
        self.update = SupplyChainFactoryUpdateParams(app_factory)
        self.delete = SupplyChainFactoryDeleteParams(app_factory)

class SupplyChainFactoryCreateParams:
    """Parameters for 'create' operations of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateParams:
        """Creates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            compilation_params=compilation_params)

    def register_crop(
        self,
        args: tuple[int, str, int, str, str, str, str, str, str, str, str, str] | RegisterCropArgs,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None
    ) -> algokit_utils.AppCreateMethodCallParams:
        """Creates a new instance using the register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64 ABI method"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.create(
            algokit_utils.AppFactoryCreateMethodCallParams(
                **{
                **dataclasses.asdict(params),
                "method": "register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64",
                "args": _parse_abi_args(args),
                }
            ),
            compilation_params=compilation_params
        )

class SupplyChainFactoryUpdateParams:
    """Parameters for 'update' operations of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppUpdateParams:
        """Updates an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_update(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )

class SupplyChainFactoryDeleteParams:
    """Parameters for 'delete' operations of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        
    ) -> algokit_utils.AppDeleteParams:
        """Deletes an instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.params.bare.deploy_delete(
            algokit_utils.AppClientBareCallParams(**dataclasses.asdict(params)),
            )


class SupplyChainFactoryCreateTransaction:
    """Create transactions for SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = SupplyChainFactoryCreateTransactionCreate(app_factory)


class SupplyChainFactoryCreateTransactionCreate:
    """Create new instances of SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
    ) -> Transaction:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        return self.app_factory.create_transaction.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
        )


class SupplyChainFactorySend:
    """Send calls to SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory
        self.create = SupplyChainFactorySendCreate(app_factory)


class SupplyChainFactorySendCreate:
    """Send create calls to SupplyChain contract"""

    def __init__(self, app_factory: algokit_utils.AppFactory):
        self.app_factory = app_factory

    def bare(
        self,
        *,
        params: algokit_utils.CommonAppCallCreateParams | None = None,
        send_params: algokit_utils.SendParams | None = None,
        compilation_params: algokit_utils.AppClientCompilationParams | None = None,
    ) -> tuple[SupplyChainClient, algokit_utils.SendAppCreateTransactionResult]:
        """Creates a new instance using a bare call"""
        params = params or algokit_utils.CommonAppCallCreateParams()
        result = self.app_factory.send.bare.create(
            algokit_utils.AppFactoryCreateParams(**dataclasses.asdict(params)),
            send_params=send_params,
            compilation_params=compilation_params
        )
        return SupplyChainClient(result[0]), result[1]


class SupplyChainComposer:
    """Composer for creating transaction groups for SupplyChain contract calls"""

    def __init__(self, client: "SupplyChainClient"):
        self.client = client
        self._composer = client.algorand.new_group()
        self._result_mappers: list[typing.Callable[[algokit_utils.ABIReturn | None], object] | None] = []

    def register_crop(
        self,
        args: tuple[int, str, int, str, str, str, str, str, str, str, str, str] | RegisterCropArgs,
        params: algokit_utils.CommonAppCallParams | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_app_call_method_call(
            self.client.params.register_crop(
                args=args,
                params=params,
            )
        )
        self._result_mappers.append(
            lambda v: self.client.decode_return_value(
                "register_crop(uint64,string,uint64,string,string,string,string,string,string,string,string,string)uint64", v
            )
        )
        return self

    def clear_state(
        self,
        *,
        args: list[bytes] | None = None,
        params: algokit_utils.CommonAppCallParams | None = None,
    ) -> "SupplyChainComposer":
        params=params or algokit_utils.CommonAppCallParams()
        self._composer.add_app_call(
            self.client.params.clear_state(
                algokit_utils.AppClientBareCallParams(
                    **{
                        **dataclasses.asdict(params),
                        "args": args
                    }
                )
            )
        )
        return self
    
    def add_transaction(
        self, txn: Transaction, signer: TransactionSigner | None = None
    ) -> "SupplyChainComposer":
        self._composer.add_transaction(txn, signer)
        return self
    
    def composer(self) -> algokit_utils.TransactionComposer:
        return self._composer
    
    def simulate(
        self,
        allow_more_logs: bool | None = None,
        allow_empty_signatures: bool | None = None,
        allow_unnamed_resources: bool | None = None,
        extra_opcode_budget: int | None = None,
        exec_trace_config: SimulateTraceConfig | None = None,
        simulation_round: int | None = None,
        skip_signatures: bool | None = None,
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.simulate(
            allow_more_logs=allow_more_logs,
            allow_empty_signatures=allow_empty_signatures,
            allow_unnamed_resources=allow_unnamed_resources,
            extra_opcode_budget=extra_opcode_budget,
            exec_trace_config=exec_trace_config,
            simulation_round=simulation_round,
            skip_signatures=skip_signatures,
        )
    
    def send(
        self,
        send_params: algokit_utils.SendParams | None = None
    ) -> algokit_utils.SendAtomicTransactionComposerResults:
        return self._composer.send(send_params)
