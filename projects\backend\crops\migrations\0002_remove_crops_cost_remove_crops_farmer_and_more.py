# Generated by Django 5.2 on 2025-04-25 10:40

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("crops", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="crops",
            name="cost",
        ),
        migrations.RemoveField(
            model_name="crops",
            name="farmer",
        ),
        migrations.AddField(
            model_name="croptransfer",
            name="cost",
            field=models.PositiveBigIntegerField(
                default=0, validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
    ]
