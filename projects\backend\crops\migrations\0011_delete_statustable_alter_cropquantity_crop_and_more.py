# Generated by Django 5.2 on 2025-05-07 08:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("crops", "0010_statustable_cropstatus_cropquantity"),
    ]

    operations = [
        migrations.DeleteModel(
            name="StatusTable",
        ),
        migrations.AlterField(
            model_name="cropquantity",
            name="crop",
            field=models.ForeignKey(
                help_text="The crop to which these quantities belong.",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="quantities",
                to="crops.crops",
            ),
        ),
        migrations.AlterField(
            model_name="cropstatus",
            name="status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "Storage"), (2, "Ready"), (3, "In Progress"), (4, "Sold")],
                help_text="The status of the crop (e.g., Storage, Ready, In Progress, Sold).",
                max_length=1,
            ),
        ),
    ]
