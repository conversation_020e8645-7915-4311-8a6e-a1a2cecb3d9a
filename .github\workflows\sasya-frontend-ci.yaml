name: <PERSON><PERSON>te sasya-frontend

on:
  
  workflow_call:
  

jobs:
  validate:
    runs-on: 'ubuntu-latest'
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install poetry
        run: pipx install poetry

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          cache: "poetry"

      - name: Install algokit
        run: pipx install algokit

      - name: Install dependencies
        run: algokit project bootstrap all --project-name 'sasya-frontend'

      
      - name: Run linters
        run: algokit project run lint --project-name 'sasya-frontend'
      
      

      - name: Build
        run: algokit project run build --project-name 'sasya-frontend'
