import pytest
from algokit_utils import AlgorandClient, SigningAccount
from algokit_utils.config import config

config.configure(
    debug=True,
    trace_all=True, # uncomment to trace all transactions
)


@pytest.fixture(scope="session")
def algorand_client() -> AlgorandClient:
    # by default we are using localnet algod
    """
    Creates an AlgorandClient instance configured from environment variables.
    
    Returns:
        An AlgorandClient connected to the network specified by environment settings.
    """
    return AlgorandClient.from_environment()


    
@pytest.fixture(scope="session")
def deployer(algorand_client: AlgorandClient) -> SigningAccount:
    """
    Provides a signing account for deployment using environment-configured credentials.
    
    Returns:
        A SigningAccount instance initialized from environment variables.
    """
    return algorand_client.account.dispenser_from_environment()

