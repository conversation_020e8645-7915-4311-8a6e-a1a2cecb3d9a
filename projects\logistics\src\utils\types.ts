export interface WalletAccount {
  address: string
  name: string
  balance: number
}

export interface Asset {
  'asset-id': number
  amount: number
  'is-frozen': boolean
}
export interface Transaction {
  id: string
  type: 'earn' | 'burn' | 'pledge'
  amount: number
  timestamp: Date
  status: 'completed' | 'pending' | 'failed'
  networkFee?: number
  fromAddress: string
  toAddress: string
  transactionHash: string
  blockNumber?: number
  confirmations?: number
  errorMessage?: string
  notes?: string
  blockExplorerUrl?: string
}
export interface TransactionFilters {
  type?: 'earn' | 'burn' | 'pledge'
  status?: 'completed' | 'pending' | 'failed'
  dateFrom?: Date
  dateTo?: Date
}
