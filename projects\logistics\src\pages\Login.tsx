import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import AuthLayout from '../components/AuthLayout'
import FormInput from '../components/FormInput'
import { useToast } from '../components/ui/use-toast'
import { login } from '../services/authService'

const Login = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const navigate = useNavigate()

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid'
    }

    if (!password) {
      newErrors.password = 'Password is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    try {
      console.log(email, password)
      const user_data = await login(email, password)
      toast({
        title: 'Success!',
        description: 'You have successfully logged in.',
      })

      if (user_data['user'].wallet_address === null) {
        navigate('/onboarding')
      } else {
        navigate('/dashboard')
      }
    } catch (error) {
      console.error('Login error:', error)
      toast({
        title: 'Error!',
        description: 'An error occurred during login. Please check your credentials.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout title="Welcome Back To Sasya Token" subtitle="Enter your credentials to access your account">
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput label="Email" type="email" value={email} onChange={setEmail} error={errors.email} placeholder="Enter your email" />
        <FormInput
          label="Password"
          type="password"
          value={password}
          onChange={setPassword}
          error={errors.password}
          placeholder="Enter your password"
        />

        <div className="flex items-center justify-between">
          <Link to="/forgot-password" className="text-sm text-accent hover:text-[#00c853] transition-colors">
            Forgot password?
          </Link>
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className={`w-full py-3 px-4 bg-accent text-accent-foreground rounded-lg font-medium ${
            isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent/90 transition-colors'
          }`}
        >
          {isLoading ? 'Signing in...' : 'Sign In'}
        </button>

        <p className="text-center text-secondary-foreground">
          Don't have an account?{' '}
          <Link to="/register" className="text-accent hover:text-[#00c853] transition-colors">
            Register now
          </Link>
        </p>
      </form>
    </AuthLayout>
  )
}

export default Login
