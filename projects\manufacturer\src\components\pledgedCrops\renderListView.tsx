import { ChevronRight } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { Crop } from '../../utils/types'
interface ListViewProps {
  filteredCrops: Crop[]
  formatDate: (date: Date) => string
}

export function RenderListView({ filteredCrops, formatDate }: ListViewProps) {
  const navigate = useNavigate()

  return (
    <div className="bg-card-bg rounded-lg shadow-md overflow-hidden border border-card-border">
      <table className="w-full">
        <thead>
          <tr className="bg-accent-light/10">
            <th className="p-4 text-left text-primary-text font-semibold">Crop ID</th>
            <th className="p-4 text-left text-primary-text font-semibold">Quantity</th>
            <th className="p-4 text-left text-primary-text font-semibold">Location</th>
            <th className="p-4 text-left text-primary-text font-semibold">Pledge Date</th>
            <th className="p-4 text-left text-primary-text font-semibold">Status</th>
            <th className="p-4"></th>
          </tr>
        </thead>
        <tbody>
          {filteredCrops.map((crop) => (
            <tr
              key={crop.crop_id}
              className="border-t border-card-border hover:bg-accent-light/5 cursor-pointer transition-all duration-200 group"
              onClick={() => navigate(`/pledged-crops/${crop.crop_id}`)}
            >
              <td className="p-4">
                <div>
                  <div className="text-primary-text font-medium group-hover:text-accent transition-colors">{crop.crop_id}</div>
                  <div className="text-xs text-secondary-text mt-1">Grade {crop.crop_grade || 'A'}</div>
                </div>
              </td>
              <td className="p-4">
                <div className="text-primary-text group-hover:text-accent transition-colors">
                  {crop.quantity} {crop.unit}
                </div>
              </td>
              <td className="p-4">
                <div className="text-primary-text group-hover:text-accent transition-colors truncate max-w-[200px]">
                  {crop.location.address}
                </div>
              </td>
              <td className="p-4">
                <div className="text-secondary-text group-hover:text-accent-dark transition-colors">
                  {formatDate(new Date(crop.created_at))}
                </div>
              </td>
              <td className="p-4">
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-status-active-bg text-status-active-text">Active</span>
              </td>
              <td className="p-4">
                <ChevronRight className="h-5 w-5 text-secondary-text group-hover:text-accent transition-colors" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
