import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuthStore } from '../../stores/authStore';
import { 
  LayoutDashboard, 
  ArrowRightLeft, 
  LockKeyhole, 
  Package, 
  Truck, 
  FileText, 
  LogOut, 
  Sprout,
  Clock
} from 'lucide-react';

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userType, logout } = useAuthStore();
  
  const buyerLinks = [
    { name: 'Dashboard', icon: <LayoutDashboard className="w-5 h-5" />, path: '/dashboard' },
    { name: 'Transactions', icon: <ArrowRightLeft className="w-5 h-5" />, path: '/transactions' },
    { name: 'Escrow', icon: <LockKeyhole className="w-5 h-5" />, path: '/escrow' },
  ];
  
  const sellerLinks = [
    { name: 'Dashboard', icon: <LayoutDashboard className="w-5 h-5" />, path: '/seller/dashboard' },
    { name: 'Inventory', icon: <Package className="w-5 h-5" />, path: '/inventory' },
    { name: 'Shipments', icon: <Truck className="w-5 h-5" />, path: '/shipments' },
  ];
  
  const commonLinks = [
    { name: 'Documentation', icon: <FileText className="w-5 h-5" />, path: '/documentation' },
  ];
  
  const links = userType === 'buyer' ? buyerLinks : sellerLinks;
  
  const handleLogout = () => {
    logout();
    navigate('/login');
  };
  
  return (
    <aside className="flex flex-col w-64 h-screen bg-white border-r border-neutral-200">
      <div className="flex items-center gap-2 p-6">
        <Sprout className="w-8 h-8 text-primary-500" />
        <span className="text-lg font-bold text-neutral-900">AgriTram</span>
      </div>
      
      <nav className="flex-1 p-4">
        <ul className="space-y-1">
          {links.map((link) => (
            <li key={link.path}>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate(link.path)}
                className={`flex items-center w-full gap-3 px-4 py-3 rounded-lg transition-colors ${
                  location.pathname === link.path
                    ? 'bg-primary-50 text-primary-700'
                    : 'text-neutral-600 hover:bg-neutral-100'
                }`}
              >
                {link.icon}
                <span>{link.name}</span>
                {location.pathname === link.path && (
                  <motion.div
                    layoutId="sidebar-indicator"
                    className="absolute left-0 w-1 h-8 bg-primary-500 rounded-r"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                )}
              </motion.button>
            </li>
          ))}
        </ul>
        
        <div className="mt-6 pt-6 border-t border-neutral-200">
          <ul className="space-y-1">
            {commonLinks.map((link) => (
              <li key={link.path}>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => navigate(link.path)}
                  className={`flex items-center w-full gap-3 px-4 py-3 rounded-lg transition-colors ${
                    location.pathname === link.path
                      ? 'bg-primary-50 text-primary-700'
                      : 'text-neutral-600 hover:bg-neutral-100'
                  }`}
                >
                  {link.icon}
                  <span>{link.name}</span>
                  {location.pathname === link.path && (
                    <motion.div
                      layoutId="sidebar-indicator-common"
                      className="absolute left-0 w-1 h-8 bg-primary-500 rounded-r"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    />
                  )}
                </motion.button>
              </li>
            ))}
          </ul>
        </div>
      </nav>
      
      <div className="p-4 border-t border-neutral-200">
        <div className="flex items-center justify-between p-2 rounded-lg bg-neutral-50">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary-100 text-primary-700">
              <Clock className="w-4 h-4" />
            </div>
            <div>
              <p className="text-xs font-medium text-neutral-500">Last sync</p>
              <p className="text-sm font-medium text-neutral-700">2 min ago</p>
            </div>
          </div>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleLogout}
          className="flex items-center w-full gap-3 px-4 py-3 mt-4 text-red-600 rounded-lg hover:bg-red-50"
        >
          <LogOut className="w-5 h-5" />
          <span>Logout</span>
        </motion.button>
      </div>
    </aside>
  );
};

export default Sidebar;