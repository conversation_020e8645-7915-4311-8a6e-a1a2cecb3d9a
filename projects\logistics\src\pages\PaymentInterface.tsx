import Footer from '@/components/paymentinterface/Footer'
import Header from '@/components/paymentinterface/Header'
import PaymentMethodSelection from '@/components/paymentinterface/PaymentMethodSelection'
import { useToast } from '@/hooks/use-toast'
import apiClient from '@/services/apiClient'
import { Calendar, ChevronDown, CreditCard, HelpCircle, Loader2, Lock } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

interface PaymentFormData {
  cardNumber: string
  expiryDate: string
  cvv: string
  cardholderName: string
  address: {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
  }
  mobileNumber: string
}

interface ValidationErrors {
  [key: string]: string
}

const COUNTRIES = [
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'GB', name: 'United Kingdom' },
  // Add more countries as needed
]

const CARD_TYPES = {
  visa: /^4/,
  mastercard: /^5[1-5]/,
  amex: /^3[47]/,
  discover: /^6/,
}

export default function PaymentInterface() {
  const location = useLocation()
  const amount = location.state?.tokenAmount || 0
  const navigate = useNavigate()
  const currency = 'USD'
  const { toast } = useToast()
  const [formData, setFormData] = useState<PaymentFormData>({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    address: {
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
    },
    mobileNumber: '',
  })
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedMethod, setSelectedMethod] = useState<'card' | 'google-pay' | 'apple-pay' | 'mobile-money'>('card')
  const [cardType, setCardType] = useState<string>('')
  const [showCancelDialog, setShowCancelDialog] = useState(false)
  const [sessionTimeout, setSessionTimeout] = useState<number>(900)
  const [formTouched, setFormTouched] = useState(false)

  // Session timeout handler
  useEffect(() => {
    const handleSessionTimeout = () => {
      // Clear only sensitive card fields on timeout
      setFormData((prev) => ({
        ...prev,
        cardNumber: '',
        cvv: '',
        expiryDate: '',
      }))
      setErrors({ session: 'Session expired. Please refresh the page to continue.' })
      navigate(-1)
    }
    if (formTouched && sessionTimeout > 0) {
      const timer = setInterval(() => {
        setSessionTimeout((prev) => prev - 1)
      }, 1000)
      return () => clearInterval(timer)
    } else if (sessionTimeout === 0) {
      handleSessionTimeout()
    }
  }, [sessionTimeout, formTouched, navigate])

  const detectCardType = (number: string) => {
    for (const [type, pattern] of Object.entries(CARD_TYPES)) {
      if (pattern.test(number)) {
        return type
      }
    }
    return ''
  }

  const formatCardNumber = (value: string) => {
    const cleaned = value.replace(/\D/g, '')
    const groups = cleaned.match(/.{1,4}/g)
    return groups ? groups.join('-') : cleaned
  }

  const formatExpiryDate = (value: string) => {
    const cleaned = value.replace(/\D/g, '')
    if (cleaned.length >= 2) {
      return `${cleaned.slice(0, 2)}/${cleaned.slice(2, 4)}`
    }
    return cleaned
  }

  // Validation for the card payment form only
  const validateCardForm = () => {
    const newErrors: ValidationErrors = {}

    if (!/^\d{16,19}$/.test(formData.cardNumber.replace(/\D/g, ''))) {
      newErrors.cardNumber = 'Invalid card number'
    }
    const [month, year] = formData.expiryDate.split('/')
    const now = new Date()
    const expiry = new Date(2000 + parseInt(year || '0'), parseInt(month || '0') - 1)
    if (!month || !year || expiry <= now) {
      newErrors.expiryDate = 'Invalid expiry date'
    }
    if (!/^\d{3,4}$/.test(formData.cvv)) {
      newErrors.cvv = 'Invalid CVV'
    }
    if (!formData.cardholderName.trim()) {
      newErrors.cardholderName = 'Cardholder name is required'
    }
    if (!formData.address.street.trim()) {
      newErrors['address.street'] = 'Street address is required'
    }
    if (!formData.address.city.trim()) {
      newErrors['address.city'] = 'City is required'
    }
    if (!formData.address.state.trim()) {
      newErrors['address.state'] = 'State is required'
    }
    if (!formData.address.postalCode.trim()) {
      newErrors['address.postalCode'] = 'Postal code is required'
    }
    if (!formData.address.country) {
      newErrors['address.country'] = 'Country is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Common change handler for both methods
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormTouched(true)
    setSessionTimeout(900)

    if (name === 'cardNumber') {
      const formatted = formatCardNumber(value)
      setCardType(detectCardType(value))
      setFormData((prev) => ({ ...prev, [name]: formatted }))
    } else if (name === 'expiryDate') {
      const formatted = formatExpiryDate(value)
      setFormData((prev) => ({ ...prev, [name]: formatted }))
    } else if (name.startsWith('address.')) {
      const addressField = name.split('.')[1]
      setFormData((prev) => ({
        ...prev,
        address: { ...prev.address, [addressField]: value },
      }))
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }))
    }

    // Clear the error for the modified field
    setErrors((prev) => {
      const newErrors = { ...prev }
      delete newErrors[name]
      return newErrors
    })
  }

  // Submit handler for the card payment form
  const handleSubmitCard = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateCardForm()) return
    setIsProcessing(true)

    try {
      const [expMonth, expYear] = formData.expiryDate.split('/')

      const cleanCardNumber = formData.cardNumber.replace(/[\s-]/g, '')

      const response = await apiClient.post('/transaction/buy-tokens/', {
        amount: amount,
        payment_method: 'CARD',
        payment_details: {
          card_number: cleanCardNumber,
          exp_month: parseInt(expMonth),
          exp_year: parseInt('20' + expYear),
          cvc: formData.cvv,
        },
      })

      // Handle successful response
      if (response.data && response.status === 200) {
        toast({
          title: 'Success!',
          description: 'Payment successful. Transaction ID: ' + response.data.transaction.algorand_tx_id,
        })
        navigate(-1)
      } else {
        toast({
          title: 'Error!',
          description: 'Payment failed. Please try again. Transaction ID: ' + response.data.transaction.algorand_tx_id,
        })
      }
    } catch (error) {
      // Handle specific error cases if needed
      console.error('Payment error:', error)
      setErrors({ submit: 'Payment failed. Please try again.' })
    } finally {
      setIsProcessing(false)
    }
  }

  // Submit handler for the mobile money form
  const handleSubmitMobileMoney = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.mobileNumber.trim()) {
      setErrors({ mobileNumber: 'Mobile number is required' })
      return
    }
    setIsProcessing(true)
    try {
      const response = await apiClient.post('/transaction/buy-tokens/', {
        amount: amount,
        payment_method: 'MTM',
        payment_details: {
          mobile_number: formData.mobileNumber,
        },
      })

      console.log(response.data.transaction.algorand_tx_id)
      if (response.data && response.status === 200) {
        toast({
          title: 'Success!',
          description: 'Payment successful. Transaction ID: ' + response.data.transaction.algorand_tx_id,
        })
        navigate(-1)
      } else {
        toast({
          title: 'Error!',
          description: 'Payment failed. Please try again. Transaction ID: ' + response.data.transaction.algorand_tx_id,
        })
      }
    } catch (error) {
      setErrors({ submit: 'Payment failed. Please try again.' })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCancel = () => {
    if (formTouched) {
      setShowCancelDialog(true)
    } else {
      navigate('/dashboard')
    }
  }

  return (
    <div className="min-h-screen bg-[#212121] py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <Header amount={amount} currency={currency} sessionTimeout={sessionTimeout} />
        <PaymentMethodSelection selectedMethod={selectedMethod} setSelectedMethod={setSelectedMethod} />

        {/* Card Payment Form */}
        {selectedMethod === 'card' && (
          <form onSubmit={handleSubmitCard} className="space-y-6">
            <div className="bg-[#303030] p-6 rounded-lg space-y-4">
              <h3 className="text-lg font-medium text-white mb-4">Card Information</h3>
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Card Number</label>
                <div className="relative">
                  <input
                    type="text"
                    name="cardNumber"
                    value={formData.cardNumber}
                    onChange={handleInputChange}
                    className={`w-full bg-[#212121] border ${
                      errors.cardNumber ? 'border-red-500' : 'border-[#424242]'
                    } rounded-lg pl-10 pr-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                    placeholder="1234-5678-9012-3456"
                    maxLength={19}
                    required
                  />
                  <CreditCard className="absolute left-3 top-2.5 h-5 w-5 text-[#BDBDBD]" />
                </div>
                {errors.cardNumber && <p className="mt-2 text-sm text-red-500">{errors.cardNumber}</p>}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Expiry Date</label>
                  <div className="relative">
                    <input
                      type="text"
                      name="expiryDate"
                      value={formData.expiryDate}
                      onChange={handleInputChange}
                      className={`w-full bg-[#212121] border ${
                        errors.expiryDate ? 'border-red-500' : 'border-[#424242]'
                      } rounded-lg pl-10 pr-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                      placeholder="MM/YY"
                      maxLength={5}
                      required
                    />
                    <Calendar className="absolute left-3 top-2.5 h-5 w-5 text-[#BDBDBD]" />
                  </div>
                  {errors.expiryDate && <p className="mt-2 text-sm text-red-500">{errors.expiryDate}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">
                    CVV
                    <button type="button" className="ml-2 text-[#BDBDBD] hover:text-white">
                      <HelpCircle className="h-4 w-4 inline" />
                    </button>
                  </label>
                  <input
                    type="password"
                    name="cvv"
                    value={formData.cvv}
                    onChange={handleInputChange}
                    className={`w-full bg-[#212121] border ${
                      errors.cvv ? 'border-red-500' : 'border-[#424242]'
                    } rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                    placeholder="123"
                    maxLength={4}
                    required
                  />
                  {errors.cvv && <p className="mt-2 text-sm text-red-500">{errors.cvv}</p>}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Cardholder Name</label>
                <input
                  type="text"
                  name="cardholderName"
                  value={formData.cardholderName}
                  onChange={handleInputChange}
                  className={`w-full bg-[#212121] border ${
                    errors.cardholderName ? 'border-red-500' : 'border-[#424242]'
                  } rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                  placeholder="Name as shown on card"
                  required
                />
                {errors.cardholderName && <p className="mt-2 text-sm text-red-500">{errors.cardholderName}</p>}
              </div>
            </div>

            <div className="bg-[#303030] p-6 rounded-lg space-y-4">
              <h3 className="text-lg font-medium text-white mb-4">Billing Address</h3>
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Street Address</label>
                <input
                  type="text"
                  name="address.street"
                  value={formData.address.street}
                  onChange={handleInputChange}
                  className={`w-full bg-[#212121] border ${
                    errors['address.street'] ? 'border-red-500' : 'border-[#424242]'
                  } rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                  required
                />
                {errors['address.street'] && <p className="mt-2 text-sm text-red-500">{errors['address.street']}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">City</label>
                  <input
                    type="text"
                    name="address.city"
                    value={formData.address.city}
                    onChange={handleInputChange}
                    className={`w-full bg-[#212121] border ${
                      errors['address.city'] ? 'border-red-500' : 'border-[#424242]'
                    } rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                    required
                  />
                  {errors['address.city'] && <p className="mt-2 text-sm text-red-500">{errors['address.city']}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">State/Province</label>
                  <input
                    type="text"
                    name="address.state"
                    value={formData.address.state}
                    onChange={handleInputChange}
                    className={`w-full bg-[#212121] border ${
                      errors['address.state'] ? 'border-red-500' : 'border-[#424242]'
                    } rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                    required
                  />
                  {errors['address.state'] && <p className="mt-2 text-sm text-red-500">{errors['address.state']}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Postal Code</label>
                  <input
                    type="text"
                    name="address.postalCode"
                    value={formData.address.postalCode}
                    onChange={handleInputChange}
                    className={`w-full bg-[#212121] border ${
                      errors['address.postalCode'] ? 'border-red-500' : 'border-[#424242]'
                    } rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent`}
                    required
                  />
                  {errors['address.postalCode'] && <p className="mt-2 text-sm text-red-500">{errors['address.postalCode']}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Country</label>
                  <div className="relative">
                    <select
                      name="address.country"
                      value={formData.address.country}
                      onChange={handleInputChange}
                      className={`w-full bg-[#212121] border ${
                        errors['address.country'] ? 'border-red-500' : 'border-[#424242]'
                      } rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent appearance-none`}
                      required
                    >
                      <option value="">Select country</option>
                      {COUNTRIES.map((country) => (
                        <option key={country.code} value={country.code}>
                          {country.name}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="absolute right-3 top-2.5 h-5 w-5 text-[#BDBDBD] pointer-events-none" />
                  </div>
                  {errors['address.country'] && <p className="mt-2 text-sm text-red-500">{errors['address.country']}</p>}
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="submit"
                disabled={isProcessing}
                className="flex-1 bg-[#1B5E20] text-white px-6 py-3 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <Lock className="h-5 w-5" />
                    <span>
                      Pay{' '}
                      {amount.toLocaleString(undefined, {
                        style: 'currency',
                        currency,
                      })}
                    </span>
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="sm:flex-none bg-[#424242] text-white px-6 py-3 rounded-lg hover:bg-[#616161] transition-colors duration-300"
              >
                Cancel
              </button>
            </div>
          </form>
        )}

        {/* Mobile Money Form */}
        {selectedMethod === 'mobile-money' && (
          <form onSubmit={handleSubmitMobileMoney} className="space-y-6">
            <div className="bg-[#303030] p-6 rounded-lg space-y-4">
              <h3 className="text-lg font-medium text-white mb-4">Mobile Money Payment</h3>
              <div>
                <label className="block text-sm font-medium text-[#BDBDBD] mb-2">Mobile Number</label>
                <div className="flex">
                  <select className="bg-[#212121] border border-[#424242] rounded-l-lg px-3 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent">
                    <option value="+233">+233</option>
                    <option value="+234">+234</option>
                    <option value="+254">+254</option>
                    <option value="+256">+256</option>
                  </select>
                  <input
                    type="tel"
                    name="mobileNumber"
                    value={formData.mobileNumber}
                    onChange={handleInputChange}
                    className="flex-1 bg-[#212121] border border-l-0 border-[#424242] rounded-r-lg px-4 py-2 text-white focus:ring-2 focus:ring-[#00E676] focus:border-transparent"
                    placeholder="Mobile number"
                    required
                  />
                </div>
                {errors.mobileNumber && <p className="mt-2 text-sm text-red-500">{errors.mobileNumber}</p>}
                <p className="mt-2 text-sm text-[#BDBDBD]">You will receive a confirmation prompt on your mobile phone</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="submit"
                disabled={isProcessing}
                className="flex-1 bg-[#1B5E20] text-white px-6 py-3 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <Lock className="h-5 w-5" />
                    <span>
                      Pay{' '}
                      {amount.toLocaleString(undefined, {
                        style: 'currency',
                        currency,
                      })}
                    </span>
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="sm:flex-none bg-[#424242] text-white px-6 py-3 rounded-lg hover:bg-[#616161] transition-colors duration-300"
              >
                Cancel
              </button>
            </div>
          </form>
        )}

        {showCancelDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-[#303030] rounded-lg p-6 max-w-md w-full">
              <h3 className="text-xl font-bold text-white mb-4">Cancel Payment?</h3>
              <p className="text-[#BDBDBD] mb-6">Are you sure you want to cancel? All entered information will be lost.</p>
              <div className="flex justify-end gap-4">
                <button onClick={() => setShowCancelDialog(false)} className="px-4 py-2 text-[#BDBDBD] hover:text-white transition-colors">
                  Continue Payment
                </button>
                <button
                  onClick={() => navigate(-1)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Cancel Payment
                </button>
              </div>
            </div>
          </div>
        )}

        <Footer />
      </div>
    </div>
  )
}
