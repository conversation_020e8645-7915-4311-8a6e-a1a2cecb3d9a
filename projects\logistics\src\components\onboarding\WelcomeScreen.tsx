import { ArrowRight, DollarSign, Leaf, Truck } from 'lucide-react'

interface WelcomeScreenProps {
  handleNext: () => void
}

export default function WelcomeScreen({ handleNext }: WelcomeScreenProps) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">Welcome to FarmChain</h1>
        <p className="text-[#BDBDBD] text-lg max-w-2xl mx-auto">
          Your gateway to modern farming. Track crops, manage supply chains, and access financial services - all in one secure platform.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <div className="bg-[#303030] p-6 rounded-lg text-center">
          <div className="w-12 h-12 bg-[#1B5E20] rounded-full flex items-center justify-center mx-auto mb-4">
            <Leaf className="h-6 w-6 text-[#00E676]" />
          </div>
          <h3 className="text-white font-semibold mb-2">Crop Tracking</h3>
          <p className="text-[#BDBDBD]">Monitor your crops from planting to harvest with detailed analytics</p>
        </div>

        <div className="bg-[#303030] p-6 rounded-lg text-center">
          <div className="w-12 h-12 bg-[#1B5E20] rounded-full flex items-center justify-center mx-auto mb-4">
            <Truck className="h-6 w-6 text-[#00E676]" />
          </div>
          <h3 className="text-white font-semibold mb-2">Supply Chain</h3>
          <p className="text-[#BDBDBD]">Streamline your supply chain with transparent tracking and verification</p>
        </div>

        <div className="bg-[#303030] p-6 rounded-lg text-center">
          <div className="w-12 h-12 bg-[#1B5E20] rounded-full flex items-center justify-center mx-auto mb-4">
            <DollarSign className="h-6 w-6 text-[#00E676]" />
          </div>
          <h3 className="text-white font-semibold mb-2">Financial Access</h3>
          <p className="text-[#BDBDBD]">Access loans, insurance, and other financial services with ease</p>
        </div>
      </div>

      <div className="text-center">
        <button
          onClick={handleNext}
          className="bg-[#1B5E20] text-white px-8 py-3 rounded-lg hover:bg-[#2E7D32] transition-colors duration-300 flex items-center justify-center mx-auto"
        >
          Get Started
          <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </div>
  )
}
