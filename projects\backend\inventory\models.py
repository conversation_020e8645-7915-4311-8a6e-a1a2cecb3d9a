from django.db import models
from django.core.validators import MinValueValidator
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from crops.models import Crops

User = get_user_model()

# -------------------------------
# Inventory Quantity (per Trader, per Crop)
# -------------------------------
class InventoryQuantity(models.Model):
    """
    Tracks the quantity of a specific crop held by a trader, including total, ready to sell, and sold quantities.
    """

    trader = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="crop_quantities",
        help_text="The trader (user) who owns these crop quantities.",
    )
    total_quantity_to_date = models.DecimalField(
        max_digits=16,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text="Total quantity of this crop transacted by the trader since first joining the platform.",
    )
    total_quantity_in_storage = models.DecimalField(
        max_digits=16,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text="Total quantity of this crop currently in storage for the trader.",
    )
    ready_to_sell_quantity = models.DecimalField(
        max_digits=16,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text="Quantity of this crop that is ready to be sold. Cannot exceed total quantity.",
    )
    sold_quantity = models.DecimalField(
        max_digits=16,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text="Total quantity of this crop that has been sold. Cannot exceed total quantity.",
    )
    total_quantity_batches = models.PositiveIntegerField(
        default=0,
        help_text="Number of batches that make up the total quantity to date.",
    )
    storage_batches = models.PositiveIntegerField(
        default=0,
        help_text="Number of batches that make up the total quantity in storage.",
    )
    ready_to_sell_batches = models.PositiveIntegerField(
        default=0,
        help_text="Number of batches that make up the ready to sell quantity.",
    )
    sold_batches = models.PositiveIntegerField(
        default=0, help_text="Number of batches that make up the sold quantity."
    )
    updated_at = models.DateTimeField(
        auto_now=True, help_text="Timestamp for when the quantities were last updated."
    )

    def clean(self):
        """
        Validates logical consistency among all quantity and batch fields.
        """
        errors = {}
        # Quantity validations
        if self.ready_to_sell_quantity > self.total_quantity_to_date:
            errors["ready_to_sell_quantity"] = (
                "Ready to sell quantity cannot exceed total quantity to date."
            )
        if self.sold_quantity > self.total_quantity_to_date:
            errors["sold_quantity"] = (
                "Sold quantity cannot exceed total quantity to date."
            )
        if self.total_quantity_in_storage > self.total_quantity_to_date:
            errors["total_quantity_in_storage"] = (
                "Storage quantity cannot exceed total quantity to date."
            )
        if (
            self.ready_to_sell_quantity
            + self.sold_quantity
            + self.total_quantity_in_storage
            > self.total_quantity_to_date
        ):
            errors["__all__"] = (
                "Sum of ready to sell, sold, and storage quantities cannot exceed total quantity to date."
            )

        # Batch count validations
        if self.ready_to_sell_quantity == 0 and self.ready_to_sell_batches != 0:
            errors["ready_to_sell_batches"] = (
                "Batches for ready to sell must be zero if quantity is zero."
            )
        if self.sold_quantity == 0 and self.sold_batches != 0:
            errors["sold_batches"] = (
                "Batches for sold must be zero if quantity is zero."
            )
        if self.total_quantity_in_storage == 0 and self.storage_batches != 0:
            errors["storage_batches"] = (
                "Batches for storage must be zero if quantity is zero."
            )
        if self.total_quantity_to_date == 0 and self.total_quantity_batches != 0:
            errors["total_quantity_batches"] = (
                "Batches for total quantity must be zero if quantity is zero."
            )

        # Optional: Ensure batch counts are not illogical
        max_batches = max(
            self.total_quantity_batches,
            self.storage_batches,
            self.ready_to_sell_batches,
            self.sold_batches,
        )
        if (
            self.total_quantity_batches < max_batches
            or self.storage_batches < 0
            or self.ready_to_sell_batches < 0
            or self.sold_batches < 0
        ):
            errors["__all__"] = (
                errors.get("__all__", "")
                + " Batch counts must be non-negative and at least as large as the largest batch count among the types."
            )

        if errors:
            raise ValidationError(errors)

    def __str__(self):
        """
        Returns a string representation showing trader, crop, and total quantity.
        """
        return (
            f"{self.trader.username} — {self.crop} ({self.total_quantity} {self.unit})"
        )

    class Meta:
        verbose_name_plural = "Crop Quantities"
        ordering = ["-updated_at"]


# -------------------------------
# Crop Status (tracks status per trader and crop)
# -------------------------------
class InventoryCropStatus(models.Model):
    """
    Tracks the status of a crop for a trader. Multiple records per (trader, crop) pair are allowed (for status history).
    Status is chosen from a static set, but can be extended in the database.
    """

    STATUS_CHOICES = [
        ("storage", "Storage"),
        ("ready", "Ready"),
        ("in_progress", "In Progress"),
        ("sold", "Sold"),
    ]
    trader = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="crop_statuses",
        help_text="The trader (user) for whom the crop status is tracked.",
    )
    crop = models.ForeignKey(
        Crops,
        on_delete=models.CASCADE,
        related_name="status_records",
        help_text="The crop for which the status is tracked.",
    )
    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        help_text="The status of the crop (e.g., Storage, Ready, In Progress, Sold).",
    )
    created_at = models.DateTimeField(
        auto_now_add=True, help_text="When this status record was created."
    )
    updated_at = models.DateTimeField(
        auto_now=True, help_text="When this status record was last updated."
    )

    def __str__(self):
        return f"{self.trader.username} — {self.crop} — {self.get_status_display()}"

    class Meta:
        verbose_name_plural = "Crop Status Records"
        ordering = ["-created_at"]

