#pragma version 10
#pragma typetrack false

// algopy.arc4.ARC4Contract.approval_program() -> uint64:
main:
    intcblock 0 1 65 4
    bytecblock 0x00 "released" "withdrawn" "buyer" "milestones" "seller" "total_amount" 0x0000000000000000
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    txn NumAppArgs
    bz main_bare_routing@10
    pushbytess 0x866aa25b 0x8446de14 0x2030a656 0x0ec3967a 0xc675ae0b // method "create_contract(address,address,uint64,asset,uint64[])void", method "fund_escrow(axfer)void", method "approve_milestone(uint64)void", method "release_funds(asset)void", method "get_balance(asset)uint64"
    txna ApplicationArgs 0
    match main_create_contract_route@3 main_fund_escrow_route@4 main_approve_milestone_route@5 main_release_funds_route@6 main_get_balance_route@7

main_after_if_else@14:
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    intc_0 // 0
    return

main_get_balance_route@7:
    // smart_contracts/proof_of_unlock/contract.py:148
    // @arc4.abimethod(readonly=True)
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    txna ApplicationArgs 1
    btoi
    txnas Assets
    // smart_contracts/proof_of_unlock/contract.py:148
    // @arc4.abimethod(readonly=True)
    callsub get_balance
    pushbytes 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_release_funds_route@6:
    // smart_contracts/proof_of_unlock/contract.py:119
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    txna ApplicationArgs 1
    btoi
    txnas Assets
    // smart_contracts/proof_of_unlock/contract.py:119
    // @arc4.abimethod
    callsub release_funds
    intc_1 // 1
    return

main_approve_milestone_route@5:
    // smart_contracts/proof_of_unlock/contract.py:84
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    txna ApplicationArgs 1
    // smart_contracts/proof_of_unlock/contract.py:84
    // @arc4.abimethod
    callsub approve_milestone
    intc_1 // 1
    return

main_fund_escrow_route@4:
    // smart_contracts/proof_of_unlock/contract.py:68
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    txn GroupIndex
    intc_1 // 1
    -
    dup
    gtxns TypeEnum
    intc_3 // axfer
    ==
    assert // transaction type is axfer
    // smart_contracts/proof_of_unlock/contract.py:68
    // @arc4.abimethod
    callsub fund_escrow
    intc_1 // 1
    return

main_create_contract_route@3:
    // smart_contracts/proof_of_unlock/contract.py:23
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    txna ApplicationArgs 1
    txna ApplicationArgs 2
    txna ApplicationArgs 3
    txna ApplicationArgs 4
    btoi
    txnas Assets
    txna ApplicationArgs 5
    // smart_contracts/proof_of_unlock/contract.py:23
    // @arc4.abimethod
    callsub create_contract
    intc_1 // 1
    return

main_bare_routing@10:
    // smart_contracts/proof_of_unlock/contract.py:10
    // class ProofOfUnlock(ARC4Contract):
    txn OnCompletion
    bnz main_after_if_else@14
    txn ApplicationID
    !
    assert // can only call when creating
    intc_1 // 1
    return


// smart_contracts.proof_of_unlock.contract.ProofOfUnlock.create_contract(buyer: bytes, seller: bytes, total_amount: bytes, asset: uint64, milestone_percentages: bytes) -> void:
create_contract:
    // smart_contracts/proof_of_unlock/contract.py:23-31
    // @arc4.abimethod
    // def create_contract(
    //     self,
    //     buyer: arc4.Address,
    //     seller: arc4.Address,
    //     total_amount: arc4.UInt64,
    //     asset: Asset,
    //     milestone_percentages: arc4.DynamicArray[arc4.UInt64],
    // ) -> None:
    proto 5 0
    intc_0 // 0
    // smart_contracts/proof_of_unlock/contract.py:32
    // log("create_contract called", buyer, seller, total_amount, asset, milestone_percentages)
    pushbytess "" "create_contract called" // "", "create_contract called"
    frame_dig -5
    concat
    frame_dig -4
    concat
    frame_dig -3
    concat
    frame_dig -2
    itob
    concat
    frame_dig -1
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:34-35
    // # Verify percentage sum equals 100%
    // total_percent = UInt64(0)
    intc_0 // 0
    // smart_contracts/proof_of_unlock/contract.py:36
    // for percentage in milestone_percentages:
    frame_dig -1
    intc_0 // 0
    extract_uint16
    intc_0 // 0

create_contract_for_header@1:
    // smart_contracts/proof_of_unlock/contract.py:36
    // for percentage in milestone_percentages:
    frame_dig 4
    frame_dig 3
    <
    bz create_contract_after_for@4
    frame_dig -1
    extract 2 0
    frame_dig 4
    dup
    cover 2
    pushint 8 // 8
    *
    // smart_contracts/proof_of_unlock/contract.py:37
    // total_percent += percentage.native
    extract_uint64
    frame_dig 2
    +
    frame_bury 2
    intc_1 // 1
    +
    frame_bury 4
    b create_contract_for_header@1

create_contract_after_for@4:
    // smart_contracts/proof_of_unlock/contract.py:38
    // assert total_percent == 100, "Total percentages must equal 100%"
    frame_dig 2
    pushint 100 // 100
    ==
    assert // Total percentages must equal 100%
    // smart_contracts/proof_of_unlock/contract.py:40-41
    // # Initialize state
    // self.buyer = buyer
    bytec_3 // "buyer"
    frame_dig -5
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:42
    // self.seller = seller
    bytec 5 // "seller"
    frame_dig -4
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:43
    // self.total_amount = total_amount
    bytec 6 // "total_amount"
    frame_dig -3
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:44
    // self.released = arc4.UInt64(0)
    bytec_1 // "released"
    bytec 7 // 0x0000000000000000
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:45
    // self.withdrawn = arc4.UInt64(0)
    bytec_2 // "withdrawn"
    bytec 7 // 0x0000000000000000
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:46
    // log("State after storing contract data", self.buyer, self.seller, self.total_amount, self.released, self.withdrawn)
    intc_0 // 0
    bytec_3 // "buyer"
    app_global_get_ex
    assert // check self.buyer exists
    pushbytes "State after storing contract data"
    swap
    concat
    intc_0 // 0
    bytec 5 // "seller"
    app_global_get_ex
    assert // check self.seller exists
    concat
    intc_0 // 0
    bytec 6 // "total_amount"
    app_global_get_ex
    assert // check self.total_amount exists
    concat
    intc_0 // 0
    bytec_1 // "released"
    app_global_get_ex
    assert // check self.released exists
    concat
    intc_0 // 0
    bytec_2 // "withdrawn"
    app_global_get_ex
    assert // check self.withdrawn exists
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:48-53
    // itxn.AssetTransfer(
    //     asset_receiver=Global.current_application_address,
    //     xfer_asset=asset,
    //     asset_amount=0,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/proof_of_unlock/contract.py:52
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/proof_of_unlock/contract.py:49
    // asset_receiver=Global.current_application_address,
    global CurrentApplicationAddress
    // smart_contracts/proof_of_unlock/contract.py:51
    // asset_amount=0,
    intc_0 // 0
    itxn_field AssetAmount
    frame_dig -2
    itxn_field XferAsset
    itxn_field AssetReceiver
    // smart_contracts/proof_of_unlock/contract.py:48
    // itxn.AssetTransfer(
    intc_3 // axfer
    itxn_field TypeEnum
    itxn_field Fee
    // smart_contracts/proof_of_unlock/contract.py:48-53
    // itxn.AssetTransfer(
    //     asset_receiver=Global.current_application_address,
    //     xfer_asset=asset,
    //     asset_amount=0,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    // smart_contracts/proof_of_unlock/contract.py:55-56
    // # Create milestones
    // milestones = arc4.DynamicArray[Milestone]()
    pushbytes 0x0000
    frame_bury 0
    intc_0 // 0
    frame_bury 1

create_contract_for_header@6:
    // smart_contracts/proof_of_unlock/contract.py:57
    // for percentage in milestone_percentages:
    frame_dig 1
    frame_dig 3
    <
    bz create_contract_after_for@9
    frame_dig -1
    extract 2 0
    frame_dig 1
    dup
    cover 2
    pushint 8 // 8
    *
    pushint 8 // 8
    extract3 // on error: Index access is out of bounds
    // smart_contracts/proof_of_unlock/contract.py:58-64
    // milestones.append(
    //     Milestone(
    //         percentage=percentage,
    //         buyer_approved=arc4.Bool(False),
    //         seller_approved=arc4.Bool(False),
    //     )
    // )
    frame_dig 0
    extract 2 0
    // smart_contracts/proof_of_unlock/contract.py:59-63
    // Milestone(
    //     percentage=percentage,
    //     buyer_approved=arc4.Bool(False),
    //     seller_approved=arc4.Bool(False),
    // )
    swap
    // smart_contracts/proof_of_unlock/contract.py:61
    // buyer_approved=arc4.Bool(False),
    bytec_0 // 0x00
    // smart_contracts/proof_of_unlock/contract.py:59-63
    // Milestone(
    //     percentage=percentage,
    //     buyer_approved=arc4.Bool(False),
    //     seller_approved=arc4.Bool(False),
    // )
    concat
    intc_2 // 65
    intc_0 // 0
    setbit
    // smart_contracts/proof_of_unlock/contract.py:58-64
    // milestones.append(
    //     Milestone(
    //         percentage=percentage,
    //         buyer_approved=arc4.Bool(False),
    //         seller_approved=arc4.Bool(False),
    //     )
    // )
    concat
    dup
    len
    pushint 9 // 9
    /
    itob
    extract 6 2
    swap
    concat
    frame_bury 0
    intc_1 // 1
    +
    frame_bury 1
    b create_contract_for_header@6

create_contract_after_for@9:
    // smart_contracts/proof_of_unlock/contract.py:65
    // self.milestones = milestones.copy()
    bytec 4 // "milestones"
    frame_dig 0
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:66
    // log("Milestones after storing", self.milestones)
    intc_0 // 0
    bytec 4 // "milestones"
    app_global_get_ex
    assert // check self.milestones exists
    pushbytes "Milestones after storing"
    swap
    concat
    log
    retsub


// smart_contracts.proof_of_unlock.contract.ProofOfUnlock.fund_escrow(payment: uint64) -> void:
fund_escrow:
    // smart_contracts/proof_of_unlock/contract.py:68-69
    // @arc4.abimethod
    // def fund_escrow(self, payment: gtxn.AssetTransferTransaction) -> None:
    proto 1 0
    // smart_contracts/proof_of_unlock/contract.py:70
    // log("fund_escrow called", payment.asset_sender, payment.asset_receiver, payment.asset_amount)
    frame_dig -1
    gtxns AssetSender
    pushbytes "fund_escrow called"
    dig 1
    concat
    frame_dig -1
    gtxns AssetReceiver
    swap
    dig 1
    concat
    frame_dig -1
    gtxns AssetAmount
    dup
    itob
    uncover 2
    dig 1
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:72-73
    // # Verify transaction group structure
    // assert Txn.group_index == 1, "Must be second transaction in group"
    txn GroupIndex
    intc_1 // 1
    ==
    assert // Must be second transaction in group
    // smart_contracts/proof_of_unlock/contract.py:76-77
    // # Validate payment details
    // assert payment.asset_sender == self.buyer.native, "Invalid buyer"
    intc_0 // 0
    bytec_3 // "buyer"
    app_global_get_ex
    assert // check self.buyer exists
    dig 4
    ==
    assert // Invalid buyer
    // smart_contracts/proof_of_unlock/contract.py:79
    // payment.asset_receiver == Global.current_application_address
    global CurrentApplicationAddress
    uncover 3
    ==
    // smart_contracts/proof_of_unlock/contract.py:78-80
    // assert (
    //     payment.asset_receiver == Global.current_application_address
    // ), "Invalid receiver"
    assert // Invalid receiver
    // smart_contracts/proof_of_unlock/contract.py:81
    // assert payment.asset_amount == self.total_amount.native, "Incorrect amount"
    intc_0 // 0
    bytec 6 // "total_amount"
    app_global_get_ex
    assert // check self.total_amount exists
    btoi
    uncover 2
    ==
    assert // Incorrect amount
    // smart_contracts/proof_of_unlock/contract.py:82
    // log("Escrow funded", payment.asset_sender, payment.asset_amount)
    pushbytes "Escrow funded"
    uncover 2
    concat
    swap
    concat
    log
    retsub


// smart_contracts.proof_of_unlock.contract.ProofOfUnlock.approve_milestone(milestone_index: bytes) -> void:
approve_milestone:
    // smart_contracts/proof_of_unlock/contract.py:84-85
    // @arc4.abimethod
    // def approve_milestone(self, milestone_index: arc4.UInt64) -> None:
    proto 1 0
    // smart_contracts/proof_of_unlock/contract.py:86
    // log("approve_milestone called", milestone_index, Txn.sender)
    pushbytes "approve_milestone called"
    frame_dig -1
    concat
    txn Sender
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:88
    // idx = milestone_index.native
    frame_dig -1
    btoi
    dup
    // smart_contracts/proof_of_unlock/contract.py:89
    // milestones = self.milestones.copy()
    intc_0 // 0
    bytec 4 // "milestones"
    app_global_get_ex
    swap
    dup
    cover 2
    cover 3
    assert // check self.milestones exists
    // smart_contracts/proof_of_unlock/contract.py:90
    // assert idx < milestones.length, "Invalid milestone index"
    dup
    intc_0 // 0
    extract_uint16
    dig 2
    >
    assert // Invalid milestone index
    // smart_contracts/proof_of_unlock/contract.py:92-93
    // # Get current milestone
    // milestone = milestones[idx].copy()
    extract 2 0
    swap
    pushint 9 // 9
    *
    dup
    cover 2
    pushint 9 // 9
    extract3 // on error: Index access is out of bounds
    dup
    // smart_contracts/proof_of_unlock/contract.py:95
    // milestone.buyer_approved and milestone.seller_approved
    pushint 64 // 64
    getbit
    bytec_0 // 0x00
    intc_0 // 0
    uncover 2
    setbit
    bytec_0 // 0x00
    !=
    // smart_contracts/proof_of_unlock/contract.py:94-96
    // assert not (
    //     milestone.buyer_approved and milestone.seller_approved
    // ), "Already approved"
    bz approve_milestone_bool_false@3
    // smart_contracts/proof_of_unlock/contract.py:95
    // milestone.buyer_approved and milestone.seller_approved
    frame_dig 3
    intc_2 // 65
    getbit
    bytec_0 // 0x00
    intc_0 // 0
    uncover 2
    setbit
    bytec_0 // 0x00
    !=
    // smart_contracts/proof_of_unlock/contract.py:94-96
    // assert not (
    //     milestone.buyer_approved and milestone.seller_approved
    // ), "Already approved"
    bz approve_milestone_bool_false@3
    intc_1 // 1

approve_milestone_bool_merge@4:
    // smart_contracts/proof_of_unlock/contract.py:94-96
    // assert not (
    //     milestone.buyer_approved and milestone.seller_approved
    // ), "Already approved"
    !
    assert // Already approved
    // smart_contracts/proof_of_unlock/contract.py:98-99
    // # Update approval status
    // if Txn.sender == self.buyer.native:
    txn Sender
    intc_0 // 0
    bytec_3 // "buyer"
    app_global_get_ex
    assert // check self.buyer exists
    ==
    assert // Unauthorized
    // smart_contracts/proof_of_unlock/contract.py:100
    // milestone.buyer_approved = arc4.Bool(True)
    frame_dig 3
    pushint 64 // 64
    intc_1 // 1
    setbit
    // smart_contracts/proof_of_unlock/contract.py:101
    // milestone.seller_approved = arc4.Bool(True)
    intc_2 // 65
    intc_1 // 1
    setbit
    dup
    frame_bury 3
    // smart_contracts/proof_of_unlock/contract.py:105-106
    // # Store updated milestone
    // milestones[idx] = milestone.copy()
    frame_dig 2
    pushint 2 // 2
    +
    frame_dig 1
    swap
    dig 2
    replace3
    // smart_contracts/proof_of_unlock/contract.py:107
    // self.milestones = milestones.copy()
    bytec 4 // "milestones"
    swap
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:108
    // log("Milestone after approval", idx, milestone, self.milestones)
    frame_dig 0
    itob
    pushbytes "Milestone after approval"
    swap
    concat
    dig 1
    concat
    intc_0 // 0
    bytec 4 // "milestones"
    app_global_get_ex
    assert // check self.milestones exists
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:110-111
    // # Update released amount if both approved
    // if milestone.buyer_approved and milestone.seller_approved:
    pushint 64 // 64
    getbit
    bytec_0 // 0x00
    intc_0 // 0
    uncover 2
    setbit
    bytec_0 // 0x00
    !=
    bz approve_milestone_after_if_else@10
    frame_dig 3
    intc_2 // 65
    getbit
    bytec_0 // 0x00
    intc_0 // 0
    uncover 2
    setbit
    bytec_0 // 0x00
    !=
    bz approve_milestone_after_if_else@10
    // smart_contracts/proof_of_unlock/contract.py:112
    // percentage = milestone.percentage.native
    frame_dig 3
    intc_0 // 0
    extract_uint64
    // smart_contracts/proof_of_unlock/contract.py:113
    // current_released = self.released.native
    intc_0 // 0
    bytec_1 // "released"
    app_global_get_ex
    assert // check self.released exists
    btoi
    // smart_contracts/proof_of_unlock/contract.py:114
    // additional = (self.total_amount.native * percentage) // 100
    intc_0 // 0
    bytec 6 // "total_amount"
    app_global_get_ex
    assert // check self.total_amount exists
    btoi
    uncover 2
    *
    pushint 100 // 100
    /
    // smart_contracts/proof_of_unlock/contract.py:115
    // self.released = arc4.UInt64(current_released + additional)
    +
    itob
    bytec_1 // "released"
    swap
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:116
    // log("Released updated", self.released)
    intc_0 // 0
    bytec_1 // "released"
    app_global_get_ex
    assert // check self.released exists
    pushbytes "Released updated"
    swap
    concat
    log

approve_milestone_after_if_else@10:
    retsub

approve_milestone_bool_false@3:
    intc_0 // 0
    b approve_milestone_bool_merge@4


// smart_contracts.proof_of_unlock.contract.ProofOfUnlock.release_funds(asset: uint64) -> void:
release_funds:
    // smart_contracts/proof_of_unlock/contract.py:119-120
    // @arc4.abimethod
    // def release_funds(self, asset: Asset) -> None:
    proto 1 0
    // smart_contracts/proof_of_unlock/contract.py:121
    // log("release_funds called", asset, Txn.sender)
    frame_dig -1
    itob
    pushbytes "release_funds called"
    swap
    concat
    txn Sender
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:123
    // if Txn.sender != self.buyer.native:
    txn Sender
    intc_0 // 0
    bytec_3 // "buyer"
    app_global_get_ex
    assert // check self.buyer exists
    !=
    bz release_funds_after_if_else@2
    // smart_contracts/proof_of_unlock/contract.py:124
    // log("Withdraw failed: Only buyer can release", Txn.sender)
    pushbytes "Withdraw failed: Only buyer can release"
    txn Sender
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:125
    // assert False, "Only buyer can release"
    err // Only buyer can release

release_funds_after_if_else@2:
    // smart_contracts/proof_of_unlock/contract.py:127
    // available = self.released.native - self.withdrawn.native
    intc_0 // 0
    bytec_1 // "released"
    app_global_get_ex
    assert // check self.released exists
    btoi
    intc_0 // 0
    bytec_2 // "withdrawn"
    app_global_get_ex
    assert // check self.withdrawn exists
    btoi
    -
    dup
    // smart_contracts/proof_of_unlock/contract.py:128
    // if available <= 0:
    intc_0 // 0
    <=
    bz release_funds_after_if_else@4
    // smart_contracts/proof_of_unlock/contract.py:129
    // log("Withdraw failed: No funds available", available)
    itob
    pushbytes "Withdraw failed: No funds available"
    swap
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:130
    // assert False, "No funds available"
    err // No funds available

release_funds_after_if_else@4:
    // smart_contracts/proof_of_unlock/contract.py:133-134
    // # Update withdrawn amount
    // self.withdrawn = arc4.UInt64(self.withdrawn.native + available)
    intc_0 // 0
    bytec_2 // "withdrawn"
    app_global_get_ex
    assert // check self.withdrawn exists
    btoi
    dig 1
    +
    itob
    bytec_2 // "withdrawn"
    swap
    app_global_put
    // smart_contracts/proof_of_unlock/contract.py:135
    // log("Withdrawn updated", self.withdrawn)
    intc_0 // 0
    bytec_2 // "withdrawn"
    app_global_get_ex
    assert // check self.withdrawn exists
    pushbytes "Withdrawn updated"
    swap
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:137-138
    // # Log successful withdrawal
    // log("Withdraw success", self.seller.native, available)
    intc_0 // 0
    bytec 5 // "seller"
    app_global_get_ex
    assert // check self.seller exists
    pushbytes "Withdraw success"
    swap
    concat
    dig 1
    itob
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:140-146
    // # Send payment
    // itxn.AssetTransfer(
    //     asset_receiver=self.seller.native,
    //     asset_amount=available,
    //     xfer_asset=asset,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/proof_of_unlock/contract.py:145
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/proof_of_unlock/contract.py:142
    // asset_receiver=self.seller.native,
    intc_0 // 0
    bytec 5 // "seller"
    app_global_get_ex
    assert // check self.seller exists
    frame_dig -1
    itxn_field XferAsset
    uncover 2
    itxn_field AssetAmount
    itxn_field AssetReceiver
    // smart_contracts/proof_of_unlock/contract.py:140-141
    // # Send payment
    // itxn.AssetTransfer(
    intc_3 // axfer
    itxn_field TypeEnum
    itxn_field Fee
    // smart_contracts/proof_of_unlock/contract.py:140-146
    // # Send payment
    // itxn.AssetTransfer(
    //     asset_receiver=self.seller.native,
    //     asset_amount=available,
    //     xfer_asset=asset,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    retsub


// smart_contracts.proof_of_unlock.contract.ProofOfUnlock.get_balance(asset: uint64) -> bytes:
get_balance:
    // smart_contracts/proof_of_unlock/contract.py:148-149
    // @arc4.abimethod(readonly=True)
    // def get_balance(self, asset: Asset) -> arc4.UInt64:
    proto 1 1
    // smart_contracts/proof_of_unlock/contract.py:150
    // log("get_balance called", asset)
    frame_dig -1
    itob
    pushbytes "get_balance called"
    swap
    concat
    log
    // smart_contracts/proof_of_unlock/contract.py:152
    // app_address = op.Global.current_application_address
    global CurrentApplicationAddress
    // smart_contracts/proof_of_unlock/contract.py:153
    // asset_balance, exists = op.AssetHoldingGet.asset_balance(app_address, asset)
    frame_dig -1
    asset_holding_get AssetBalance
    // smart_contracts/proof_of_unlock/contract.py:154-155
    // # Explicitly convert to arc4.UInt64 to ensure type consistency
    // return arc4.UInt64(asset_balance) if exists else arc4.UInt64(0)
    bz get_balance_ternary_false@2
    frame_dig 0
    itob

get_balance_ternary_merge@3:
    // smart_contracts/proof_of_unlock/contract.py:154-155
    // # Explicitly convert to arc4.UInt64 to ensure type consistency
    // return arc4.UInt64(asset_balance) if exists else arc4.UInt64(0)
    swap
    retsub

get_balance_ternary_false@2:
    // smart_contracts/proof_of_unlock/contract.py:154-155
    // # Explicitly convert to arc4.UInt64 to ensure type consistency
    // return arc4.UInt64(asset_balance) if exists else arc4.UInt64(0)
    bytec 7 // 0x0000000000000000
    b get_balance_ternary_merge@3
