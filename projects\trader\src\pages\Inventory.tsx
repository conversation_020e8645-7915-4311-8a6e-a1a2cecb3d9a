import apiClient from '@/services/apiClient'
import { InventoryItem } from '@/utils/types'
import { Edit, MoreHorizontal, Wheat } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const staticInventoryInfo = [
  {
    id: 1,
    name: 'Cocoa Beans',
    description: 'Premium quality organic cocoa beans',
    unit: 'tonnes',
    category: 'Beans',
    icon: <Wheat className="w-5 h-5" />,
  },
]

const Inventory = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const navigate = useNavigate()

  useEffect(() => {
    apiClient
      .get('/inventory/inventory-quantity/')
      .then((res) => {
        const apiData = res.data
        const merged = staticInventoryInfo.map((item) => ({
          ...item,
          ...apiData,
        }))
        setInventory(merged)
      })
      .catch((err) => {
        console.log(err)
        setInventory(
          staticInventoryInfo.map((item) => ({
            ...item,
            total_quantity_to_date: 0,
            total_quantity_batches: 0,
            total_quantity_in_storage: 0,
            ready_to_sell_quantity: 0,
            sold_quantity: 0,
          })),
        )
      })
  }, [])

  return (
    <div className="min-h-screen bg-main-bg py-8 px-4 sm:px-6 lg:px-8">
      <div className="bg-card-bg rounded-xl shadow-md overflow-hidden">
        <div className="p-6 border-b border-card-border flex items-center justify-between">
          <h2 className="text-xl font-semibold text-primary-text">Product Inventory</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-card-border">
                <th className="py-4 px-6 text-left text-sm font-medium text-accent">Product</th>
                <th className="py-4 px-6 text-left text-sm font-medium text-accent">Category</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-accent">Total Quantity</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-accent">Total Batches</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-accent">Ready To Sell</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-accent">In Storage</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-accent">Sold</th>
                <th className="py-4 px-6 text-center text-sm font-medium text-accent">Actions</th>
              </tr>
            </thead>
            <tbody>
              {inventory.map((item) => (
                <tr key={item.id} className="border-b border-card-border hover:bg-alt-bg transition-colors">
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-accent-light text-accent">{item.icon}</div>
                      <div>
                        <p className="font-medium text-primary-text">{item.name}</p>
                        <p className="text-xs text-secondary-text">{item.description}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <p className="text-sm text-primary-text">{item.category}</p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-primary-text">
                      {item.total_quantity_to_date} {item.unit}
                    </p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-primary-text">{item.total_quantity_batches}</p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-status-active-text">
                      {item.ready_to_sell_quantity} {item.unit}
                    </p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-status-active-text">
                      {item.total_quantity_in_storage} {item.unit}
                    </p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <p className="text-sm font-medium text-status-pending-text">
                      {item.sold_quantity} {item.unit}
                    </p>
                  </td>
                  <td className="py-4 px-6 text-center">
                    <div className="flex items-center justify-center gap-3">
                      <button
                        className="p-2 rounded-full bg-status-completed-bg text-status-completed-text hover:opacity-90 transition-opacity"
                        onClick={() => navigate('/inventory/details')}
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        className="p-2 rounded-full bg-alt-bg text-primary-text hover:opacity-90 transition-opacity"
                        onClick={() => navigate('/inventory/details')}
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default Inventory
