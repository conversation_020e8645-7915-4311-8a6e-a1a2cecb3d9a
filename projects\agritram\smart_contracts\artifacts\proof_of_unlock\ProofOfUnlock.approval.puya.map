{"version": 3, "sources": ["../../proof_of_unlock/contract.py"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;;AAAA;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAAA;;;;;;;;;;;;AAAA;;AA0IK;;AAAA;AAAA;AAAA;;AAAA;AA1IL;;;AAAA;AAAA;;AA0IK;;;AAAA;;;;;;AAAA;AAAA;AAAA;AAAA;;AA7BA;;AAAA;AAAA;AAAA;;AAAA;AA7GL;;;AAAA;AAAA;;AA6GK;;;AAAA;;AAnCA;;AAAA;AAAA;AAAA;;AAAA;AA1EL;;;AA0EK;;;AAAA;;AAhBA;;AAAA;AAAA;AAAA;;AAAA;AA1DL;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA0DK;;;AAAA;;AA7CA;;AAAA;AAAA;AAAA;;AAAA;AAbL;;;AAAA;;;AAAA;;;AAAA;;;AAAA;AAAA;;AAAA;;;AAaK;;;AAAA;;AAbL;;AAAA;;;;;;;;;AAaA;;;;AASsC;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAAA;AAAO;;AAAA;AAAQ;;AAAA;AAAc;;AAAA;AAAA;AAAO;;AAAA;AAAlE;AAGgB;AACxB;;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;;AAAA;;AAAA;;;AAAA;;AAAA;AAAA;;AAAA;;AAAA;AAC6B;AAAjB;;AAAA;AAAA;;;;;;;;;AACG;;AAAiB;;AAAjB;AAAP;AAGA;AAAA;;AAAA;AACA;;AAAA;;AAAA;AACA;;AAAA;;AAAA;AACA;AAAgB;;AAAhB;AACA;AAAiB;;AAAjB;AACyC;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAY;AAAA;;AAAA;AAAA;AAAA;AAAa;AAAA;;AAAA;AAAA;AAAA;AAAmB;AAAA;AAAA;AAAA;AAAA;AAAe;AAAA;AAAA;AAAA;AAAA;AAApG;AAEA;AAIQ;;AAHW;;AAEF;;;;;;;;;AAHjB;;;;;AAAA;AAQa;;;;AAAA;;;;;AACrB;;AAAA;;AAAA;AAAA;;;AAAA;;AAAA;;;AAAA;;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA;AACY;;AAAA;;;AACI;AAEmB;AAFnB;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;;AAAA;AAAA;AAAA;;;;;;;;;AAOJ;;AAAA;;AAAA;AACgC;AAAA;;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAhC;;AAER;;;AAEkC;;AAAA;;AAAA;;;;;;;;;;;;;;;;;;;;AAAA;;AAAA;AAAsB;;AAAA;;AAAA;AAAA;;AAAA;AAAwB;;AAAA;;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAxE;AAGO;;AAAmB;AAAnB;AAAP;AAI+B;AAAA;AAAA;AAAA;AAAxB;;AAAA;AAAP;AAE8B;;AAA1B;;AAAA;AADJ;AAG+B;AAAA;;AAAA;AAAA;AAAA;AAAxB;;AAAA;AAAP;AACqB;;;;;;;;;;;;;;;AAAA;;AAAA;AAAsB;AAAA;AAA3C;;AAER;;;AAEwC;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAAA;AAAiB;;AAAA;AAAjD;AAEA;;AAAM;AAAN;AACa;AAAA;;AAAA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AACA;AAAA;AAAA;AAAN;;AAAA;AAAP;AAGY;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AAER;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AADG;;;AAC0B;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAD1B;;;;AAAA;AAAP;AAKG;;AAAc;AAAA;AAAA;AAAA;AAAd;AAAX;AACY;;AAAA;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;AAKJ;;AAAA;;AAAA;AAAA;;AAAA;AAAA;;AAAA;AACA;;AAAA;AAAA;AACgC;;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAK;;AAAA;AAAW;AAAA;;AAAA;AAAA;AAAA;AAAhD;AAGG;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;;;AAA6B;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAA7B;;;AACC;;AAAa;AAAA;AACM;AAAA;AAAA;AAAA;AAAA;AACL;AAAA;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAA0C;;AAA3C;AACe;AAAZ;AAAhB;AAAA;AAAA;AACwB;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAxB;;;;;;AAGZ;;;AAEoC;;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAO;;AAAA;AAAnC;AAEG;;AAAc;AAAA;AAAA;AAAA;AAAd;AAAX;;;AAC2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAAA;AAA/C;AACA;AAEQ;AAAA;AAAA;AAAA;AAAA;AAAuB;AAAA;AAAA;AAAA;AAAA;AAAvB;AAAZ;AACgB;AAAb;AAAX;;;AACuD;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAA3C;AACA;AAIyB;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAZ;AAAjB;AAAA;AAAA;AACyB;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAzB;AAGwB;AAAA;;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAoB;;AAAA;AAAA;AAA5C;AAGA;AAIQ;;AAHW;AAAA;;AAAA;AAAA;;;;;;;;;;;AADnB;;;;;AAAA;;AAOR;;;AAEkC;;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAA1B;AAEc;;AACU;;AAAA;;AAEjB;;;AAAA;;AAAA;AAAP;AAAA;AAAiD;;", "op_pc_offset": 0, "pc_events": {"1": {"subroutine": "algopy.arc4.ARC4Contract.approval_program", "params": {}, "block": "main", "stack_in": [], "op": "intcblock 0 1 65 4"}, "7": {"op": "bytecblock 0x00 \"released\" \"withdrawn\" \"buyer\" \"milestones\" \"seller\" \"total_amount\" 0x0000000000000000"}, "76": {"op": "txn NumAppArgs", "defined_out": ["tmp%0#1"], "stack_out": ["tmp%0#1"]}, "78": {"op": "bz main_bare_routing@10", "stack_out": []}, "81": {"op": "pushbytess 0x866aa25b 0x8446de14 0x2030a656 0x0ec3967a 0xc675ae0b // method \"create_contract(address,address,uint64,asset,uint64[])void\", method \"fund_escrow(axfer)void\", method \"approve_milestone(uint64)void\", method \"release_funds(asset)void\", method \"get_balance(asset)uint64\"", "defined_out": ["Method(approve_milestone(uint64)void)", "Method(create_contract(address,address,uint64,asset,uint64[])void)", "Method(fund_escrow(axfer)void)", "Method(get_balance(asset)uint64)", "Method(release_funds(asset)void)"], "stack_out": ["Method(create_contract(address,address,uint64,asset,uint64[])void)", "Method(fund_escrow(axfer)void)", "Method(approve_milestone(uint64)void)", "Method(release_funds(asset)void)", "Method(get_balance(asset)uint64)"]}, "108": {"op": "txna ApplicationArgs 0", "defined_out": ["Method(approve_milestone(uint64)void)", "Method(create_contract(address,address,uint64,asset,uint64[])void)", "Method(fund_escrow(axfer)void)", "Method(get_balance(asset)uint64)", "Method(release_funds(asset)void)", "tmp%2#0"], "stack_out": ["Method(create_contract(address,address,uint64,asset,uint64[])void)", "Method(fund_escrow(axfer)void)", "Method(approve_milestone(uint64)void)", "Method(release_funds(asset)void)", "Method(get_balance(asset)uint64)", "tmp%2#0"]}, "111": {"op": "match main_create_contract_route@3 main_fund_escrow_route@4 main_approve_milestone_route@5 main_release_funds_route@6 main_get_balance_route@7", "stack_out": []}, "123": {"block": "main_after_if_else@14", "stack_in": [], "op": "intc_0 // 0", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "124": {"op": "return", "stack_out": []}, "125": {"block": "main_get_balance_route@7", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%25#0"], "stack_out": ["tmp%25#0"]}, "127": {"op": "!", "defined_out": ["tmp%26#0"], "stack_out": ["tmp%26#0"]}, "128": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "129": {"op": "txn ApplicationID", "defined_out": ["tmp%27#0"], "stack_out": ["tmp%27#0"]}, "131": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "132": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[1]%2#0"], "stack_out": ["reinterpret_bytes[1]%2#0"]}, "135": {"op": "btoi", "defined_out": ["tmp%29#0"], "stack_out": ["tmp%29#0"]}, "136": {"op": "txnas <PERSON>", "defined_out": ["tmp%30#0"], "stack_out": ["tmp%30#0"]}, "138": {"callsub": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.get_balance", "op": "callsub get_balance", "defined_out": ["tmp%31#0"], "stack_out": ["tmp%31#0"]}, "141": {"op": "pushbytes 0x151f7c75", "defined_out": ["0x151f7c75", "tmp%31#0"], "stack_out": ["tmp%31#0", "0x151f7c75"]}, "147": {"op": "swap", "stack_out": ["0x151f7c75", "tmp%31#0"]}, "148": {"op": "concat", "defined_out": ["tmp%32#0"], "stack_out": ["tmp%32#0"]}, "149": {"op": "log", "stack_out": []}, "150": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "151": {"op": "return", "stack_out": []}, "152": {"block": "main_release_funds_route@6", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%19#0"], "stack_out": ["tmp%19#0"]}, "154": {"op": "!", "defined_out": ["tmp%20#0"], "stack_out": ["tmp%20#0"]}, "155": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "156": {"op": "txn ApplicationID", "defined_out": ["tmp%21#0"], "stack_out": ["tmp%21#0"]}, "158": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "159": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[1]%1#0"], "stack_out": ["reinterpret_bytes[1]%1#0"]}, "162": {"op": "btoi", "defined_out": ["tmp%23#0"], "stack_out": ["tmp%23#0"]}, "163": {"op": "txnas <PERSON>", "defined_out": ["tmp%24#0"], "stack_out": ["tmp%24#0"]}, "165": {"callsub": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.release_funds", "op": "callsub release_funds", "stack_out": []}, "168": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "169": {"op": "return", "stack_out": []}, "170": {"block": "main_approve_milestone_route@5", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%15#0"], "stack_out": ["tmp%15#0"]}, "172": {"op": "!", "defined_out": ["tmp%16#0"], "stack_out": ["tmp%16#0"]}, "173": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "174": {"op": "txn ApplicationID", "defined_out": ["tmp%17#0"], "stack_out": ["tmp%17#0"]}, "176": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "177": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[8]%1#0"], "stack_out": ["reinterpret_bytes[8]%1#0"]}, "180": {"callsub": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.approve_milestone", "op": "callsub approve_milestone", "stack_out": []}, "183": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "184": {"op": "return", "stack_out": []}, "185": {"block": "main_fund_escrow_route@4", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%10#0"], "stack_out": ["tmp%10#0"]}, "187": {"op": "!", "defined_out": ["tmp%11#0"], "stack_out": ["tmp%11#0"]}, "188": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "189": {"op": "txn ApplicationID", "defined_out": ["tmp%12#0"], "stack_out": ["tmp%12#0"]}, "191": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "192": {"op": "txn GroupIndex", "defined_out": ["tmp%14#0"], "stack_out": ["tmp%14#0"]}, "194": {"op": "intc_1 // 1", "defined_out": ["1", "tmp%14#0"], "stack_out": ["tmp%14#0", "1"]}, "195": {"op": "-", "defined_out": ["gtxn_idx%0#0"], "stack_out": ["gtxn_idx%0#0"]}, "196": {"op": "dup", "defined_out": ["gtxn_idx%0#0", "gtxn_idx%0#0 (copy)"], "stack_out": ["gtxn_idx%0#0", "gtxn_idx%0#0 (copy)"]}, "197": {"op": "gtxns TypeEnum", "defined_out": ["gtxn_idx%0#0", "gtxn_type%0#0"], "stack_out": ["gtxn_idx%0#0", "gtxn_type%0#0"]}, "199": {"op": "intc_3 // axfer", "defined_out": ["axfer", "gtxn_idx%0#0", "gtxn_type%0#0"], "stack_out": ["gtxn_idx%0#0", "gtxn_type%0#0", "axfer"]}, "200": {"op": "==", "defined_out": ["gtxn_idx%0#0", "gtxn_type_matches%0#0"], "stack_out": ["gtxn_idx%0#0", "gtxn_type_matches%0#0"]}, "201": {"error": "transaction type is axfer", "op": "assert // transaction type is axfer", "stack_out": ["gtxn_idx%0#0"]}, "202": {"callsub": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.fund_escrow", "op": "callsub fund_escrow", "stack_out": []}, "205": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "206": {"op": "return", "stack_out": []}, "207": {"block": "main_create_contract_route@3", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%3#0"], "stack_out": ["tmp%3#0"]}, "209": {"op": "!", "defined_out": ["tmp%4#0"], "stack_out": ["tmp%4#0"]}, "210": {"error": "OnCompletion is not NoOp", "op": "assert // OnCompletion is not NoOp", "stack_out": []}, "211": {"op": "txn ApplicationID", "defined_out": ["tmp%5#0"], "stack_out": ["tmp%5#0"]}, "213": {"error": "can only call when not creating", "op": "assert // can only call when not creating", "stack_out": []}, "214": {"op": "txna ApplicationArgs 1", "defined_out": ["reinterpret_bytes[32]%0#0"], "stack_out": ["reinterpret_bytes[32]%0#0"]}, "217": {"op": "txna ApplicationArgs 2", "defined_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0"], "stack_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0"]}, "220": {"op": "txna ApplicationArgs 3", "defined_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0"], "stack_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0"]}, "223": {"op": "txna ApplicationArgs 4", "defined_out": ["reinterpret_bytes[1]%0#0", "reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0"], "stack_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0", "reinterpret_bytes[1]%0#0"]}, "226": {"op": "btoi", "defined_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0", "tmp%7#0"], "stack_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0", "tmp%7#0"]}, "227": {"op": "txnas <PERSON>", "defined_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0", "tmp%8#0"], "stack_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0", "tmp%8#0"]}, "229": {"op": "txna ApplicationArgs 5", "defined_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0", "tmp%8#0", "tmp%9#0"], "stack_out": ["reinterpret_bytes[32]%0#0", "reinterpret_bytes[32]%1#0", "reinterpret_bytes[8]%0#0", "tmp%8#0", "tmp%9#0"]}, "232": {"callsub": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.create_contract", "op": "callsub create_contract", "stack_out": []}, "235": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "236": {"op": "return", "stack_out": []}, "237": {"block": "main_bare_routing@10", "stack_in": [], "op": "txn OnCompletion", "defined_out": ["tmp%33#0"], "stack_out": ["tmp%33#0"]}, "239": {"op": "bnz main_after_if_else@14", "stack_out": []}, "242": {"op": "txn ApplicationID", "defined_out": ["tmp%34#0"], "stack_out": ["tmp%34#0"]}, "244": {"op": "!", "defined_out": ["tmp%35#0"], "stack_out": ["tmp%35#0"]}, "245": {"error": "can only call when creating", "op": "assert // can only call when creating", "stack_out": []}, "246": {"op": "intc_1 // 1", "defined_out": ["tmp%0#0"], "stack_out": ["tmp%0#0"]}, "247": {"op": "return", "stack_out": []}, "248": {"subroutine": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.create_contract", "params": {"buyer#0": "bytes", "seller#0": "bytes", "total_amount#0": "bytes", "asset#0": "uint64", "milestone_percentages#0": "bytes"}, "block": "create_contract", "stack_in": [], "op": "proto 5 0"}, "251": {"op": "intc_0 // 0", "stack_out": ["milestones#0"]}, "252": {"op": "pushbytess \"\" \"create_contract called\" // \"\", \"create_contract called\"", "defined_out": ["\"create_contract called\""], "stack_out": ["milestones#0", "item_index_internal%1#0", "\"create_contract called\""]}, "278": {"op": "frame_dig -5", "defined_out": ["\"create_contract called\"", "buyer#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "\"create_contract called\"", "buyer#0 (copy)"]}, "280": {"op": "concat", "defined_out": ["tmp%1#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%1#0"]}, "281": {"op": "frame_dig -4", "defined_out": ["seller#0 (copy)", "tmp%1#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%1#0", "seller#0 (copy)"]}, "283": {"op": "concat", "defined_out": ["tmp%3#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%3#0"]}, "284": {"op": "frame_dig -3", "defined_out": ["tmp%3#0", "total_amount#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%3#0", "total_amount#0 (copy)"]}, "286": {"op": "concat", "defined_out": ["tmp%5#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%5#0"]}, "287": {"op": "frame_dig -2", "defined_out": ["asset#0 (copy)", "tmp%5#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%5#0", "asset#0 (copy)"]}, "289": {"op": "itob", "defined_out": ["tmp%5#0", "tmp%7#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%5#0", "tmp%7#0"]}, "290": {"op": "concat", "defined_out": ["tmp%8#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%8#0"]}, "291": {"op": "frame_dig -1", "defined_out": ["milestone_percentages#0 (copy)", "tmp%8#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%8#0", "milestone_percentages#0 (copy)"]}, "293": {"op": "concat", "defined_out": ["tmp%10#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "tmp%10#0"]}, "294": {"op": "log", "stack_out": ["milestones#0", "item_index_internal%1#0"]}, "295": {"op": "intc_0 // 0"}, "296": {"op": "frame_dig -1"}, "298": {"op": "intc_0 // 0", "defined_out": ["0", "milestone_percentages#0 (copy)", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "milestone_percentages#0 (copy)", "0"]}, "299": {"op": "extract_uint16", "defined_out": ["array_length%0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0"]}, "300": {"op": "intc_0 // 0", "defined_out": ["array_length%0#0", "item_index_internal%0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "301": {"block": "create_contract_for_header@1", "stack_in": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"], "op": "frame_dig 4", "defined_out": ["item_index_internal%0#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0"]}, "303": {"op": "frame_dig 3", "defined_out": ["array_length%0#0", "item_index_internal%0#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "array_length%0#0"]}, "305": {"op": "<", "defined_out": ["array_length%0#0", "continue_looping%0#0", "item_index_internal%0#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "continue_looping%0#0"]}, "306": {"op": "bz create_contract_after_for@4", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "309": {"op": "frame_dig -1", "defined_out": ["array_length%0#0", "item_index_internal%0#0", "milestone_percentages#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "milestone_percentages#0 (copy)"]}, "311": {"op": "extract 2 0", "defined_out": ["array_head_and_tail%0#0", "array_length%0#0", "item_index_internal%0#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "array_head_and_tail%0#0"]}, "314": {"op": "frame_dig 4", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "array_head_and_tail%0#0", "item_index_internal%0#0"]}, "316": {"op": "dup", "defined_out": ["array_head_and_tail%0#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "array_head_and_tail%0#0", "item_index_internal%0#0 (copy)", "item_index_internal%0#0 (copy)"]}, "317": {"op": "cover 2", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "array_head_and_tail%0#0", "item_index_internal%0#0 (copy)"]}, "319": {"op": "pushint 8 // 8", "defined_out": ["8", "array_head_and_tail%0#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "array_head_and_tail%0#0", "item_index_internal%0#0 (copy)", "8"]}, "321": {"op": "*", "defined_out": ["array_head_and_tail%0#0", "array_length%0#0", "item_index_internal%0#0", "item_offset%0#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "array_head_and_tail%0#0", "item_offset%0#0"]}, "322": {"op": "extract_uint64", "defined_out": ["array_length%0#0", "item_index_internal%0#0", "tmp%11#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "tmp%11#0"]}, "323": {"op": "frame_dig 2", "defined_out": ["array_length%0#0", "item_index_internal%0#0", "tmp%11#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "tmp%11#0", "total_percent#0"]}, "325": {"op": "+", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "total_percent#0"]}, "326": {"op": "frame_bury 2", "defined_out": ["array_length%0#0", "item_index_internal%0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0"]}, "328": {"op": "intc_1 // 1", "defined_out": ["1", "array_length%0#0", "item_index_internal%0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0", "1"]}, "329": {"op": "+", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%0#0"]}, "330": {"op": "frame_bury 4", "defined_out": ["array_length%0#0", "item_index_internal%0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "332": {"op": "b create_contract_for_header@1"}, "335": {"block": "create_contract_after_for@4", "stack_in": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"], "op": "frame_dig 2", "defined_out": ["total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "total_percent#0"]}, "337": {"op": "pushint 100 // 100", "defined_out": ["100", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "total_percent#0", "100"]}, "339": {"op": "==", "defined_out": ["tmp%12#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%12#0"]}, "340": {"error": "Total percentages must equal 100%", "op": "assert // Total percentages must equal 100%", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "341": {"op": "bytec_3 // \"buyer\"", "defined_out": ["\"buyer\"", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"buyer\""]}, "342": {"op": "frame_dig -5", "defined_out": ["\"buyer\"", "buyer#0 (copy)", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"buyer\"", "buyer#0 (copy)"]}, "344": {"op": "app_global_put", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "345": {"op": "bytec 5 // \"seller\"", "defined_out": ["\"seller\"", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"seller\""]}, "347": {"op": "frame_dig -4", "defined_out": ["\"seller\"", "seller#0 (copy)", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"seller\"", "seller#0 (copy)"]}, "349": {"op": "app_global_put", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "350": {"op": "bytec 6 // \"total_amount\"", "defined_out": ["\"total_amount\"", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"total_amount\""]}, "352": {"op": "frame_dig -3", "defined_out": ["\"total_amount\"", "total_amount#0 (copy)", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"total_amount\"", "total_amount#0 (copy)"]}, "354": {"op": "app_global_put", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "355": {"op": "bytec_1 // \"released\"", "defined_out": ["\"released\"", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"released\""]}, "356": {"op": "bytec 7 // 0x0000000000000000", "defined_out": ["\"released\"", "0x0000000000000000", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"released\"", "0x0000000000000000"]}, "358": {"op": "app_global_put", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "359": {"op": "bytec_2 // \"withdrawn\"", "defined_out": ["\"withdrawn\"", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"withdrawn\""]}, "360": {"op": "bytec 7 // 0x0000000000000000", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"withdrawn\"", "0x0000000000000000"]}, "362": {"op": "app_global_put", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "363": {"op": "intc_0 // 0", "defined_out": ["0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "0"]}, "364": {"op": "bytec_3 // \"buyer\"", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "0", "\"buyer\""]}, "365": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "maybe_value%0#0", "maybe_exists%0#0"]}, "366": {"error": "check self.buyer exists", "op": "assert // check self.buyer exists", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "maybe_value%0#0"]}, "367": {"op": "pushbytes \"State after storing contract data\"", "defined_out": ["\"State after storing contract data\"", "maybe_value%0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "maybe_value%0#0", "\"State after storing contract data\""]}, "402": {"op": "swap", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"State after storing contract data\"", "maybe_value%0#0"]}, "403": {"op": "concat", "defined_out": ["tmp%14#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%14#0"]}, "404": {"op": "intc_0 // 0", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%14#0", "0"]}, "405": {"op": "bytec 5 // \"seller\"", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%14#0", "0", "\"seller\""]}, "407": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%1#0", "maybe_value%1#0", "tmp%14#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%14#0", "maybe_value%1#0", "maybe_exists%1#0"]}, "408": {"error": "check self.seller exists", "op": "assert // check self.seller exists", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%14#0", "maybe_value%1#0"]}, "409": {"op": "concat", "defined_out": ["tmp%16#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%16#0"]}, "410": {"op": "intc_0 // 0", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%16#0", "0"]}, "411": {"op": "bytec 6 // \"total_amount\"", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%16#0", "0", "\"total_amount\""]}, "413": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%2#0", "maybe_value%2#0", "tmp%16#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%16#0", "maybe_value%2#0", "maybe_exists%2#0"]}, "414": {"error": "check self.total_amount exists", "op": "assert // check self.total_amount exists", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%16#0", "maybe_value%2#0"]}, "415": {"op": "concat", "defined_out": ["tmp%18#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%18#0"]}, "416": {"op": "intc_0 // 0", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%18#0", "0"]}, "417": {"op": "bytec_1 // \"released\"", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%18#0", "0", "\"released\""]}, "418": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%3#0", "maybe_value%3#0", "tmp%18#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%18#0", "maybe_value%3#0", "maybe_exists%3#0"]}, "419": {"error": "check self.released exists", "op": "assert // check self.released exists", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%18#0", "maybe_value%3#0"]}, "420": {"op": "concat", "defined_out": ["tmp%20#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%20#0"]}, "421": {"op": "intc_0 // 0", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%20#0", "0"]}, "422": {"op": "bytec_2 // \"withdrawn\"", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%20#0", "0", "\"withdrawn\""]}, "423": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%4#0", "maybe_value%4#0", "tmp%20#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%20#0", "maybe_value%4#0", "maybe_exists%4#0"]}, "424": {"error": "check self.withdrawn exists", "op": "assert // check self.withdrawn exists", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%20#0", "maybe_value%4#0"]}, "425": {"op": "concat", "defined_out": ["tmp%22#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%22#0"]}, "426": {"op": "log", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "427": {"op": "itxn_begin"}, "428": {"op": "global MinTxnFee", "defined_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0"]}, "430": {"op": "global CurrentApplicationAddress", "defined_out": ["inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0"]}, "432": {"op": "intc_0 // 0", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "0"]}, "433": {"op": "itxn_field AssetAmount", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0"]}, "435": {"op": "frame_dig -2", "defined_out": ["asset#0 (copy)", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0", "asset#0 (copy)"]}, "437": {"op": "itxn_field XferAsset", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "inner_txn_params%0%%param_AssetReceiver_idx_0#0"]}, "439": {"op": "itxn_field AssetReceiver", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0"]}, "441": {"op": "intc_3 // axfer", "defined_out": ["axfer", "inner_txn_params%0%%param_Fee_idx_0#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0", "axfer"]}, "442": {"op": "itxn_field TypeEnum", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "inner_txn_params%0%%param_Fee_idx_0#0"]}, "444": {"op": "itxn_field Fee", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "446": {"op": "itxn_submit"}, "447": {"op": "pushbytes 0x0000", "defined_out": ["milestones#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "milestones#0"]}, "451": {"op": "frame_bury 0", "defined_out": ["milestones#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "453": {"op": "intc_0 // 0", "defined_out": ["item_index_internal%1#0", "milestones#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0"]}, "454": {"op": "frame_bury 1", "defined_out": ["item_index_internal%1#0", "milestones#0", "total_percent#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "456": {"block": "create_contract_for_header@6", "stack_in": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"], "op": "frame_dig 1", "defined_out": ["item_index_internal%1#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0"]}, "458": {"op": "frame_dig 3", "defined_out": ["array_length%0#0", "item_index_internal%1#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "array_length%0#0"]}, "460": {"op": "<", "defined_out": ["array_length%0#0", "continue_looping%1#0", "item_index_internal%1#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "continue_looping%1#0"]}, "461": {"op": "bz create_contract_after_for@9", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "464": {"op": "frame_dig -1", "defined_out": ["array_length%0#0", "item_index_internal%1#0", "milestone_percentages#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "milestone_percentages#0 (copy)"]}, "466": {"op": "extract 2 0", "defined_out": ["array_head_and_tail%1#0", "array_length%0#0", "item_index_internal%1#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "array_head_and_tail%1#0"]}, "469": {"op": "frame_dig 1", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "array_head_and_tail%1#0", "item_index_internal%1#0"]}, "471": {"op": "dup", "defined_out": ["array_head_and_tail%1#0", "array_length%0#0", "item_index_internal%1#0", "item_index_internal%1#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "array_head_and_tail%1#0", "item_index_internal%1#0 (copy)", "item_index_internal%1#0 (copy)"]}, "472": {"op": "cover 2", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "array_head_and_tail%1#0", "item_index_internal%1#0 (copy)"]}, "474": {"op": "pushint 8 // 8", "defined_out": ["8", "array_head_and_tail%1#0", "array_length%0#0", "item_index_internal%1#0", "item_index_internal%1#0 (copy)"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "array_head_and_tail%1#0", "item_index_internal%1#0 (copy)", "8"]}, "476": {"op": "*", "defined_out": ["array_head_and_tail%1#0", "array_length%0#0", "item_index_internal%1#0", "item_offset%1#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "array_head_and_tail%1#0", "item_offset%1#0"]}, "477": {"op": "pushint 8 // 8", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "array_head_and_tail%1#0", "item_offset%1#0", "8"]}, "479": {"error": "Index access is out of bounds", "op": "extract3 // on error: Index access is out of bounds", "defined_out": ["array_length%0#0", "item_index_internal%1#0", "percentage#1"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "percentage#1"]}, "480": {"op": "frame_dig 0", "defined_out": ["array_length%0#0", "item_index_internal%1#0", "milestones#0", "percentage#1"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "percentage#1", "milestones#0"]}, "482": {"op": "extract 2 0", "defined_out": ["array_length%0#0", "expr_value_trimmed%0#0", "item_index_internal%1#0", "milestones#0", "percentage#1"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "percentage#1", "expr_value_trimmed%0#0"]}, "485": {"op": "swap", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "expr_value_trimmed%0#0", "percentage#1"]}, "486": {"op": "bytec_0 // 0x00", "defined_out": ["0x00", "array_length%0#0", "expr_value_trimmed%0#0", "item_index_internal%1#0", "milestones#0", "percentage#1"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "expr_value_trimmed%0#0", "percentage#1", "0x00"]}, "487": {"op": "concat", "defined_out": ["array_length%0#0", "encoded_tuple_buffer%2#0", "expr_value_trimmed%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "expr_value_trimmed%0#0", "encoded_tuple_buffer%2#0"]}, "488": {"op": "intc_2 // 65", "defined_out": ["65", "array_length%0#0", "encoded_tuple_buffer%2#0", "expr_value_trimmed%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "expr_value_trimmed%0#0", "encoded_tuple_buffer%2#0", "65"]}, "489": {"op": "intc_0 // 0", "defined_out": ["0", "65", "array_length%0#0", "encoded_tuple_buffer%2#0", "expr_value_trimmed%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "expr_value_trimmed%0#0", "encoded_tuple_buffer%2#0", "65", "0"]}, "490": {"op": "setbit", "defined_out": ["array_length%0#0", "encoded_tuple_buffer%3#0", "expr_value_trimmed%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "expr_value_trimmed%0#0", "encoded_tuple_buffer%3#0"]}, "491": {"op": "concat", "defined_out": ["array_length%0#0", "concatenated%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "concatenated%0#0"]}, "492": {"op": "dup", "defined_out": ["array_length%0#0", "concatenated%0#0", "concatenated%0#0 (copy)", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "concatenated%0#0", "concatenated%0#0 (copy)"]}, "493": {"op": "len", "defined_out": ["array_length%0#0", "byte_len%0#0", "concatenated%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "concatenated%0#0", "byte_len%0#0"]}, "494": {"op": "pushint 9 // 9", "defined_out": ["9", "array_length%0#0", "byte_len%0#0", "concatenated%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "concatenated%0#0", "byte_len%0#0", "9"]}, "496": {"op": "/", "defined_out": ["array_length%0#0", "concatenated%0#0", "item_index_internal%1#0", "len_%0#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "concatenated%0#0", "len_%0#0"]}, "497": {"op": "itob", "defined_out": ["array_length%0#0", "as_bytes%0#0", "concatenated%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "concatenated%0#0", "as_bytes%0#0"]}, "498": {"op": "extract 6 2", "defined_out": ["array_length%0#0", "concatenated%0#0", "item_index_internal%1#0", "len_16_bit%0#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "concatenated%0#0", "len_16_bit%0#0"]}, "501": {"op": "swap", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "len_16_bit%0#0", "concatenated%0#0"]}, "502": {"op": "concat", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "milestones#0"]}, "503": {"op": "frame_bury 0", "defined_out": ["array_length%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0"]}, "505": {"op": "intc_1 // 1", "defined_out": ["1", "array_length%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0", "1"]}, "506": {"op": "+", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "item_index_internal%1#0"]}, "507": {"op": "frame_bury 1", "defined_out": ["array_length%0#0", "item_index_internal%1#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "509": {"op": "b create_contract_for_header@6"}, "512": {"block": "create_contract_after_for@9", "stack_in": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"], "op": "bytec 4 // \"milestones\"", "defined_out": ["\"milestones\""], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"milestones\""]}, "514": {"op": "frame_dig 0", "defined_out": ["\"milestones\"", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"milestones\"", "milestones#0"]}, "516": {"op": "app_global_put", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "517": {"op": "intc_0 // 0", "defined_out": ["0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "0"]}, "518": {"op": "bytec 4 // \"milestones\"", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "0", "\"milestones\""]}, "520": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%5#0", "maybe_value%5#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "maybe_value%5#0", "maybe_exists%5#0"]}, "521": {"error": "check self.milestones exists", "op": "assert // check self.milestones exists", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "maybe_value%5#0"]}, "522": {"op": "pushbytes \"Milestones after storing\"", "defined_out": ["\"Milestones after storing\"", "maybe_value%5#0", "milestones#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "maybe_value%5#0", "\"Milestones after storing\""]}, "548": {"op": "swap", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "\"Milestones after storing\"", "maybe_value%5#0"]}, "549": {"op": "concat", "defined_out": ["milestones#0", "tmp%24#0"], "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0", "tmp%24#0"]}, "550": {"op": "log", "stack_out": ["milestones#0", "item_index_internal%1#0", "total_percent#0", "array_length%0#0", "item_index_internal%0#0"]}, "551": {"retsub": true, "op": "retsub"}, "552": {"subroutine": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.fund_escrow", "params": {"payment#0": "uint64"}, "block": "fund_escrow", "stack_in": [], "op": "proto 1 0"}, "555": {"op": "frame_dig -1", "defined_out": ["payment#0 (copy)"], "stack_out": ["payment#0 (copy)"]}, "557": {"op": "gtxns AssetSender", "defined_out": ["tmp%1#0"], "stack_out": ["tmp%1#0"]}, "559": {"op": "pushbytes \"fund_escrow called\"", "defined_out": ["\"fund_escrow called\"", "tmp%1#0"], "stack_out": ["tmp%1#0", "\"fund_escrow called\""]}, "579": {"op": "dig 1", "defined_out": ["\"fund_escrow called\"", "tmp%1#0", "tmp%1#0 (copy)"], "stack_out": ["tmp%1#0", "\"fund_escrow called\"", "tmp%1#0 (copy)"]}, "581": {"op": "concat", "defined_out": ["tmp%1#0", "tmp%2#0"], "stack_out": ["tmp%1#0", "tmp%2#0"]}, "582": {"op": "frame_dig -1", "stack_out": ["tmp%1#0", "tmp%2#0", "payment#0 (copy)"]}, "584": {"op": "gtxns AssetReceiver", "defined_out": ["tmp%1#0", "tmp%2#0", "tmp%4#0"], "stack_out": ["tmp%1#0", "tmp%2#0", "tmp%4#0"]}, "586": {"op": "swap", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%2#0"]}, "587": {"op": "dig 1", "defined_out": ["tmp%1#0", "tmp%2#0", "tmp%4#0", "tmp%4#0 (copy)"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%2#0", "tmp%4#0 (copy)"]}, "589": {"op": "concat", "defined_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0"]}, "590": {"op": "frame_dig -1", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "payment#0 (copy)"]}, "592": {"op": "gtxns AssetAmount", "defined_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "tmp%7#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "tmp%7#0"]}, "594": {"op": "dup", "defined_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "tmp%7#0", "tmp%7#0 (copy)"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "tmp%7#0", "tmp%7#0 (copy)"]}, "595": {"op": "itob", "defined_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "tmp%7#0", "tmp%8#0"]}, "596": {"op": "uncover 2", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%5#0"]}, "598": {"op": "dig 1", "defined_out": ["tmp%1#0", "tmp%4#0", "tmp%5#0", "tmp%7#0", "tmp%8#0", "tmp%8#0 (copy)"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%5#0", "tmp%8#0 (copy)"]}, "600": {"op": "concat", "defined_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%9#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%9#0"]}, "601": {"op": "log", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"]}, "602": {"op": "txn GroupIndex", "defined_out": ["tmp%1#0", "tmp%10#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%10#0"]}, "604": {"op": "intc_1 // 1", "defined_out": ["1", "tmp%1#0", "tmp%10#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%10#0", "1"]}, "605": {"op": "==", "defined_out": ["tmp%1#0", "tmp%11#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%11#0"]}, "606": {"error": "Must be second transaction in group", "op": "assert // Must be second transaction in group", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"]}, "607": {"op": "intc_0 // 0", "defined_out": ["0", "tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "0"]}, "608": {"op": "bytec_3 // \"buyer\"", "defined_out": ["\"buyer\"", "0", "tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "0", "\"buyer\""]}, "609": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0", "tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "maybe_value%0#0", "maybe_exists%0#0"]}, "610": {"error": "check self.buyer exists", "op": "assert // check self.buyer exists", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "maybe_value%0#0"]}, "611": {"op": "dig 4", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "maybe_value%0#0", "tmp%1#0 (copy)"]}, "613": {"op": "==", "defined_out": ["tmp%1#0", "tmp%13#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%13#0"]}, "614": {"error": "Invalid buyer", "op": "assert // Invalid buyer", "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"]}, "615": {"op": "global CurrentApplicationAddress", "defined_out": ["tmp%1#0", "tmp%15#0", "tmp%4#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%4#0", "tmp%7#0", "tmp%8#0", "tmp%15#0"]}, "617": {"op": "uncover 3", "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0", "tmp%15#0", "tmp%4#0"]}, "619": {"op": "==", "defined_out": ["tmp%1#0", "tmp%16#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0", "tmp%16#0"]}, "620": {"error": "Invalid receiver", "op": "assert // Invalid receiver", "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0"]}, "621": {"op": "intc_0 // 0", "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0", "0"]}, "622": {"op": "bytec 6 // \"total_amount\"", "defined_out": ["\"total_amount\"", "0", "tmp%1#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0", "0", "\"total_amount\""]}, "624": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%1#0", "maybe_value%1#0", "tmp%1#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0", "maybe_value%1#0", "maybe_exists%1#0"]}, "625": {"error": "check self.total_amount exists", "op": "assert // check self.total_amount exists", "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0", "maybe_value%1#0"]}, "626": {"op": "btoi", "defined_out": ["tmp%1#0", "tmp%18#0", "tmp%7#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%7#0", "tmp%8#0", "tmp%18#0"]}, "627": {"op": "uncover 2", "stack_out": ["tmp%1#0", "tmp%8#0", "tmp%18#0", "tmp%7#0"]}, "629": {"op": "==", "defined_out": ["tmp%1#0", "tmp%19#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%8#0", "tmp%19#0"]}, "630": {"error": "Incorrect amount", "op": "assert // Incorrect amount", "stack_out": ["tmp%1#0", "tmp%8#0"]}, "631": {"op": "pushbytes \"Escrow funded\"", "defined_out": ["\"Escrow funded\"", "tmp%1#0", "tmp%8#0"], "stack_out": ["tmp%1#0", "tmp%8#0", "\"Escrow funded\""]}, "646": {"op": "uncover 2", "stack_out": ["tmp%8#0", "\"Escrow funded\"", "tmp%1#0"]}, "648": {"op": "concat", "defined_out": ["tmp%22#0", "tmp%8#0"], "stack_out": ["tmp%8#0", "tmp%22#0"]}, "649": {"op": "swap", "stack_out": ["tmp%22#0", "tmp%8#0"]}, "650": {"op": "concat", "defined_out": ["tmp%26#0"], "stack_out": ["tmp%26#0"]}, "651": {"op": "log", "stack_out": []}, "652": {"retsub": true, "op": "retsub"}, "653": {"subroutine": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.approve_milestone", "params": {"milestone_index#0": "bytes"}, "block": "approve_milestone", "stack_in": [], "op": "proto 1 0"}, "656": {"op": "pushbytes \"approve_milestone called\"", "defined_out": ["\"approve_milestone called\""], "stack_out": ["\"approve_milestone called\""]}, "682": {"op": "frame_dig -1", "defined_out": ["\"approve_milestone called\"", "milestone_index#0 (copy)"], "stack_out": ["\"approve_milestone called\"", "milestone_index#0 (copy)"]}, "684": {"op": "concat", "defined_out": ["tmp%1#0"], "stack_out": ["tmp%1#0"]}, "685": {"op": "txn Sender", "defined_out": ["tmp%1#0", "tmp%3#0"], "stack_out": ["tmp%1#0", "tmp%3#0"]}, "687": {"op": "concat", "defined_out": ["tmp%4#0"], "stack_out": ["tmp%4#0"]}, "688": {"op": "log", "stack_out": []}, "689": {"op": "frame_dig -1", "stack_out": ["milestone_index#0 (copy)"]}, "691": {"op": "btoi", "defined_out": ["idx#0"], "stack_out": ["idx#0"]}, "692": {"op": "dup", "defined_out": ["idx#0"], "stack_out": ["idx#0", "idx#0"]}, "693": {"op": "intc_0 // 0", "stack_out": ["idx#0", "idx#0", "0"]}, "694": {"op": "bytec 4 // \"milestones\"", "defined_out": ["\"milestones\"", "0", "idx#0"], "stack_out": ["idx#0", "idx#0", "0", "\"milestones\""]}, "696": {"op": "app_global_get_ex", "defined_out": ["idx#0", "maybe_exists%0#0", "milestones#0"], "stack_out": ["idx#0", "idx#0", "milestones#0", "maybe_exists%0#0"]}, "697": {"op": "swap", "stack_out": ["idx#0", "idx#0", "maybe_exists%0#0", "milestones#0"]}, "698": {"op": "dup", "stack_out": ["idx#0", "idx#0", "maybe_exists%0#0", "milestones#0", "milestones#0 (copy)"]}, "699": {"op": "cover 2", "stack_out": ["idx#0", "idx#0", "milestones#0", "maybe_exists%0#0", "milestones#0"]}, "701": {"op": "cover 3", "defined_out": ["idx#0", "maybe_exists%0#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0", "maybe_exists%0#0"]}, "703": {"error": "check self.milestones exists", "op": "assert // check self.milestones exists", "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0"]}, "704": {"op": "dup", "defined_out": ["idx#0", "milestones#0", "milestones#0 (copy)"], "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0", "milestones#0 (copy)"]}, "705": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0", "milestones#0 (copy)", "0"]}, "706": {"op": "extract_uint16", "defined_out": ["idx#0", "milestones#0", "tmp%5#0"], "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0", "tmp%5#0"]}, "707": {"op": "dig 2", "defined_out": ["idx#0", "idx#0 (copy)", "milestones#0", "tmp%5#0"], "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0", "tmp%5#0", "idx#0 (copy)"]}, "709": {"op": ">", "defined_out": ["idx#0", "milestones#0", "tmp%6#0"], "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0", "tmp%6#0"]}, "710": {"error": "Invalid milestone index", "op": "assert // Invalid milestone index", "stack_out": ["idx#0", "milestones#0", "idx#0", "milestones#0"]}, "711": {"op": "extract 2 0", "defined_out": ["array_head_and_tail%0#0", "idx#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "idx#0", "array_head_and_tail%0#0"]}, "714": {"op": "swap", "stack_out": ["idx#0", "milestones#0", "array_head_and_tail%0#0", "idx#0"]}, "715": {"op": "pushint 9 // 9", "defined_out": ["9", "array_head_and_tail%0#0", "idx#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "array_head_and_tail%0#0", "idx#0", "9"]}, "717": {"op": "*", "defined_out": ["array_head_and_tail%0#0", "idx#0", "item_offset%0#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "array_head_and_tail%0#0", "item_offset%0#0"]}, "718": {"op": "dup", "stack_out": ["idx#0", "milestones#0", "array_head_and_tail%0#0", "item_offset%0#0", "item_offset%0#0"]}, "719": {"op": "cover 2", "defined_out": ["array_head_and_tail%0#0", "idx#0", "item_offset%0#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "array_head_and_tail%0#0", "item_offset%0#0"]}, "721": {"op": "pushint 9 // 9", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "array_head_and_tail%0#0", "item_offset%0#0", "9"]}, "723": {"error": "Index access is out of bounds", "op": "extract3 // on error: Index access is out of bounds", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "724": {"op": "dup", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "725": {"op": "pushint 64 // 64", "defined_out": ["64", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "64"]}, "727": {"op": "getbit", "defined_out": ["idx#0", "is_true%0#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%0#0"]}, "728": {"op": "bytec_0 // 0x00", "defined_out": ["0x00", "idx#0", "is_true%0#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%0#0", "0x00"]}, "729": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%0#0", "0x00", "0"]}, "730": {"op": "uncover 2", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "0x00", "0", "is_true%0#0"]}, "732": {"op": "setbit", "defined_out": ["encoded_bool%0#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%0#0"]}, "733": {"op": "bytec_0 // 0x00", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%0#0", "0x00"]}, "734": {"op": "!=", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%8#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%8#0"]}, "735": {"op": "bz approve_milestone_bool_false@3", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "738": {"op": "frame_dig 3", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "740": {"op": "intc_2 // 65", "defined_out": ["65", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "65"]}, "741": {"op": "getbit", "defined_out": ["idx#0", "is_true%1#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%1#0"]}, "742": {"op": "bytec_0 // 0x00", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%1#0", "0x00"]}, "743": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%1#0", "0x00", "0"]}, "744": {"op": "uncover 2", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "0x00", "0", "is_true%1#0"]}, "746": {"op": "setbit", "defined_out": ["encoded_bool%1#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%1#0"]}, "747": {"op": "bytec_0 // 0x00", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%1#0", "0x00"]}, "748": {"op": "!=", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%9#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%9#0"]}, "749": {"op": "bz approve_milestone_bool_false@3", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "752": {"op": "intc_1 // 1", "defined_out": ["and_result%0#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "and_result%0#0"]}, "753": {"block": "approve_milestone_bool_merge@4", "stack_in": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "and_result%0#0"], "op": "!", "defined_out": ["tmp%10#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%10#0"]}, "754": {"error": "Already approved", "op": "assert // Already approved", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "755": {"op": "txn Sender", "defined_out": ["tmp%11#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%11#0"]}, "757": {"op": "intc_0 // 0", "defined_out": ["0", "tmp%11#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%11#0", "0"]}, "758": {"op": "bytec_3 // \"buyer\"", "defined_out": ["\"buyer\"", "0", "tmp%11#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%11#0", "0", "\"buyer\""]}, "759": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%1#0", "maybe_value%1#0", "tmp%11#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%11#0", "maybe_value%1#0", "maybe_exists%1#0"]}, "760": {"error": "check self.buyer exists", "op": "assert // check self.buyer exists", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%11#0", "maybe_value%1#0"]}, "761": {"op": "==", "defined_out": ["tmp%12#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%12#0"]}, "762": {"error": "Unauthorized", "op": "assert // Unauthorized", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "763": {"op": "frame_dig 3", "defined_out": ["milestone#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "765": {"op": "pushint 64 // 64", "defined_out": ["64", "milestone#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "64"]}, "767": {"op": "intc_1 // 1", "defined_out": ["1", "64", "milestone#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "64", "1"]}, "768": {"op": "setbit", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "769": {"op": "intc_2 // 65", "defined_out": ["65", "milestone#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "65"]}, "770": {"op": "intc_1 // 1", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "65", "1"]}, "771": {"op": "setbit", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "772": {"op": "dup", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "milestone#0"]}, "773": {"op": "frame_bury 3", "defined_out": ["milestone#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "775": {"op": "frame_dig 2", "defined_out": ["item_offset%0#0", "milestone#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "item_offset%0#0"]}, "777": {"op": "pushint 2 // 2", "defined_out": ["2", "item_offset%0#0", "milestone#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "item_offset%0#0", "2"]}, "779": {"op": "+", "defined_out": ["item_offset%0#0", "milestone#0", "write_offset%0#1"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "write_offset%0#1"]}, "780": {"op": "frame_dig 1", "defined_out": ["item_offset%0#0", "milestone#0", "milestones#0", "write_offset%0#1"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "write_offset%0#1", "milestones#0"]}, "782": {"op": "swap", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "milestones#0", "write_offset%0#1"]}, "783": {"op": "dig 2", "defined_out": ["item_offset%0#0", "milestone#0", "milestone#0 (copy)", "milestones#0", "write_offset%0#1"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "milestones#0", "write_offset%0#1", "milestone#0 (copy)"]}, "785": {"op": "replace3", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "milestones#0"]}, "786": {"op": "bytec 4 // \"milestones\"", "defined_out": ["\"milestones\"", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "milestones#0", "\"milestones\""]}, "788": {"op": "swap", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "\"milestones\"", "milestones#0"]}, "789": {"op": "app_global_put", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "790": {"op": "frame_dig 0", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "idx#0"]}, "792": {"op": "itob", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%14#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%14#0"]}, "793": {"op": "pushbytes \"Milestone after approval\"", "defined_out": ["\"Milestone after approval\"", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%14#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%14#0", "\"Milestone after approval\""]}, "819": {"op": "swap", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "\"Milestone after approval\"", "tmp%14#0"]}, "820": {"op": "concat", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%15#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%15#0"]}, "821": {"op": "dig 1", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%15#0", "milestone#0 (copy)"]}, "823": {"op": "concat", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%17#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%17#0"]}, "824": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%17#0", "0"]}, "825": {"op": "bytec 4 // \"milestones\"", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%17#0", "0", "\"milestones\""]}, "827": {"op": "app_global_get_ex", "defined_out": ["idx#0", "item_offset%0#0", "maybe_exists%2#0", "maybe_value%2#0", "milestone#0", "milestones#0", "tmp%17#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%17#0", "maybe_value%2#0", "maybe_exists%2#0"]}, "828": {"error": "check self.milestones exists", "op": "assert // check self.milestones exists", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%17#0", "maybe_value%2#0"]}, "829": {"op": "concat", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%19#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "tmp%19#0"]}, "830": {"op": "log", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "831": {"op": "pushint 64 // 64", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "64"]}, "833": {"op": "getbit", "defined_out": ["idx#0", "is_true%4#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%4#0"]}, "834": {"op": "bytec_0 // 0x00", "defined_out": ["0x00", "idx#0", "is_true%4#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%4#0", "0x00"]}, "835": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%4#0", "0x00", "0"]}, "836": {"op": "uncover 2", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "0x00", "0", "is_true%4#0"]}, "838": {"op": "setbit", "defined_out": ["encoded_bool%4#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%4#0"]}, "839": {"op": "bytec_0 // 0x00", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%4#0", "0x00"]}, "840": {"op": "!=", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%20#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%20#0"]}, "841": {"op": "bz approve_milestone_after_if_else@10", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "844": {"op": "frame_dig 3", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "846": {"op": "intc_2 // 65", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "65"]}, "847": {"op": "getbit", "defined_out": ["idx#0", "is_true%5#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%5#0"]}, "848": {"op": "bytec_0 // 0x00", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%5#0", "0x00"]}, "849": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "is_true%5#0", "0x00", "0"]}, "850": {"op": "uncover 2", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "0x00", "0", "is_true%5#0"]}, "852": {"op": "setbit", "defined_out": ["encoded_bool%5#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%5#0"]}, "853": {"op": "bytec_0 // 0x00", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "encoded_bool%5#0", "0x00"]}, "854": {"op": "!=", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%21#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%21#0"]}, "855": {"op": "bz approve_milestone_after_if_else@10", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "858": {"op": "frame_dig 3", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0"]}, "860": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "milestone#0", "0"]}, "861": {"op": "extract_uint64", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "percentage#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0"]}, "862": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "0"]}, "863": {"op": "bytec_1 // \"released\"", "defined_out": ["\"released\"", "0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "percentage#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "0", "\"released\""]}, "864": {"op": "app_global_get_ex", "defined_out": ["idx#0", "item_offset%0#0", "maybe_exists%3#0", "maybe_value%3#0", "milestone#0", "milestones#0", "percentage#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "maybe_value%3#0", "maybe_exists%3#0"]}, "865": {"error": "check self.released exists", "op": "assert // check self.released exists", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "maybe_value%3#0"]}, "866": {"op": "btoi", "defined_out": ["current_released#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "percentage#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "current_released#0"]}, "867": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "current_released#0", "0"]}, "868": {"op": "bytec 6 // \"total_amount\"", "defined_out": ["\"total_amount\"", "0", "current_released#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "percentage#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "current_released#0", "0", "\"total_amount\""]}, "870": {"op": "app_global_get_ex", "defined_out": ["current_released#0", "idx#0", "item_offset%0#0", "maybe_exists%4#0", "maybe_value%4#0", "milestone#0", "milestones#0", "percentage#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "current_released#0", "maybe_value%4#0", "maybe_exists%4#0"]}, "871": {"error": "check self.total_amount exists", "op": "assert // check self.total_amount exists", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "current_released#0", "maybe_value%4#0"]}, "872": {"op": "btoi", "defined_out": ["current_released#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "percentage#0", "tmp%23#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "percentage#0", "current_released#0", "tmp%23#0"]}, "873": {"op": "uncover 2", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "current_released#0", "tmp%23#0", "percentage#0"]}, "875": {"op": "*", "defined_out": ["current_released#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%24#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "current_released#0", "tmp%24#0"]}, "876": {"op": "pushint 100 // 100", "defined_out": ["100", "current_released#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%24#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "current_released#0", "tmp%24#0", "100"]}, "878": {"op": "/", "defined_out": ["additional#0", "current_released#0", "idx#0", "item_offset%0#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "current_released#0", "additional#0"]}, "879": {"op": "+", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "to_encode%0#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "to_encode%0#0"]}, "880": {"op": "itob", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "val_as_bytes%0#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "val_as_bytes%0#0"]}, "881": {"op": "bytec_1 // \"released\"", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "val_as_bytes%0#0", "\"released\""]}, "882": {"op": "swap", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "\"released\"", "val_as_bytes%0#0"]}, "883": {"op": "app_global_put", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "884": {"op": "intc_0 // 0", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "0"]}, "885": {"op": "bytec_1 // \"released\"", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "0", "\"released\""]}, "886": {"op": "app_global_get_ex", "defined_out": ["idx#0", "item_offset%0#0", "maybe_exists%5#0", "maybe_value%5#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "maybe_value%5#0", "maybe_exists%5#0"]}, "887": {"error": "check self.released exists", "op": "assert // check self.released exists", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "maybe_value%5#0"]}, "888": {"op": "pushbytes \"Released updated\"", "defined_out": ["\"Released updated\"", "idx#0", "item_offset%0#0", "maybe_value%5#0", "milestone#0", "milestones#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "maybe_value%5#0", "\"Released updated\""]}, "906": {"op": "swap", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "\"Released updated\"", "maybe_value%5#0"]}, "907": {"op": "concat", "defined_out": ["idx#0", "item_offset%0#0", "milestone#0", "milestones#0", "tmp%26#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "tmp%26#0"]}, "908": {"op": "log", "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"]}, "909": {"block": "approve_milestone_after_if_else@10", "stack_in": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"], "retsub": true, "op": "retsub"}, "910": {"block": "approve_milestone_bool_false@3", "stack_in": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0"], "op": "intc_0 // 0", "defined_out": ["and_result%0#0"], "stack_out": ["idx#0", "milestones#0", "item_offset%0#0", "milestone#0", "and_result%0#0"]}, "911": {"op": "b approve_milestone_bool_merge@4"}, "914": {"subroutine": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.release_funds", "params": {"asset#0": "uint64"}, "block": "release_funds", "stack_in": [], "op": "proto 1 0"}, "917": {"op": "frame_dig -1", "defined_out": ["asset#0 (copy)"], "stack_out": ["asset#0 (copy)"]}, "919": {"op": "itob", "defined_out": ["tmp%1#0"], "stack_out": ["tmp%1#0"]}, "920": {"op": "pushbytes \"release_funds called\"", "defined_out": ["\"release_funds called\"", "tmp%1#0"], "stack_out": ["tmp%1#0", "\"release_funds called\""]}, "942": {"op": "swap", "stack_out": ["\"release_funds called\"", "tmp%1#0"]}, "943": {"op": "concat", "defined_out": ["tmp%2#0"], "stack_out": ["tmp%2#0"]}, "944": {"op": "txn Sender", "defined_out": ["tmp%2#0", "tmp%4#0"], "stack_out": ["tmp%2#0", "tmp%4#0"]}, "946": {"op": "concat", "defined_out": ["tmp%5#0"], "stack_out": ["tmp%5#0"]}, "947": {"op": "log", "stack_out": []}, "948": {"op": "txn Sender", "defined_out": ["tmp%6#0"], "stack_out": ["tmp%6#0"]}, "950": {"op": "intc_0 // 0", "defined_out": ["0", "tmp%6#0"], "stack_out": ["tmp%6#0", "0"]}, "951": {"op": "bytec_3 // \"buyer\"", "defined_out": ["\"buyer\"", "0", "tmp%6#0"], "stack_out": ["tmp%6#0", "0", "\"buyer\""]}, "952": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%0#0", "maybe_value%0#0", "tmp%6#0"], "stack_out": ["tmp%6#0", "maybe_value%0#0", "maybe_exists%0#0"]}, "953": {"error": "check self.buyer exists", "op": "assert // check self.buyer exists", "stack_out": ["tmp%6#0", "maybe_value%0#0"]}, "954": {"op": "!=", "defined_out": ["tmp%7#0"], "stack_out": ["tmp%7#0"]}, "955": {"op": "bz release_funds_after_if_else@2", "stack_out": []}, "958": {"op": "pushbytes \"Withdraw failed: Only buyer can release\"", "defined_out": ["\"Withdraw failed: Only buyer can release\""], "stack_out": ["\"Withdraw failed: Only buyer can release\""]}, "999": {"op": "txn Sender", "defined_out": ["\"Withdraw failed: Only buyer can release\"", "tmp%9#0"], "stack_out": ["\"Withdraw failed: Only buyer can release\"", "tmp%9#0"]}, "1001": {"op": "concat", "defined_out": ["tmp%10#0"], "stack_out": ["tmp%10#0"]}, "1002": {"op": "log", "stack_out": []}, "1003": {"error": "Only buyer can release", "op": "err // Only buyer can release"}, "1004": {"block": "release_funds_after_if_else@2", "stack_in": [], "op": "intc_0 // 0", "defined_out": ["0"], "stack_out": ["0"]}, "1005": {"op": "bytec_1 // \"released\"", "defined_out": ["\"released\"", "0"], "stack_out": ["0", "\"released\""]}, "1006": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%1#0", "maybe_value%1#0"], "stack_out": ["maybe_value%1#0", "maybe_exists%1#0"]}, "1007": {"error": "check self.released exists", "op": "assert // check self.released exists", "stack_out": ["maybe_value%1#0"]}, "1008": {"op": "btoi", "defined_out": ["tmp%11#0"], "stack_out": ["tmp%11#0"]}, "1009": {"op": "intc_0 // 0", "stack_out": ["tmp%11#0", "0"]}, "1010": {"op": "bytec_2 // \"withdrawn\"", "defined_out": ["\"withdrawn\"", "0", "tmp%11#0"], "stack_out": ["tmp%11#0", "0", "\"withdrawn\""]}, "1011": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%2#0", "maybe_value%2#0", "tmp%11#0"], "stack_out": ["tmp%11#0", "maybe_value%2#0", "maybe_exists%2#0"]}, "1012": {"error": "check self.withdrawn exists", "op": "assert // check self.withdrawn exists", "stack_out": ["tmp%11#0", "maybe_value%2#0"]}, "1013": {"op": "btoi", "defined_out": ["tmp%11#0", "tmp%12#0"], "stack_out": ["tmp%11#0", "tmp%12#0"]}, "1014": {"op": "-", "defined_out": ["available#0"], "stack_out": ["available#0"]}, "1015": {"op": "dup", "defined_out": ["available#0"], "stack_out": ["available#0", "available#0"]}, "1016": {"op": "intc_0 // 0", "stack_out": ["available#0", "available#0", "0"]}, "1017": {"op": "<=", "defined_out": ["available#0", "tmp%13#0"], "stack_out": ["available#0", "tmp%13#0"]}, "1018": {"op": "bz release_funds_after_if_else@4", "stack_out": ["available#0"]}, "1021": {"op": "itob", "defined_out": ["tmp%15#0"], "stack_out": ["tmp%15#0"]}, "1022": {"op": "pushbytes \"Withdraw failed: No funds available\"", "defined_out": ["\"Withdraw failed: No funds available\"", "tmp%15#0"], "stack_out": ["tmp%15#0", "\"Withdraw failed: No funds available\""]}, "1059": {"op": "swap", "stack_out": ["\"Withdraw failed: No funds available\"", "tmp%15#0"]}, "1060": {"op": "concat", "defined_out": ["tmp%16#0"], "stack_out": ["tmp%16#0"]}, "1061": {"op": "log", "stack_out": []}, "1062": {"error": "No funds available", "op": "err // No funds available"}, "1063": {"block": "release_funds_after_if_else@4", "stack_in": ["available#0"], "op": "intc_0 // 0", "defined_out": ["0"], "stack_out": ["available#0", "0"]}, "1064": {"op": "bytec_2 // \"withdrawn\"", "defined_out": ["\"withdrawn\"", "0"], "stack_out": ["available#0", "0", "\"withdrawn\""]}, "1065": {"op": "app_global_get_ex", "defined_out": ["maybe_exists%3#0", "maybe_value%3#0"], "stack_out": ["available#0", "maybe_value%3#0", "maybe_exists%3#0"]}, "1066": {"error": "check self.withdrawn exists", "op": "assert // check self.withdrawn exists", "stack_out": ["available#0", "maybe_value%3#0"]}, "1067": {"op": "btoi", "defined_out": ["tmp%17#0"], "stack_out": ["available#0", "tmp%17#0"]}, "1068": {"op": "dig 1", "defined_out": ["available#0", "available#0 (copy)", "tmp%17#0"], "stack_out": ["available#0", "tmp%17#0", "available#0 (copy)"]}, "1070": {"op": "+", "defined_out": ["available#0", "to_encode%0#0"], "stack_out": ["available#0", "to_encode%0#0"]}, "1071": {"op": "itob", "defined_out": ["available#0", "val_as_bytes%0#0"], "stack_out": ["available#0", "val_as_bytes%0#0"]}, "1072": {"op": "bytec_2 // \"withdrawn\"", "stack_out": ["available#0", "val_as_bytes%0#0", "\"withdrawn\""]}, "1073": {"op": "swap", "stack_out": ["available#0", "\"withdrawn\"", "val_as_bytes%0#0"]}, "1074": {"op": "app_global_put", "stack_out": ["available#0"]}, "1075": {"op": "intc_0 // 0", "stack_out": ["available#0", "0"]}, "1076": {"op": "bytec_2 // \"withdrawn\"", "stack_out": ["available#0", "0", "\"withdrawn\""]}, "1077": {"op": "app_global_get_ex", "defined_out": ["available#0", "maybe_exists%4#0", "maybe_value%4#0"], "stack_out": ["available#0", "maybe_value%4#0", "maybe_exists%4#0"]}, "1078": {"error": "check self.withdrawn exists", "op": "assert // check self.withdrawn exists", "stack_out": ["available#0", "maybe_value%4#0"]}, "1079": {"op": "pushbytes \"Withdrawn updated\"", "defined_out": ["\"Withdrawn updated\"", "available#0", "maybe_value%4#0"], "stack_out": ["available#0", "maybe_value%4#0", "\"Withdrawn updated\""]}, "1098": {"op": "swap", "stack_out": ["available#0", "\"Withdrawn updated\"", "maybe_value%4#0"]}, "1099": {"op": "concat", "defined_out": ["available#0", "tmp%19#0"], "stack_out": ["available#0", "tmp%19#0"]}, "1100": {"op": "log", "stack_out": ["available#0"]}, "1101": {"op": "intc_0 // 0", "stack_out": ["available#0", "0"]}, "1102": {"op": "bytec 5 // \"seller\"", "defined_out": ["\"seller\"", "0", "available#0"], "stack_out": ["available#0", "0", "\"seller\""]}, "1104": {"op": "app_global_get_ex", "defined_out": ["available#0", "maybe_exists%5#0", "maybe_value%5#0"], "stack_out": ["available#0", "maybe_value%5#0", "maybe_exists%5#0"]}, "1105": {"error": "check self.seller exists", "op": "assert // check self.seller exists", "stack_out": ["available#0", "maybe_value%5#0"]}, "1106": {"op": "pushbytes \"Withdraw success\"", "defined_out": ["\"Withdraw success\"", "available#0", "maybe_value%5#0"], "stack_out": ["available#0", "maybe_value%5#0", "\"Withdraw success\""]}, "1124": {"op": "swap", "stack_out": ["available#0", "\"Withdraw success\"", "maybe_value%5#0"]}, "1125": {"op": "concat", "defined_out": ["available#0", "tmp%21#0"], "stack_out": ["available#0", "tmp%21#0"]}, "1126": {"op": "dig 1", "stack_out": ["available#0", "tmp%21#0", "available#0 (copy)"]}, "1128": {"op": "itob", "defined_out": ["available#0", "tmp%21#0", "tmp%23#0"], "stack_out": ["available#0", "tmp%21#0", "tmp%23#0"]}, "1129": {"op": "concat", "defined_out": ["available#0", "tmp%24#0"], "stack_out": ["available#0", "tmp%24#0"]}, "1130": {"op": "log", "stack_out": ["available#0"]}, "1131": {"op": "itxn_begin"}, "1132": {"op": "global MinTxnFee", "defined_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0"]}, "1134": {"op": "intc_0 // 0", "stack_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "0"]}, "1135": {"op": "bytec 5 // \"seller\"", "stack_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "0", "\"seller\""]}, "1137": {"op": "app_global_get_ex", "defined_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_exists%6#0", "maybe_value%6#0"], "stack_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%6#0", "maybe_exists%6#0"]}, "1138": {"error": "check self.seller exists", "op": "assert // check self.seller exists", "stack_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%6#0"]}, "1139": {"op": "frame_dig -1", "defined_out": ["asset#0 (copy)", "available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%6#0"], "stack_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%6#0", "asset#0 (copy)"]}, "1141": {"op": "itxn_field XferAsset", "stack_out": ["available#0", "inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%6#0"]}, "1143": {"op": "uncover 2", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%6#0", "available#0"]}, "1145": {"op": "itxn_field AssetAmount", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "maybe_value%6#0"]}, "1147": {"op": "itxn_field AssetReceiver", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "1149": {"op": "intc_3 // axfer", "defined_out": ["axfer", "inner_txn_params%0%%param_Fee_idx_0#0"], "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0", "axfer"]}, "1150": {"op": "itxn_field TypeEnum", "stack_out": ["inner_txn_params%0%%param_Fee_idx_0#0"]}, "1152": {"op": "itxn_field Fee", "stack_out": []}, "1154": {"op": "itxn_submit"}, "1155": {"retsub": true, "op": "retsub"}, "1156": {"subroutine": "smart_contracts.proof_of_unlock.contract.ProofOfUnlock.get_balance", "params": {"asset#0": "uint64"}, "block": "get_balance", "stack_in": [], "op": "proto 1 1"}, "1159": {"op": "frame_dig -1", "defined_out": ["asset#0 (copy)"], "stack_out": ["asset#0 (copy)"]}, "1161": {"op": "itob", "defined_out": ["tmp%1#0"], "stack_out": ["tmp%1#0"]}, "1162": {"op": "pushbytes \"get_balance called\"", "defined_out": ["\"get_balance called\"", "tmp%1#0"], "stack_out": ["tmp%1#0", "\"get_balance called\""]}, "1182": {"op": "swap", "stack_out": ["\"get_balance called\"", "tmp%1#0"]}, "1183": {"op": "concat", "defined_out": ["tmp%2#0"], "stack_out": ["tmp%2#0"]}, "1184": {"op": "log", "stack_out": []}, "1185": {"op": "global CurrentApplicationAddress", "defined_out": ["app_address#0"], "stack_out": ["app_address#0"]}, "1187": {"op": "frame_dig -1", "stack_out": ["app_address#0", "asset#0 (copy)"]}, "1189": {"op": "asset_holding_get AssetBalance", "defined_out": ["asset_balance#0", "exists#0"], "stack_out": ["asset_balance#0", "exists#0"]}, "1191": {"op": "bz get_balance_ternary_false@2", "stack_out": ["asset_balance#0"]}, "1194": {"op": "frame_dig 0", "stack_out": ["asset_balance#0", "asset_balance#0"]}, "1196": {"op": "itob", "defined_out": ["asset_balance#0", "ternary_result%0#1"], "stack_out": ["asset_balance#0", "ternary_result%0#1"]}, "1197": {"block": "get_balance_ternary_merge@3", "stack_in": ["asset_balance#0", "ternary_result%0#1"], "op": "swap", "defined_out": ["ternary_result%0#1"]}, "1198": {"retsub": true, "op": "retsub"}, "1199": {"block": "get_balance_ternary_false@2", "stack_in": ["asset_balance#0"], "op": "bytec 7 // 0x0000000000000000", "defined_out": ["ternary_result%0#1"], "stack_out": ["asset_balance#0", "ternary_result%0#1"]}, "1201": {"op": "b get_balance_ternary_merge@3"}}}