import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Wallet, ArrowRightLeft, LockKeyhole, Clock, CheckCircle2, AlertTriangle, EyeIcon } from 'lucide-react';
import AppLayout from '../../components/layout/AppLayout';
import { useTransactionStore, Transaction } from '../../stores/transactionStore';
import { useNavigate } from 'react-router-dom';

const Escrow = () => {
  const navigate = useNavigate();
  const { transactions, loading, fetchTransactions } = useTransactionStore();
  const [viewMode, setViewMode] = useState<'locked' | 'released' | 'all'>('all');
  
  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);
  
  // Calculate total funds in escrow
  const totalEscrow = transactions.reduce((sum, tx) => sum + tx.totalAmount - tx.releaseAmount, 0);
  
  // Calculate total funds released
  const totalReleased = transactions.reduce((sum, tx) => sum + tx.releaseAmount, 0);
  
  // Filter transactions based on view mode
  const filteredTransactions = transactions.filter((tx) => {
    if (viewMode === 'locked') {
      return tx.totalAmount > tx.releaseAmount;
    } else if (viewMode === 'released') {
      return tx.releaseAmount > 0;
    }
    return true;
  });
  
  // Navigate to transaction details
  const handleTransactionClick = (transaction: Transaction) => {
    navigate(`/transactions?id=${transaction.id}`);
  };
  
  // Get transaction status indicator
  const getStatusIndicator = (transaction: Transaction) => {
    switch (transaction.status) {
      case 'active':
        return (
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4 text-primary-500" />
            <span className="text-xs font-medium text-primary-700">Active</span>
          </div>
        );
      case 'completed':
        return (
          <div className="flex items-center gap-1">
            <CheckCircle2 className="w-4 h-4 text-success-500" />
            <span className="text-xs font-medium text-success-700">Completed</span>
          </div>
        );
      case 'disputed':
        return (
          <div className="flex items-center gap-1">
            <AlertTriangle className="w-4 h-4 text-error-500" />
            <span className="text-xs font-medium text-error-700">Disputed</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4 text-secondary-500" />
            <span className="text-xs font-medium text-secondary-700">Pending</span>
          </div>
        );
    }
  };
  
  return (
    <AppLayout title="Escrow Management">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <motion.div
          whileHover={{ y: -4 }}
          className="bg-white p-6 rounded-xl shadow-sm"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-neutral-500">Total in Escrow</p>
              <p className="mt-2 text-2xl font-semibold">${totalEscrow.toLocaleString()}</p>
            </div>
            
            <div className="p-3 rounded-lg bg-primary-50 text-primary-600">
              <LockKeyhole className="w-5 h-5" />
            </div>
          </div>
        </motion.div>
        
        <motion.div
          whileHover={{ y: -4 }}
          className="bg-white p-6 rounded-xl shadow-sm"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-neutral-500">Total Released</p>
              <p className="mt-2 text-2xl font-semibold">${totalReleased.toLocaleString()}</p>
            </div>
            
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              <ArrowRightLeft className="w-5 h-5" />
            </div>
          </div>
        </motion.div>
        
        <motion.div
          whileHover={{ y: -4 }}
          className="bg-white p-6 rounded-xl shadow-sm"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-neutral-500">Total Transactions</p>
              <p className="mt-2 text-2xl font-semibold">{transactions.length}</p>
            </div>
            
            <div className="p-3 rounded-lg bg-neutral-100 text-neutral-600">
              <Wallet className="w-5 h-5" />
            </div>
          </div>
        </motion.div>
      </div>
      
      <div className="bg-white p-6 rounded-xl shadow-sm mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <h2 className="text-lg font-semibold text-neutral-900 mb-4 sm:mb-0">Escrow Transactions</h2>
          
          <div className="flex items-center gap-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setViewMode('all')}
              className={`px-3 py-1 text-sm font-medium rounded-lg ${
                viewMode === 'all'
                  ? 'bg-primary-500 text-white'
                  : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
              }`}
            >
              All
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setViewMode('locked')}
              className={`px-3 py-1 text-sm font-medium rounded-lg ${
                viewMode === 'locked'
                  ? 'bg-primary-500 text-white'
                  : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
              }`}
            >
              Locked Funds
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setViewMode('released')}
              className={`px-3 py-1 text-sm font-medium rounded-lg ${
                viewMode === 'released'
                  ? 'bg-primary-500 text-white'
                  : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
              }`}
            >
              Released
            </motion.button>
          </div>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        ) : filteredTransactions.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-neutral-200">
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Product</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Total Amount</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">In Escrow</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Released</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Status</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-neutral-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredTransactions.map((transaction) => (
                  <tr
                    key={transaction.id}
                    className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors"
                  >
                    <td className="py-4 px-4">
                      <div>
                        <p className="font-medium text-neutral-900">{transaction.productName}</p>
                        <p className="text-xs text-neutral-500">{transaction.quantity} units</p>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <p className="font-medium text-neutral-900">${transaction.totalAmount.toLocaleString()}</p>
                    </td>
                    <td className="py-4 px-4">
                      <p className="font-medium text-primary-700">${(transaction.totalAmount - transaction.releaseAmount).toLocaleString()}</p>
                    </td>
                    <td className="py-4 px-4">
                      <p className="font-medium text-success-700">${transaction.releaseAmount.toLocaleString()}</p>
                    </td>
                    <td className="py-4 px-4">
                      {getStatusIndicator(transaction)}
                    </td>
                    <td className="py-4 px-4">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleTransactionClick(transaction)}
                        className="flex items-center gap-1 text-sm font-medium text-primary-600 hover:text-primary-700"
                      >
                        <EyeIcon className="w-4 h-4" />
                        <span>View</span>
                      </motion.button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="py-12 text-center">
            <LockKeyhole className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
            <p className="text-lg font-medium text-neutral-900 mb-2">No transactions found</p>
            <p className="text-neutral-500 max-w-md mx-auto">
              {viewMode === 'locked'
                ? "You don't have any funds locked in escrow at the moment."
                : viewMode === 'released'
                ? "You haven't released any funds yet."
                : "You don't have any transactions in your escrow."}
            </p>
          </div>
        )}
      </div>
      
      <div className="bg-white p-6 rounded-xl shadow-sm">
        <h2 className="text-lg font-semibold text-neutral-900 mb-6">About Smart Contract Escrow</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-neutral-50 p-4 rounded-lg">
            <div className="flex items-start gap-3 mb-3">
              <div className="p-2 rounded-full bg-primary-100 text-primary-700">
                <LockKeyhole className="w-5 h-5" />
              </div>
              <h3 className="text-lg font-medium text-neutral-900">How It Works</h3>
            </div>
            <p className="text-sm text-neutral-600">
              Our smart contract escrow system securely locks your funds in a blockchain contract until all 
              predefined conditions are met. Funds are only released when both parties confirm that 
              milestones have been completed, ensuring safe and transparent transactions.
            </p>
          </div>
          
          <div className="bg-neutral-50 p-4 rounded-lg">
            <div className="flex items-start gap-3 mb-3">
              <div className="p-2 rounded-full bg-success-100 text-success-700">
                <CheckCircle2 className="w-5 h-5" />
              </div>
              <h3 className="text-lg font-medium text-neutral-900">Benefits</h3>
            </div>
            <ul className="text-sm text-neutral-600 space-y-2">
              <li className="flex items-start gap-2">
                <span className="block w-1 h-1 mt-2 rounded-full bg-neutral-400"></span>
                <span>Secure multi-signature transactions</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="block w-1 h-1 mt-2 rounded-full bg-neutral-400"></span>
                <span>Transparent milestone-based releases</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="block w-1 h-1 mt-2 rounded-full bg-neutral-400"></span>
                <span>Immutable blockchain verification</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="block w-1 h-1 mt-2 rounded-full bg-neutral-400"></span>
                <span>Automated dispute resolution process</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default Escrow;