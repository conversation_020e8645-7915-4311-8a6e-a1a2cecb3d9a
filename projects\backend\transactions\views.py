# views.py
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db.models import Sum
from .models import Transaction, MonthlyExpense
from .serializers import (
    TransactionSerializer,
    MonthlyExpenseSerializer,
    YearlyExpenseSerializer,
    MonthlyExpenseSummarySerializer,
)
from decimal import Decimal
import time
from agritram.contract import (
    mint_tokens,
    transfer_tokens,
    burn_tokens,
    convert_ktt_tokens_to_kct,
    convert_kct_tokens_to_ktt,
)
from agritram.algorand_client import (
    app_client,
    ALGORAND_ASSET_ID_KCT,
    ASA_DECIMALS,
    ALGORAND_ASSET_ID_KTT,
)
from django.db import models


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def transaction_list(request):
    if request.method == "GET":
        transactions = Transaction.objects.filter(user=request.user).order_by(
            "-timestamp"
        )
        serializer = TransactionSerializer(transactions, many=True)
        return Response(serializer.data)

    elif request.method == "POST":
        serializer = TransactionSerializer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
@permission_classes(
    [
        IsAuthenticated,
    ]
)
@authentication_classes([SessionAuthentication, TokenAuthentication])
def recent_transactions(request):
    transactions = Transaction.objects.filter(user=request.user).order_by("-timestamp")[
        :10
    ]
    serializer = TransactionSerializer(transactions, many=True)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes(
    [
        IsAuthenticated,
    ]
)
@authentication_classes([SessionAuthentication, TokenAuthentication])
def monthly_expenses(request):
    expenses = MonthlyExpense.objects.filter(user=request.user).order_by(
        "-year", "-month"
    )
    serializer = MonthlyExpenseSerializer(expenses, many=True)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes(
    [
        IsAuthenticated,
    ]
)
@authentication_classes([SessionAuthentication, TokenAuthentication])
def yearly_expense_summary(request):
    year = request.query_params.get("year", timezone.now().year)

    expenses = MonthlyExpense.objects.filter(user=request.user, year=year).order_by(
        "month"
    )

    # Initialize monthly data with zeros
    monthly_data = [0] * 12
    for expense in expenses:
        monthly_data[expense.month - 1] = float(expense.total_amount)

    data = {
        "year": int(year),
        "monthly_data": monthly_data,
        "labels": [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
        ],
    }

    serializer = YearlyExpenseSerializer(data)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes(
    [
        IsAuthenticated,
    ]
)
@authentication_classes([SessionAuthentication, TokenAuthentication])
def current_year_expenses(request):
    current_year = timezone.now().year
    expenses = MonthlyExpense.objects.filter(
        user=request.user, year=current_year
    ).order_by("month")
    serializer = MonthlyExpenseSummarySerializer(expenses, many=True)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes(
    [
        IsAuthenticated,
    ]
)
@authentication_classes([SessionAuthentication, TokenAuthentication])
def expense_statistics(request):
    queryset = MonthlyExpense.objects.filter(user=request.user)

    # Calculate total expenses
    total_expenses = queryset.aggregate(total=Sum("total_amount"))["total"] or 0

    # Get current year expenses
    current_year = timezone.now().year
    current_year_expenses = (
        queryset.filter(year=current_year).aggregate(total=Sum("total_amount"))["total"]
        or 0
    )

    # Get current month expenses
    current_month = timezone.now().month
    current_month_expenses = (
        queryset.filter(year=current_year, month=current_month).aggregate(
            total=Sum("total_amount")
        )["total"]
        or 0
    )

    return Response(
        {
            "total_all_time": total_expenses,
            "total_current_year": current_year_expenses,
            "total_current_month": current_month_expenses,
        }
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def buy_tokens(request):
    """
    Expected request data format:
    {
        "amount": "100.00",
        "payment_method": "CARD" | "MTM",
        "payment_details": {
            // For CARD:
            "card_number": "****************",
            "exp_month": "12",
            "exp_year": "2024",
            "cvc": "123"

            // For MTM:
            "mobile_number": "+254712345678"
        }
    }
    """
    try:
        # Validate request data
        amount = request.data.get("amount")
        payment_method = request.data.get("payment_method")
        payment_details = request.data.get("payment_details", {})

        if not all([amount, payment_method, payment_details]):
            return Response(
                {"error": "Missing required fields"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Validate amount
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        try:
            if amount <= 0:
                raise ValueError
        except (ValueError, TypeError):
            return Response(
                {"error": "Invalid amount"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Validate payment method specific details
        if payment_method == "CARD":
            if "mobile_number" in payment_details:
                return Response(
                    {"error": "Mobile number should not be included for card payments"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            required_fields = ["card_number", "exp_month", "exp_year", "cvc"]
            if not all(field in payment_details for field in required_fields):
                return Response(
                    {"error": "Missing required card details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        elif payment_method == "MTM":
            if any(
                field in payment_details
                for field in ["card_number", "exp_month", "exp_year", "cvc"]
            ):
                return Response(
                    {"error": "Card details should not be included for MTM payments"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if "mobile_number" not in payment_details:
                return Response(
                    {"error": "Mobile number is required for MTM payments"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "Invalid payment method"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Process payment based on method
        if payment_method == "CARD":
            result = process_card_payment(request.user, amount, payment_details)
        else:  # MTM
            result = process_mtm_payment(request.user, amount, payment_details)

        if not result["success"]:
            return Response(
                {"error": result["error"]}, status=status.HTTP_400_BAD_REQUEST
            )

        result1 = app_client.call(
            mint_tokens,
            amount=amount,
            receiver=request.user.account_address,
            token_id=ALGORAND_ASSET_ID_KTT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT],
            accounts=[request.user.account_address],
        )

        if result["success"]:
            # Create transaction record
            transaction = Transaction.objects.create(
                transaction_id=f"transaction_{int(time.time())}",
                algorand_tx_id=result1.tx_id,
                user=request.user,
                from_address=result["from_address"],
                to_address=request.user.account_address,
                amount_usd=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                amount_stablecoin_ktt=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                transaction_type="MINT",
                status="COMPLETED",
                gas_fee=result["gas_fee"],
                payment_method=payment_method,
            )

            serializer = TransactionSerializer(transaction)
            return Response(
                {
                    "message": "Payment successful",
                    "transaction": serializer.data,
                    "additional_info": result.get("message", ""),
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": result["error"]}, status=status.HTTP_400_BAD_REQUEST
            )

    except Exception as e:
        print(e)
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def process_card_payment(user, amount, payment_details):
    """
    Process card payment using Stripe
    """
    return {
        "success": True,
        "transaction_id": f"stripe_{int(time.time())}",
        "from_address": "STRIPE_PAYMENT",
        "gas_fee": Decimal("0.001"),
    }


def process_mtm_payment(user, amount, payment_details):
    """
    Process MTM (Mobile Money Transfer) payment
    """
    return {
        "success": True,
        "transaction_id": f"mtm_{int(time.time())}",
        "from_address": "MTM_PAYMENT",
        "gas_fee": Decimal("0.001"),
        "status": "pending",
        "message": "Please check your phone and complete the payment",
    }


@api_view(["POST", "GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def burn_token(request):
    if request.method == "GET":
        # Get the current user
        user = request.user

        # Filter transactions for the user with transaction type 'BURN'
        burn_transactions = Transaction.objects.filter(
            user=user, transaction_type="BURN"
        )

        # Calculate the total amount_stablecoin_ktt
        total_amount_stablecoin_ktt = (
            burn_transactions.aggregate(total=models.Sum("amount_stablecoin_ktt"))[
                "total"
            ]
            or 0
        )

        # Prepare the response data
        response_data = {
            "total_amount_stablecoin_ktt": str(
                total_amount_stablecoin_ktt
            ),  # Convert to string for JSON serialization
            "transaction_count": burn_transactions.count(),
        }

        return Response(response_data, status=status.HTTP_200_OK)
    else:
        try:
            amount = request.data.get("amount")
            amount = Decimal(amount)
            amount = int(amount * 10 ** int(ASA_DECIMALS))
            try:
                if amount <= 0:
                    raise ValueError
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid amount"}, status=status.HTTP_400_BAD_REQUEST
                )

            result1 = app_client.call(
                burn_tokens,
                amount=amount,
                address=request.user.account_address,
                token_id=ALGORAND_ASSET_ID_KTT,
                foreign_assets=[ALGORAND_ASSET_ID_KTT],
                accounts=[request.user.account_address],
            )

            # Create transaction record
            transaction = Transaction.objects.create(
                transaction_id=f"transaction_{int(time.time())}",
                algorand_tx_id=result1.tx_id,
                user=request.user,
                from_address=request.user.account_address,
                to_address="",
                amount_usd=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                amount_stablecoin_ktt=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                transaction_type="BURN",
                status="COMPLETED",
                gas_fee="0.001",
                payment_method="",
            )

            serializer = TransactionSerializer(transaction)
            return Response(
                {
                    "message": "Payment successful",
                    "transaction": serializer.data,
                    "additional_info": "",
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            print(e)
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def convert_ktt_to_kct(request):
    try:
        amount = request.data.get("amount")
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        result = app_client.call(
            convert_ktt_tokens_to_kct,
            amount=amount,
            address=request.user.account_address,
            token_ktt_id=ALGORAND_ASSET_ID_KTT,
            token_kct_id=ALGORAND_ASSET_ID_KCT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT, ALGORAND_ASSET_ID_KCT],
            accounts=[request.user.account_address],
        )
        # Create transaction record
        transaction = Transaction.objects.create(
            transaction_id=f"transaction_{int(time.time())}",
            algorand_tx_id=result.tx_id,
            user=request.user,
            from_address=request.user.account_address,
            to_address=request.user.account_address,
            amount_usd=0,
            amount_stablecoin_ktt=-Decimal(amount / 10 ** int(ASA_DECIMALS)),
            amount_stablecoin_kct=Decimal(amount / 10 ** int(ASA_DECIMALS)),
            transaction_type="CONVERT_KTT_TO_KCT",
            status="COMPLETED",
            gas_fee="0.001",
            payment_method="",
        )

        serializer = TransactionSerializer(transaction)
        return Response(
            {
                "message": "Conversion successful",
                "transaction": serializer.data,
                "additional_info": "",
            },
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def convert_kct_to_ktt(request):
    try:
        amount = request.data.get("amount")
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        result = app_client.call(
            convert_kct_tokens_to_ktt,
            amount=amount,
            address=request.user.account_address,
            token_ktt_id=ALGORAND_ASSET_ID_KTT,
            token_kct_id=ALGORAND_ASSET_ID_KCT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT, ALGORAND_ASSET_ID_KCT],
            accounts=[request.user.account_address],
        )
        # Create transaction record
        transaction = Transaction.objects.create(
            transaction_id=f"transaction_{int(time.time())}",
            algorand_tx_id=result.tx_id,
            user=request.user,
            from_address=request.user.account_address,
            to_address=request.user.account_address,
            amount_usd=0,
            amount_stablecoin_ktt=-Decimal(amount / 10 ** int(ASA_DECIMALS)),
            amount_stablecoin_kct=Decimal(amount / 10 ** int(ASA_DECIMALS)),
            transaction_type="CONVERT_KCT_TO_KTT",
            status="COMPLETED",
            gas_fee="0.001",
            payment_method="",
        )

        serializer = TransactionSerializer(transaction)
        return Response(
            {
                "message": "Sell successful",
                "transaction": serializer.data,
                "additional_info": "",
            },
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_all_transactions(request):
    try:
        # Get sent transactions
        sent_transactions = Transaction.objects.filter(user=request.user)
        sent_serializer = TransactionSerializer(sent_transactions, many=True)

        # Convert serializer.data to list
        all_transactions = list(sent_serializer.data)

        # Get received transactions
        sql_query = """
            SELECT * FROM transactions_transaction
            WHERE to_address = %s AND transaction_type='TRANSFER'
        """
        received_transactions = Transaction.objects.raw(
            sql_query, [request.user.account_address]
        )

        # Add received transactions to the list
        for transaction in received_transactions:
            transaction_data = TransactionSerializer(transaction).data
            transaction_data["transaction_type"] = "RECEIVE"
            transaction_data["amount_stablecoin_ktt"] = -Decimal(
                transaction_data["amount_stablecoin_ktt"]
            )
            all_transactions.append(transaction_data)

        return Response(
            {"status": "success", "data": all_transactions}, status=status.HTTP_200_OK
        )
    except Exception as e:
        print(e)
        return Response(
            {"status": "error", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def transfer_ktt(request):
    try:
        amount = request.data.get("amount")
        reciver_address = str(request.data.get("reciver"))
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        result = app_client.call(
            transfer_tokens,
            amount=amount,
            receiver=reciver_address,
            account=request.user.account_address,
            token_id=ALGORAND_ASSET_ID_KTT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT],
            accounts=[request.user.account_address, reciver_address],
        )
        # Create transaction record
        transaction = Transaction.objects.create(
            transaction_id=f"transaction_{int(time.time())}",
            algorand_tx_id=result.tx_id,
            user=request.user,
            from_address=request.user.account_address,
            to_address=reciver_address,
            amount_usd=0,
            amount_stablecoin_ktt=-Decimal(amount / 10 ** int(ASA_DECIMALS)),
            amount_stablecoin_kct=0,
            transaction_type="TRANSFER",
            status="COMPLETED",
            gas_fee="0.001",
            payment_method="",
        )

        serializer = TransactionSerializer(transaction)
        return Response(
            {
                "message": "Transfer successful",
                "transaction": serializer.data,
                "additional_info": "",
            },
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        print(e)
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_monthly_expenses(request):
    monthly = MonthlyExpense.objects.filter(user=request.user, transaction_type="MINT")
    serializer = MonthlyExpenseSerializer(monthly, many=True)

    if not monthly.exists():
        return Response({"message": "No monthly expenses found."}, status=404)

    return Response(serializer.data)
