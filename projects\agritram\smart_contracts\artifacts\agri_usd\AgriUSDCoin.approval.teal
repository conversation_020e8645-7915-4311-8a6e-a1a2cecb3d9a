#pragma version 10
#pragma typetrack false

// smart_contracts.agri_usd.contract.AgriUSDCoin.__algopy_entrypoint_with_init() -> uint64:
main:
    intcblock 0 1 4
    bytecblock "asset" 0x151f7c75 "minted_tokens" "burnt_tokens"
    txn ApplicationID
    bnz main_after_if_else@2
    // smart_contracts/agri_usd/contract.py:10
    // self.minted_tokens = UInt64(0)
    bytec_2 // "minted_tokens"
    intc_0 // 0
    app_global_put
    // smart_contracts/agri_usd/contract.py:11
    // self.burnt_tokens = UInt64(0)
    bytec_3 // "burnt_tokens"
    intc_0 // 0
    app_global_put
    // smart_contracts/agri_usd/contract.py:12
    // self.asset = Asset(0)
    bytec_0 // "asset"
    intc_0 // 0
    app_global_put

main_after_if_else@2:
    // smart_contracts/agri_usd/contract.py:4
    // class AgriUSDCoin(ARC4Contract):
    txn NumAppArgs
    bz main_bare_routing@12
    pushbytess 0x8213ade6 0x5ba22a84 0x528f9dcb 0x9a988f95 0x7aca8dfa 0x2d9a0de7 0xc372f979 // method "create()byte[]", method "get_asset_id()uint64", method "get_minted_tokens()uint64", method "get_burnt_tokens()uint64", method "mint_tokens(uint64,account)byte[]", method "burn_tokens(uint64,account)byte[]", method "transfer_tokens(uint64,account,account)byte[]"
    txna ApplicationArgs 0
    match main_create_route@5 main_get_asset_id_route@6 main_get_minted_tokens_route@7 main_get_burnt_tokens_route@8 main_mint_tokens_route@9 main_burn_tokens_route@10 main_transfer_tokens_route@11

main_after_if_else@14:
    // smart_contracts/agri_usd/contract.py:4
    // class AgriUSDCoin(ARC4Contract):
    intc_0 // 0
    return

main_transfer_tokens_route@11:
    // smart_contracts/agri_usd/contract.py:80
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/agri_usd/contract.py:4
    // class AgriUSDCoin(ARC4Contract):
    txna ApplicationArgs 1
    txna ApplicationArgs 2
    btoi
    txnas Accounts
    txna ApplicationArgs 3
    btoi
    txnas Accounts
    // smart_contracts/agri_usd/contract.py:80
    // @arc4.abimethod
    callsub transfer_tokens
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_burn_tokens_route@10:
    // smart_contracts/agri_usd/contract.py:63
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/agri_usd/contract.py:4
    // class AgriUSDCoin(ARC4Contract):
    txna ApplicationArgs 1
    txna ApplicationArgs 2
    btoi
    txnas Accounts
    // smart_contracts/agri_usd/contract.py:63
    // @arc4.abimethod
    callsub burn_tokens
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_mint_tokens_route@9:
    // smart_contracts/agri_usd/contract.py:47
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    // smart_contracts/agri_usd/contract.py:4
    // class AgriUSDCoin(ARC4Contract):
    txna ApplicationArgs 1
    txna ApplicationArgs 2
    btoi
    txnas Accounts
    // smart_contracts/agri_usd/contract.py:47
    // @arc4.abimethod
    callsub mint_tokens
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_get_burnt_tokens_route@8:
    // smart_contracts/agri_usd/contract.py:44
    // @arc4.abimethod(readonly=True)
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    callsub get_burnt_tokens
    itob
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_get_minted_tokens_route@7:
    // smart_contracts/agri_usd/contract.py:40
    // @arc4.abimethod(readonly=True)
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    callsub get_minted_tokens
    itob
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_get_asset_id_route@6:
    // smart_contracts/agri_usd/contract.py:37
    // @arc4.abimethod(readonly=True)
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    callsub get_asset_id
    itob
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_create_route@5:
    // smart_contracts/agri_usd/contract.py:14
    // @arc4.abimethod
    txn OnCompletion
    !
    assert // OnCompletion is not NoOp
    txn ApplicationID
    assert // can only call when not creating
    callsub create
    dup
    len
    itob
    extract 6 2
    swap
    concat
    bytec_1 // 0x151f7c75
    swap
    concat
    log
    intc_1 // 1
    return

main_bare_routing@12:
    // smart_contracts/agri_usd/contract.py:4
    // class AgriUSDCoin(ARC4Contract):
    txn OnCompletion
    bnz main_after_if_else@14
    txn ApplicationID
    !
    assert // can only call when creating
    intc_1 // 1
    return


// smart_contracts.agri_usd.contract.AgriUSDCoin.create() -> bytes:
create:
    // smart_contracts/agri_usd/contract.py:17-18
    // # Create ASA once at deployment
    // assert Txn.sender == Global.creator_address, "Only the creator can create the ASA"
    txn Sender
    global CreatorAddress
    ==
    assert // Only the creator can create the ASA
    // smart_contracts/agri_usd/contract.py:19
    // assert self.asset.id == 0, "ASA already created"
    intc_0 // 0
    bytec_0 // "asset"
    app_global_get_ex
    assert // check self.asset exists
    !
    assert // ASA already created
    // smart_contracts/agri_usd/contract.py:20-31
    // # Create the ASA with maximum supply and 2 decimals
    // asset_result = itxn.AssetConfig(
    //     total=18446744073709551615,
    //     decimals=2,
    //     asset_name="Agri USD Coin",
    //     unit_name="AGRI-USD",
    //     manager=Global.current_application_address,
    //     reserve=Global.current_application_address,
    //     freeze=Global.current_application_address,
    //     clawback=Global.current_application_address,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/agri_usd/contract.py:30
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/agri_usd/contract.py:26
    // manager=Global.current_application_address,
    global CurrentApplicationAddress
    // smart_contracts/agri_usd/contract.py:27-29
    // reserve=Global.current_application_address,
    // freeze=Global.current_application_address,
    // clawback=Global.current_application_address,
    dupn 3
    itxn_field ConfigAssetClawback
    itxn_field ConfigAssetFreeze
    itxn_field ConfigAssetReserve
    itxn_field ConfigAssetManager
    // smart_contracts/agri_usd/contract.py:25
    // unit_name="AGRI-USD",
    pushbytes "AGRI-USD"
    itxn_field ConfigAssetUnitName
    // smart_contracts/agri_usd/contract.py:24
    // asset_name="Agri USD Coin",
    pushbytes "Agri USD Coin"
    itxn_field ConfigAssetName
    // smart_contracts/agri_usd/contract.py:23
    // decimals=2,
    pushint 2 // 2
    itxn_field ConfigAssetDecimals
    // smart_contracts/agri_usd/contract.py:22
    // total=18446744073709551615,
    pushint 18446744073709551615 // 18446744073709551615
    itxn_field ConfigAssetTotal
    // smart_contracts/agri_usd/contract.py:20-21
    // # Create the ASA with maximum supply and 2 decimals
    // asset_result = itxn.AssetConfig(
    pushint 3 // acfg
    itxn_field TypeEnum
    itxn_field Fee
    // smart_contracts/agri_usd/contract.py:20-31
    // # Create the ASA with maximum supply and 2 decimals
    // asset_result = itxn.AssetConfig(
    //     total=18446744073709551615,
    //     decimals=2,
    //     asset_name="Agri USD Coin",
    //     unit_name="AGRI-USD",
    //     manager=Global.current_application_address,
    //     reserve=Global.current_application_address,
    //     freeze=Global.current_application_address,
    //     clawback=Global.current_application_address,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    itxn TxID
    // smart_contracts/agri_usd/contract.py:33-34
    // # Save ASA ID into contract storage
    // self.asset = Asset(asset_result.created_asset.id)
    bytec_0 // "asset"
    itxn CreatedAssetID
    app_global_put
    // smart_contracts/agri_usd/contract.py:35
    // return asset_result.txn_id
    retsub


// smart_contracts.agri_usd.contract.AgriUSDCoin.get_asset_id() -> uint64:
get_asset_id:
    // smart_contracts/agri_usd/contract.py:39
    // return self.asset.id
    intc_0 // 0
    bytec_0 // "asset"
    app_global_get_ex
    assert // check self.asset exists
    retsub


// smart_contracts.agri_usd.contract.AgriUSDCoin.get_minted_tokens() -> uint64:
get_minted_tokens:
    // smart_contracts/agri_usd/contract.py:42
    // return self.minted_tokens
    intc_0 // 0
    bytec_2 // "minted_tokens"
    app_global_get_ex
    assert // check self.minted_tokens exists
    retsub


// smart_contracts.agri_usd.contract.AgriUSDCoin.get_burnt_tokens() -> uint64:
get_burnt_tokens:
    // smart_contracts/agri_usd/contract.py:46
    // return self.burnt_tokens
    intc_0 // 0
    bytec_3 // "burnt_tokens"
    app_global_get_ex
    assert // check self.burnt_tokens exists
    retsub


// smart_contracts.agri_usd.contract.AgriUSDCoin.mint_tokens(amount: bytes, receiver: bytes) -> bytes:
mint_tokens:
    // smart_contracts/agri_usd/contract.py:47-52
    // @arc4.abimethod
    // def mint_tokens(
    //     self,
    //     amount: arc4.UInt64,
    //     receiver: Account,
    // ) -> Bytes:
    proto 2 1
    // smart_contracts/agri_usd/contract.py:53-58
    // itxn_result = itxn.AssetTransfer(
    //     asset_receiver=receiver,
    //     xfer_asset=self.asset.id,
    //     asset_amount=amount.native,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/agri_usd/contract.py:57
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/agri_usd/contract.py:55
    // xfer_asset=self.asset.id,
    intc_0 // 0
    bytec_0 // "asset"
    app_global_get_ex
    assert // check self.asset exists
    // smart_contracts/agri_usd/contract.py:56
    // asset_amount=amount.native,
    frame_dig -2
    btoi
    dup
    itxn_field AssetAmount
    swap
    itxn_field XferAsset
    frame_dig -1
    itxn_field AssetReceiver
    // smart_contracts/agri_usd/contract.py:53
    // itxn_result = itxn.AssetTransfer(
    intc_2 // axfer
    itxn_field TypeEnum
    swap
    itxn_field Fee
    // smart_contracts/agri_usd/contract.py:53-58
    // itxn_result = itxn.AssetTransfer(
    //     asset_receiver=receiver,
    //     xfer_asset=self.asset.id,
    //     asset_amount=amount.native,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    itxn TxID
    // smart_contracts/agri_usd/contract.py:60
    // self.minted_tokens += amount.native
    intc_0 // 0
    bytec_2 // "minted_tokens"
    app_global_get_ex
    assert // check self.minted_tokens exists
    uncover 2
    +
    bytec_2 // "minted_tokens"
    swap
    app_global_put
    // smart_contracts/agri_usd/contract.py:61
    // return itxn_result.txn_id
    retsub


// smart_contracts.agri_usd.contract.AgriUSDCoin.burn_tokens(amount: bytes, address: bytes) -> bytes:
burn_tokens:
    // smart_contracts/agri_usd/contract.py:63-68
    // @arc4.abimethod
    // def burn_tokens(
    //     self,
    //     amount: arc4.UInt64,
    //     address: Account,
    // ) -> Bytes:
    proto 2 1
    // smart_contracts/agri_usd/contract.py:69-75
    // itxn_result = itxn.AssetTransfer(
    //     asset_receiver=Global.current_application_address,
    //     xfer_asset=self.asset.id,
    //     asset_amount=amount.native,
    //     asset_sender=address,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/agri_usd/contract.py:74
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/agri_usd/contract.py:70
    // asset_receiver=Global.current_application_address,
    global CurrentApplicationAddress
    // smart_contracts/agri_usd/contract.py:71
    // xfer_asset=self.asset.id,
    intc_0 // 0
    bytec_0 // "asset"
    app_global_get_ex
    assert // check self.asset exists
    // smart_contracts/agri_usd/contract.py:72
    // asset_amount=amount.native,
    frame_dig -2
    btoi
    frame_dig -1
    itxn_field AssetSender
    dup
    itxn_field AssetAmount
    swap
    itxn_field XferAsset
    swap
    itxn_field AssetReceiver
    // smart_contracts/agri_usd/contract.py:69
    // itxn_result = itxn.AssetTransfer(
    intc_2 // axfer
    itxn_field TypeEnum
    swap
    itxn_field Fee
    // smart_contracts/agri_usd/contract.py:69-75
    // itxn_result = itxn.AssetTransfer(
    //     asset_receiver=Global.current_application_address,
    //     xfer_asset=self.asset.id,
    //     asset_amount=amount.native,
    //     asset_sender=address,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    itxn TxID
    // smart_contracts/agri_usd/contract.py:77
    // self.burnt_tokens += amount.native
    intc_0 // 0
    bytec_3 // "burnt_tokens"
    app_global_get_ex
    assert // check self.burnt_tokens exists
    uncover 2
    +
    bytec_3 // "burnt_tokens"
    swap
    app_global_put
    // smart_contracts/agri_usd/contract.py:78
    // return itxn_result.txn_id
    retsub


// smart_contracts.agri_usd.contract.AgriUSDCoin.transfer_tokens(amount: bytes, receiver: bytes, account: bytes) -> bytes:
transfer_tokens:
    // smart_contracts/agri_usd/contract.py:80-86
    // @arc4.abimethod
    // def transfer_tokens(
    //     self,
    //     amount: arc4.UInt64,
    //     receiver: Account,
    //     account: Account,
    // ) -> Bytes:
    proto 3 1
    // smart_contracts/agri_usd/contract.py:88-94
    // itxn_result = itxn.AssetTransfer(
    //         asset_receiver=receiver,
    //     xfer_asset=self.asset.id,
    //     asset_amount=amount.native,
    //     asset_sender=account,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_begin
    // smart_contracts/agri_usd/contract.py:93
    // fee=Global.min_txn_fee,
    global MinTxnFee
    // smart_contracts/agri_usd/contract.py:90
    // xfer_asset=self.asset.id,
    intc_0 // 0
    bytec_0 // "asset"
    app_global_get_ex
    assert // check self.asset exists
    // smart_contracts/agri_usd/contract.py:91
    // asset_amount=amount.native,
    frame_dig -3
    btoi
    frame_dig -1
    itxn_field AssetSender
    dup
    itxn_field AssetAmount
    swap
    itxn_field XferAsset
    frame_dig -2
    itxn_field AssetReceiver
    // smart_contracts/agri_usd/contract.py:88
    // itxn_result = itxn.AssetTransfer(
    intc_2 // axfer
    itxn_field TypeEnum
    swap
    itxn_field Fee
    // smart_contracts/agri_usd/contract.py:88-94
    // itxn_result = itxn.AssetTransfer(
    //         asset_receiver=receiver,
    //     xfer_asset=self.asset.id,
    //     asset_amount=amount.native,
    //     asset_sender=account,
    //     fee=Global.min_txn_fee,
    // ).submit()
    itxn_submit
    itxn TxID
    // smart_contracts/agri_usd/contract.py:96
    // self.minted_tokens += amount.native
    intc_0 // 0
    bytec_2 // "minted_tokens"
    app_global_get_ex
    assert // check self.minted_tokens exists
    uncover 2
    +
    bytec_2 // "minted_tokens"
    swap
    app_global_put
    // smart_contracts/agri_usd/contract.py:97
    // return itxn_result.txn_id
    retsub
