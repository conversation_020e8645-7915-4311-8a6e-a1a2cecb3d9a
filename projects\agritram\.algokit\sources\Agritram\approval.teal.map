{"version": 3, "sources": ["approval.teal"], "mappings": ";AAOI;;AACA;;;AACA;;;;;;AACA;;;AACA;;;;AAKA;;AACA;AAKA;;AACA;AACA;AACA;;AACA;AAGA;;;AACA;;;AAGA;;;AACA;AACA;AACA;AACA;;;AACA;AACA;AACA;;;;;;AACA;AACA;AACA;AACA;;AACA;AAKA;;AACA;;;AACA;;AACA;AACA;AACA;;AACA;AAQA;;;AAGA;;;;;;;;;AACA;;AACA;AACA", "pc_to_line": {"0": 0, "1": 7, "2": 7, "3": 8, "4": 8, "5": 8, "6": 9, "7": 9, "8": 9, "9": 9, "10": 9, "11": 9, "12": 10, "13": 10, "14": 10, "15": 11, "16": 11, "17": 11, "18": 11, "19": 16, "20": 16, "21": 17, "22": 22, "23": 22, "24": 23, "25": 24, "26": 25, "27": 25, "28": 26, "29": 29, "30": 29, "31": 29, "32": 30, "33": 30, "34": 30, "35": 33, "36": 33, "37": 33, "38": 34, "39": 35, "40": 36, "41": 37, "42": 37, "43": 37, "44": 38, "45": 39, "46": 40, "47": 40, "48": 40, "49": 40, "50": 40, "51": 40, "52": 41, "53": 42, "54": 43, "55": 44, "56": 44, "57": 45, "58": 50, "59": 50, "60": 51, "61": 51, "62": 51, "63": 52, "64": 52, "65": 53, "66": 54, "67": 55, "68": 55, "69": 56, "70": 64, "71": 64, "72": 64, "73": 67, "74": 67, "75": 67, "76": 67, "77": 67, "78": 67, "79": 67, "80": 67, "81": 67, "82": 68, "83": 68, "84": 69, "85": 70}, "line_to_pc": {"0": [0], "7": [1, 2], "8": [3, 4, 5], "9": [6, 7, 8, 9, 10, 11], "10": [12, 13, 14], "11": [15, 16, 17, 18], "16": [19, 20], "17": [21], "22": [22, 23], "23": [24], "24": [25], "25": [26, 27], "26": [28], "29": [29, 30, 31], "30": [32, 33, 34], "33": [35, 36, 37], "34": [38], "35": [39], "36": [40], "37": [41, 42, 43], "38": [44], "39": [45], "40": [46, 47, 48, 49, 50, 51], "41": [52], "42": [53], "43": [54], "44": [55, 56], "45": [57], "50": [58, 59], "51": [60, 61, 62], "52": [63, 64], "53": [65], "54": [66], "55": [67, 68], "56": [69], "64": [70, 71, 72], "67": [73, 74, 75, 76, 77, 78, 79, 80, 81], "68": [82, 83], "69": [84], "70": [85]}}